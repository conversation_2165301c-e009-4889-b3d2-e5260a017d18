{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-RelWithDebInfo-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": "."}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0], "name": "Project"}], "targets": []}], "kind": "codemodel", "paths": {"build": "D:/projects/job/hodan appointment/hodan_hospital/android/app/.cxx/RelWithDebInfo/5b3d683k/x86", "source": "C:/Users/<USER>/fvm/versions/stable/packages/flutter_tools/gradle/src/main/groovy"}, "version": {"major": 2, "minor": 3}}