{"buildFiles": ["C:\\Users\\<USER>\\fvm\\versions\\stable\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\projects\\job\\hodan appointment\\hodan_hospital\\android\\app\\.cxx\\RelWithDebInfo\\5b3d683k\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\projects\\job\\hodan appointment\\hodan_hospital\\android\\app\\.cxx\\RelWithDebInfo\\5b3d683k\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}