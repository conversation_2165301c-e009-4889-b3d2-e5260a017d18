{"buildFiles": ["C:\\Users\\<USER>\\shorebird-main\\bin\\cache\\flutter\\a5bd04b23bd448f5c825a20ddb391110ea15d3f1\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\projects\\job\\hodan appointment\\hodan_hospital\\android\\app\\.cxx\\RelWithDebInfo\\4x583f1w\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\projects\\job\\hodan appointment\\hodan_hospital\\android\\app\\.cxx\\RelWithDebInfo\\4x583f1w\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}