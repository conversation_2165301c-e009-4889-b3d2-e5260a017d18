{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": "."}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "Project"}], "targets": []}], "kind": "codemodel", "paths": {"build": "D:/projects/job/hodan appointment/hodan_hospital/android/app/.cxx/Debug/4x3p6s1z/x86_64", "source": "C:/Users/<USER>/fvm/versions/stable/packages/flutter_tools/gradle/src/main/groovy"}, "version": {"major": 2, "minor": 3}}