{"buildFiles": ["C:\\Users\\<USER>\\fvm\\versions\\stable\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\projects\\job\\hodan appointment\\hodan_hospital\\android\\app\\.cxx\\Debug\\4x3p6s1z\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\projects\\job\\hodan appointment\\hodan_hospital\\android\\app\\.cxx\\Debug\\4x3p6s1z\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}