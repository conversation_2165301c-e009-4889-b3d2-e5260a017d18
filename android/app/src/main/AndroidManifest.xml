    <manifest xmlns:android="http://schemas.android.com/apk/res/android"
        package="com.rasiin.hodan_hospital">

        <!-- 🔹 Required Internet & Network Permissions -->
        <uses-permission android:name="android.permission.INTERNET"/>
        <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
        <uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>

        <!-- 🔹 Camera & Image Picker Permissions -->
        <uses-permission android:name="android.permission.CAMERA"/>
        <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
        <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>

        <!-- 🔹 Android 13+ (API 33+) Photo Permissions -->
        <uses-permission android:name="android.permission.READ_MEDIA_IMAGES"/>

        <!-- 🔹 Optional: Declare camera feature (not required if camera is optional) -->
        <uses-feature android:name="android.hardware.camera" android:required="false"/>
        <uses-feature android:name="android.hardware.camera.autofocus" android:required="false"/>

        <application
            android:label="@string/app_name"
            android:name="${applicationName}"
            android:icon="@mipmap/launcher_icon"
            android:allowBackup="true"
            android:fullBackupContent="true"
            android:usesCleartextTraffic="true"
            android:requestLegacyExternalStorage="true"
            android:supportsRtl="true"
            android:enableOnBackInvokedCallback="true">

            <activity
                android:name=".MainActivity"
                android:exported="true"
                android:launchMode="singleTop"
                android:taskAffinity=""
                android:theme="@style/LaunchTheme"
                android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
                android:hardwareAccelerated="true"
                android:windowSoftInputMode="adjustResize">

                <meta-data
                    android:name="io.flutter.embedding.android.NormalTheme"
                    android:resource="@style/NormalTheme"/>

                <intent-filter>
                    <action android:name="android.intent.action.MAIN"/>
                    <category android:name="android.intent.category.LAUNCHER"/>
                </intent-filter>
            </activity>

            <!-- Required for Flutter Plugins -->
            <meta-data
                android:name="flutterEmbedding"
                android:value="2"/>

        </application>

        <!-- 🔹 Query Required Packages -->
        <queries>
            <!-- Allow intent to launch any browser / website -->
            <intent>
                <action android:name="android.intent.action.VIEW" />
                <data android:scheme="http" />
            </intent>
            <intent>
                <action android:name="android.intent.action.VIEW" />
                <data android:scheme="https" />
            </intent>

            <!-- WhatsApp -->
            <package android:name="com.whatsapp" />

            <!-- Gmail or email clients -->
            <intent>
                <action android:name="android.intent.action.SENDTO" />
                <data android:scheme="mailto" />
            </intent>

            <!-- Phone dialer -->
            <intent>
                <action android:name="android.intent.action.VIEW" />
                <data android:scheme="tel" />
            </intent>

            <!-- SMS support (optional) -->
            <!-- <intent>
                <action android:name="android.intent.action.VIEW" />
                <data android:scheme="sms" />
            </intent> -->

            <!-- Google Maps -->
            <intent>
                <action android:name="android.intent.action.VIEW" />
                <data android:scheme="geo" />
            </intent>

            <!-- Chrome Custom Tabs support -->
            <intent>
                <action android:name="android.support.customtabs.action.CustomTabsService" />
            </intent>
        </queries>



    </manifest>
