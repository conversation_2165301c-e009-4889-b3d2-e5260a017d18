{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98684673020079b82c820d08873803d0bd", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98519c0f1aabf34eacbf6f755b813f1496", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9894d2b2c4341da6da96f0ffc20bcb4e68", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ec62137b0ee28ca4265944856877be27", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9894d2b2c4341da6da96f0ffc20bcb4e68", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9832ebf16d88dad2729e444c32094aa46a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9856edc97a3c411921d40004865a7dce9b", "guid": "bfdfe7dc352907fc980b868725387e982ef32830090a5f180689e16980928fd2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcc280d0f67c66a50ea6e193ac814d23", "guid": "bfdfe7dc352907fc980b868725387e985a916ced4ab8bf3396d2522efbf655a2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e84c48797f1094b43ff8ec43e477f80", "guid": "bfdfe7dc352907fc980b868725387e98ff53f399fa1e2ecb7849daf4286e9ade", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f9e3ea976f705e6496990d722d94703", "guid": "bfdfe7dc352907fc980b868725387e988ad8e0322ec48311f0d9e5a75f54c724", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898d9ed93c9ea0aaa3936c02d4a26b7e9", "guid": "bfdfe7dc352907fc980b868725387e98b11ed937bba18c7408a49a9a028bb33f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98552dc9058d0a9467e671070b2a05bb08", "guid": "bfdfe7dc352907fc980b868725387e983d80b84cb41fbc5fdcc8bc3d451f292d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98996edc6236c61b3920c3857a5439ed6f", "guid": "bfdfe7dc352907fc980b868725387e983bf5db94459714fd2562bfa5ba3794e6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ae3a905920b321244ddc3019528efb5", "guid": "bfdfe7dc352907fc980b868725387e98b4cf2c1398499b177aa5f12416a2cefc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b057716210db9ea70515730bf0480f2", "guid": "bfdfe7dc352907fc980b868725387e98a5931e111640051bba64bb234ef28a63", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812aeae64772e3b84b742e1236747da50", "guid": "bfdfe7dc352907fc980b868725387e984e2d3cd6dd0a8b8ce5a9345bf5eed7de", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983628645106bb43524f6fdad0d7462aed", "guid": "bfdfe7dc352907fc980b868725387e984bcefc59b1341fdd0e8bad71dba39c92", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981348669614a667863e5532f27dd5f7e6", "guid": "bfdfe7dc352907fc980b868725387e98c7eb3ea66f1fa44a1f5858283c92137d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988bbc30df95e4c57a3be996831e28747a", "guid": "bfdfe7dc352907fc980b868725387e98dc02dbc796bf1a1c79ffc8c7bf3cea72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837e7505904760df9c2e7a36f0144438c", "guid": "bfdfe7dc352907fc980b868725387e98cdc5844f930b9d0fd5308dd5856a500e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98229dcc6cfbf523407dcba109cef2275f", "guid": "bfdfe7dc352907fc980b868725387e988f5bb97be79cc2d8abff7c92bd4b7567", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec4b05de00de043fa05f9fdf156a1f73", "guid": "bfdfe7dc352907fc980b868725387e983de4f1e8f5a97a6028a6bb34227f49d6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ae648ea04714ba5ef415f995136bd18", "guid": "bfdfe7dc352907fc980b868725387e98f2f63511bf606bb48ce3eb1b71b306f4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983029af32d26f00acbe7271ebab7b8b57", "guid": "bfdfe7dc352907fc980b868725387e98327b38ee62f98da7d09431ea5004a053", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a6f3b38b7b0f9224b7f801cecfafba6", "guid": "bfdfe7dc352907fc980b868725387e980188baa19b7914aff2e34c8da54773e1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b8a23a1f84027bbf47329a06c17f754", "guid": "bfdfe7dc352907fc980b868725387e9812d25da0977d54b4f93c43814429ebf2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5fd3f7558fe62399a55274f9dc8ecce", "guid": "bfdfe7dc352907fc980b868725387e98b9ff284581573c4ba830aef19b6cb271", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d25219eaafbc3f56b7038a157be9f98", "guid": "bfdfe7dc352907fc980b868725387e98938189fe00ada6ed049fb839ef6bf7d6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1d8d32b2d1d532f1504bd0e50df87c5", "guid": "bfdfe7dc352907fc980b868725387e9864333a3fadcb2636b3f7fea467c141f6", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986a03fa67e33d48dce94be2b8eb2259ec", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98dd7c2bcc3ee8b27db6a811234758b435", "guid": "bfdfe7dc352907fc980b868725387e982a61503d8185fa65d0d61935791efb04"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983cc0d6b0e5e536a1501612ad6824949e", "guid": "bfdfe7dc352907fc980b868725387e9835220589345efb92af42631dbf6b9459"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985afd5242a94fe19d6ad2a4a9903094d9", "guid": "bfdfe7dc352907fc980b868725387e98c4ed4b65682f6200b53ab06f33e7c896"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2fee66d8a21f69bb7b7b61025253635", "guid": "bfdfe7dc352907fc980b868725387e98f67d51cdaaf5e1f78be421f374efde62"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987cc1a3b913c748f9ace05e3dda1f709a", "guid": "bfdfe7dc352907fc980b868725387e98aa97ddbe032d174d7e0e5ddbc64b9d5c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809e40eb15a0ca2ffac4203126d50daa0", "guid": "bfdfe7dc352907fc980b868725387e98ff8928328841298b0ed62804c598770b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986df279102acb86d0e18595df1894ec9b", "guid": "bfdfe7dc352907fc980b868725387e98ef84c0ffd43efae0c864668786839def"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c35f6174e28835d42521a2efe0e37573", "guid": "bfdfe7dc352907fc980b868725387e9848cadd1ba7bb24a85b533aabf8e22799"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7dea848108c5e43caf53583ff9e757e", "guid": "bfdfe7dc352907fc980b868725387e9821497750e138db752421f5224d128654"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894ddf4f5c8f2f3924b8f38064870038f", "guid": "bfdfe7dc352907fc980b868725387e984a4a9db5329a7711e351ab57794c53a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e976ffc261cf9aa352f0058807adfe0", "guid": "bfdfe7dc352907fc980b868725387e9833885db617a4d69d1571730987fbd3fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f475df7e4bb796a74413d3247c8f7703", "guid": "bfdfe7dc352907fc980b868725387e9824cbbfe0ddc5f68e6717f61ca670184f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98797cbb667a9041affb9c1dc694db2188", "guid": "bfdfe7dc352907fc980b868725387e981850b344567e5543096f0a83d9eac0b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef2701251c5db8f853c6e60cb526b6f1", "guid": "bfdfe7dc352907fc980b868725387e984e5371b8cd4eca1a3ea6dfcf7d307c37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985aaa1bde403620ca5f8e13fc5dd6dd7d", "guid": "bfdfe7dc352907fc980b868725387e98d3572e24141325a30bc3c0a31c41fa85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986056506249ef9161ec6478cfb4987f06", "guid": "bfdfe7dc352907fc980b868725387e98b2f53712d1d0af9bc7d13919388a2c6d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e746f4ec8f27f2d369afa50b88d06bd", "guid": "bfdfe7dc352907fc980b868725387e98e29bf2527a03f8a7f0ee4f8fb682157b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98455fc1e039bac37a6973d49e9ea70762", "guid": "bfdfe7dc352907fc980b868725387e9827d1f3d71d300da27dcd08b0f8c4d523"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827f490b34206e11fe284f045bcd3cc58", "guid": "bfdfe7dc352907fc980b868725387e98eee17b6918192a58e3242e8202b1af3d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce8e9c18cf3bd14043aa1ba1a06d97a9", "guid": "bfdfe7dc352907fc980b868725387e98319f0ee3ee4a0d94236a327f7214191b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880ad74c356d6bf531bc42e3c9af208ed", "guid": "bfdfe7dc352907fc980b868725387e9889d6808f96db31c0fa88901646d50905"}], "guid": "bfdfe7dc352907fc980b868725387e98be6229230a4715433df2f8e74fbafc5b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9867aa7825f197b5c58a57320b4ccdc64e", "guid": "bfdfe7dc352907fc980b868725387e980797c2152e50219ee4196549bb34f857"}], "guid": "bfdfe7dc352907fc980b868725387e984d290968aff9eafa4ed5b85c80a8c610", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98fa0d11ed0b4e1a85c13d68e37d1547e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802f35ab680609a626ebd2ddd692a3822", "name": "permission_handler_apple-permission_handler_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f8f53f8ba4165e76c7481b24262177ed", "name": "permission_handler_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}