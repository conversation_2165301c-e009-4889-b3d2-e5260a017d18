# Hodan Hospital Appointment Booking App

A modern Flutter application that enables patients to easily schedule medical consultations, manage appointments, and connect with healthcare providers at Hodan Hospital.

## Features

- 🏥 Easy appointment scheduling
- 👨‍⚕️ Doctor profiles and availability
- 📅 Calendar-based appointment management
- 🔔 Appointment reminders and notifications
- 👤 Patient profile management
- 📱 Modern and intuitive UI
- 🌐 Offline support
- 🔒 Secure data storage
- 🌍 Multi-language support

## Tech Stack

- **Framework:** Flutter
- **State Management:** Flutter Bloc
- **Navigation:** Go Router
- **Local Storage:** ObjectBox
- **Network:** Dio
- **UI Components:** Custom widgets with Material Design
- **Dependency Injection:** GetIt & Injectable

## Getting Started

### Prerequisites

- Flutter SDK (^3.6.1)
- Dart SDK
- Android Studio / VS Code
- Git

### Installation

1. Clone the repository
```bash
git clone https://github.com/axmednajaad/hodan_hospital.git
```

2. Navigate to project directory
```bash
cd hodan_hospital
```

3. Install dependencies
```bash
flutter pub get
```

4. Run the app
```bash
flutter run
```

### Environment Setup

1. Create a `.env` file in the root directory
2. Add your environment variables:
```
API_KEY=your_api_key
API_SECRET=your_api_secret
```

## Project Structure

```
lib/
├── core/           # Core functionality and utilities
├── features/       # Feature modules
├── shared/         # Shared widgets and models
└── main.dart       # Application entry point
```

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- Flutter team for the amazing framework
- All contributors and maintainers
- Hodan Hospital for their support

## Support

For support, please contact:
- Email: <EMAIL>

---

Made with ❤️ by Rasiin Tech
