import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:hodan_hospital/core/services/image_picker_permission_service.dart';

void main() {
  group('ImagePickerPermissionService', () {
    late ImagePickerPermissionService service;

    setUp(() {
      service = ImagePickerPermissionService();
    });

    testWidgets('should be a singleton', (WidgetTester tester) async {
      final service1 = ImagePickerPermissionService();
      final service2 = ImagePickerPermissionService();
      
      expect(service1, equals(service2));
    });

    testWidgets('should handle camera permission gracefully', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) {
                return ElevatedButton(
                  onPressed: () async {
                    final result = await service.pickFromCamera(context: context);
                    // In test environment, this should return null due to no actual camera
                    expect(result, isNull);
                  },
                  child: const Text('Test Camera'),
                );
              },
            ),
          ),
        ),
      );

      await tester.tap(find.byType(ElevatedButton));
      await tester.pumpAndSettle();
    });

    testWidgets('should handle gallery permission gracefully', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) {
                return ElevatedButton(
                  onPressed: () async {
                    final result = await service.pickFromGallery(context: context);
                    // In test environment, this should return null due to no actual gallery
                    expect(result, isNull);
                  },
                  child: const Text('Test Gallery'),
                );
              },
            ),
          ),
        ),
      );

      await tester.tap(find.byType(ElevatedButton));
      await tester.pumpAndSettle();
    });

    test('should check camera permission status', () async {
      // This test will depend on the test environment
      // In a real device test, you would check actual permissions
      final hasPermission = await service.isCameraPermissionGranted();
      expect(hasPermission, isA<bool>());
    });

    test('should check gallery permission status', () async {
      // This test will depend on the test environment
      // In a real device test, you would check actual permissions
      final hasPermission = await service.isGalleryPermissionGranted();
      expect(hasPermission, isA<bool>());
    });
  });
}
