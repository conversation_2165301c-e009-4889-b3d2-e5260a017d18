import Flutter
import UIKit

@main
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    GeneratedPluginRegistrant.register(with: self)
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }
}

// import Flutter
// import UIKit
// import Firebase
// import FirebaseMessaging

// @main
// @objc class AppDelegate: FlutterAppDelegate {
//   override func application(
//     _ application: UIApplication,
//     didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
//   ) -> Bool {
//     FirebaseApp.configure()
//     GeneratedPluginRegistrant.register(with: self)
//     return super.application(application, didFinishLaunchingWithOptions: launchOptions)
//   }

//   override func application(
//     _ application: UIApplication,
//     didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data
//   ) {
//     Messaging.messaging().apnsToken = deviceToken
//     print("APNS Token: \(deviceToken)")
//     // Convert token to string
//     let tokenParts = deviceToken.map { data in String(format: "%02.2hhx", data) }
//     let tokenString = tokenParts.joined()
    
//     // Print formatted token
//     print("Formatted APNS Token: \(tokenString)")
//     super.application(application, didRegisterForRemoteNotificationsWithDeviceToken: deviceToken)
//   }
// }
