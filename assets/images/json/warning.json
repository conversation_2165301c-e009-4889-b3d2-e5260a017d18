{"v": "4.6.10", "fr": 30, "ip": 0, "op": 90, "w": 400, "h": 300, "nm": "error", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "circulo contornos", "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 15, "s": [0], "e": [100]}, {"t": 17}]}, "r": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p667_1_0p333_0", "t": 14, "s": [199.985, 160, 0], "e": [199.985, 167, 0], "to": [0, 1.16666662693024, 0], "ti": [0, -1.16666662693024, 0]}, {"t": 15}]}, "a": {"a": 0, "k": [1.89, 1.873, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 0.667]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0.333]}, "n": ["0p667_1_0p333_0", "0p667_1_0p333_0", "0p667_0p667_0p333_0p333"], "t": 15, "s": [0, 0, 100], "e": [200, 200, 100]}, {"t": 17}], "x": "var $bm_rt;\nvar n, n, t, t, v, amp, freq, decay;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = sub(time, key(n).time);\n}\nif (n > 0 && t < 1) {\n    v = velocityAtTime(sub(key(n).time, div(thisComp.frameDuration, 10)));\n    amp = 0.002;\n    freq = 3;\n    decay = 7;\n    $bm_rt = sum(value, div(mul(mul(v, amp), Math.sin(mul(mul(mul(freq, t), 2), Math.PI))), Math.exp(mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.456, 0], [0.315, -0.315], [0, -0.449], [-0.33, -0.285], [-0.442, 0], [-0.326, 0.289], [0, 0.507], [0.322, 0.315]], "o": [[-0.456, 0], [-0.314, 0.315], [0, 0.514], [0.329, 0.287], [0.434, 0], [0.326, -0.291], [0, -0.449], [-0.322, -0.315]], "v": [[-0.012, -1.623], [-1.169, -1.151], [-1.639, -0.005], [-1.146, 1.194], [0.01, 1.623], [1.15, 1.19], [1.639, -0.005], [1.156, -1.151]], "c": true}}, "nm": "Trazado 1", "mn": "ADBE Vector Shape - Group"}, {"ty": "fl", "c": {"a": 0, "k": [0.259, 0.255, 0.247, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "nm": "Relleno 1", "mn": "ADBE Vector Graphic - Fill"}, {"ty": "tr", "p": {"a": 0, "k": [1.89, 1.873], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transformar"}], "nm": "Grupo 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group"}], "ip": 0, "op": 300, "st": 0, "bm": 0, "sr": 1}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "linea contornos", "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 17, "s": [0], "e": [100]}, {"t": 19}]}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [200, 148.787, 0]}, "a": {"a": 0, "k": [1.889, 5.896, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 0.667]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0.333]}, "n": ["0p667_1_0p333_0", "0p667_1_0p333_0", "0p667_0p667_0p333_0p333"], "t": 15, "s": [0, 0, 100], "e": [200, 200, 100]}, {"t": 17}], "x": "var $bm_rt;\nvar n, n, t, t, v, amp, freq, decay;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = sub(time, key(n).time);\n}\nif (n > 0 && t < 1) {\n    v = velocityAtTime(sub(key(n).time, div(thisComp.frameDuration, 10)));\n    amp = 0.002;\n    freq = 3;\n    decay = 7;\n    $bm_rt = sum(value, div(mul(mul(v, amp), Math.sin(mul(mul(mul(freq, t), 2), Math.PI))), Math.exp(mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.153, -0.328], [-0.392, 0], [-0.167, 0.341], [-0.051, 0.638], [0, 0], [0, 0.478], [0.209, 0.431], [0.623, 0], [0.315, -0.337], [0, -0.6], [-0.065, -1.013]], "o": [[0.064, 0.673], [0.152, 0.33], [0.383, 0], [0.167, -0.34], [0, 0], [0.05, -0.492], [0, -0.811], [-0.209, -0.431], [-0.514, 0], [-0.314, 0.336], [0, 0.443], [0, 0]], "v": [[-1.195, 3.648], [-0.868, 5.151], [-0.055, 5.646], [0.77, 5.135], [1.096, 3.669], [1.564, -1.683], [1.639, -3.138], [1.324, -5], [0.076, -5.646], [-1.169, -5.141], [-1.639, -3.736], [-1.542, -1.553]], "c": true}}, "nm": "Trazado 1", "mn": "ADBE Vector Shape - Group"}, {"ty": "fl", "c": {"a": 0, "k": [0.259, 0.255, 0.247, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "nm": "Relleno 1", "mn": "ADBE Vector Graphic - Fill"}, {"ty": "tr", "p": {"a": 0, "k": [1.889, 5.896], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transformar"}], "nm": "Grupo 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group"}], "ip": 0, "op": 300, "st": 0, "bm": 0, "sr": 1}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "triangulo contornos", "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 13, "s": [0], "e": [100]}, {"t": 17}]}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [199.957, 148.992, 0]}, "a": {"a": 0, "k": [18.159, 14.866, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 0.667]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0.333]}, "n": ["0p667_1_0p333_0", "0p667_1_0p333_0", "0p667_0p667_0p333_0p333"], "t": 9, "s": [0, 0, 100], "e": [190, 190, 100]}, {"t": 15}], "x": "var $bm_rt;\nvar n, n, t, t, v, amp, freq, decay;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = sub(time, key(n).time);\n}\nif (n > 0 && t < 1) {\n    v = velocityAtTime(sub(key(n).time, div(thisComp.frameDuration, 10)));\n    amp = 0.01;\n    freq = 3;\n    decay = 7;\n    $bm_rt = sum(value, div(mul(mul(v, amp), Math.sin(mul(mul(mul(freq, t), 2), Math.PI))), Math.exp(mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.132, 0.179], [0, 0], [0.37, 0], [0.195, -0.315], [0, 0], [-0.189, -0.339], [-0.388, 0], [0, 0], [0, 0.591]], "o": [[0, 0], [-0.194, -0.315], [-0.371, 0], [0, 0], [-0.205, 0.329], [0.189, 0.34], [0, 0], [0.591, 0], [0, -0.24]], "v": [[17.699, 12.908], [0.934, -14.111], [0.025, -14.617], [-0.885, -14.111], [-17.695, 12.982], [-17.72, 14.066], [-16.785, 14.617], [16.839, 14.617], [17.909, 13.546]], "c": true}}, "nm": "Trazado 1", "mn": "ADBE Vector Shape - Group"}, {"ty": "fl", "c": {"a": 0, "k": [0.929, 0.788, 0.173, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "nm": "Relleno 1", "mn": "ADBE Vector Graphic - Fill"}, {"ty": "tr", "p": {"a": 0, "k": [18.159, 14.866], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transformar"}], "nm": "Grupo 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group"}], "ip": 0, "op": 300, "st": 0, "bm": 0, "sr": 1}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "ciruclo contornos", "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [199.984, 150.004, 0]}, "a": {"a": 0, "k": [30.379, 30.38, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 0.667]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0.333]}, "n": ["0p667_1_0p333_0", "0p667_1_0p333_0", "0p667_0p667_0p333_0p333"], "t": 5, "s": [0, 0, 100], "e": [200, 200, 100]}, {"t": 15}], "x": "var $bm_rt;\nvar n, n, t, t, v, amp, freq, decay;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = sub(time, key(n).time);\n}\nif (n > 0 && t < 1) {\n    v = velocityAtTime(sub(key(n).time, div(thisComp.frameDuration, 10)));\n    amp = 0.02;\n    freq = 4;\n    decay = 7;\n    $bm_rt = sum(value, div(mul(mul(v, amp), Math.sin(mul(mul(mul(freq, t), 2), Math.PI))), Math.exp(mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[16.64, 0], [0, -16.642], [-16.64, 0], [0, 16.64]], "o": [[-16.64, 0], [0, 16.64], [16.64, 0], [0, -16.642]], "v": [[0, -30.13], [-30.129, 0], [0, 30.13], [30.129, 0]], "c": true}}, "nm": "Trazado 1", "mn": "ADBE Vector Shape - Group"}, {"ty": "fl", "c": {"a": 0, "k": [0.1647059, 0.2117647, 0.2470588, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "nm": "Relleno 1", "mn": "ADBE Vector Graphic - Fill"}, {"ty": "tr", "p": {"a": 0, "k": [30.379, 30.38], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transformar"}], "nm": "Grupo 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group"}], "ip": 0, "op": 300, "st": 0, "bm": 0, "sr": 1}]}