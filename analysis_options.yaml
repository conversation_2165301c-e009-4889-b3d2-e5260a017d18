include: package:flutter_lints/flutter.yaml

linter:
  rules:
    # PREFER
    prefer_const_constructors: true
    prefer_const_literals_to_create_immutables: true
    prefer_final_fields: true
    prefer_final_locals: true
    prefer_single_quotes: true
    prefer_typing_uninitialized_variables: true
    # prefer_relative_imports: true
    prefer_collection_literals: true
    # prefer_expression_function_bodies: true // not needed for now
    prefer_interpolation_to_compose_strings: true

    # AVOID
    avoid_print: true
    avoid_unnecessary_containers: true
    # avoid_empty_else: true
    avoid_returning_null_for_future: true
    avoid_redundant_argument_values: true

    # STYLE
    always_use_package_imports: false
    sort_child_properties_last: true
    use_key_in_widget_constructors: true
    unnecessary_this: true
    curly_braces_in_flow_control_structures: true

    # EFFECTIVE DART
    # directives_ordering: true // important but later
    lines_longer_than_80_chars: false
    constant_identifier_names: false
