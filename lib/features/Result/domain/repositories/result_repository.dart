import 'package:flutter/foundation.dart';
import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/features/Result/domain/entities/lab_result_entity.dart';

abstract class ResultRepository {
  FutureEitherFailOr<List<LabResultEntity>> getAllLabResults({
    required bool forceFetch,
    required String mobileNumber,
  });

  FutureEitherFailOr<Uint8List> generateLabResultPDF({
    required String id,
    required String? type,
    required String? template,
  });
}
