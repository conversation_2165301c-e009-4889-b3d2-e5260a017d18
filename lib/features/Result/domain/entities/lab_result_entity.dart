import 'lab_result_item_entity.dart';

class LabResultEntity {
  final String labResultID;
  final String patientID;
  final String patientName;
  final String doctorName;
  final String status;
  final String type;
  final String template;
  final List<LabResultItemEntity> items;

  LabResultEntity({
    required this.labResultID,
    required this.patientID,
    required this.patientName,
    required this.doctorName,
    required this.status,
    required this.type,
    required this.template,
    required this.items,
  });

  /// Count total number of tests
  int get totalTests => items.length;

  /// Get number of abnormal or flagged tests
  int get flaggedTestsCount =>
      items.where((item) => item.flag != null && item.flag!.isNotEmpty).length;

  /// Formatted status (optional enhancement)
  String get formattedStatus {
    if (status == 'Submit') {
      return 'Submitted';
    }
    return status;
  }

  /// Returns formatted doctor name with "Dr." prefix if missing.
  String get formattedDoctorName {
    if (!doctorName.startsWith('Dr') && !doctorName.startsWith('Drs')) {
      return 'Dr. $doctorName';
    }
    return doctorName;
  }

  @override
  String toString() {
    return 'LabResultEntity(labResultID: $labResultID, patientID: $patientID, patientName: $patientName, status: $status, type:$type, template:$template items: $items)';
  }
}
