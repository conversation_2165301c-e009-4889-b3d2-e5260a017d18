class LabResultItemEntity {
  final String test;
  final String testEvent;
  final String resultValue;
  final String? normalRange;
  final String? labTestUom;
  final String? labTestComment;
  final String? flag;

  LabResultItemEntity({
    required this.test,
    required this.testEvent,
    required this.resultValue,
    this.normalRange,
    this.labTestUom,
    this.labTestComment,
    this.flag,
  });

  /// Get formatted result (optional enhancement)
  String get formattedResult => '$resultValue ${labTestUom ?? ''}';

  /// Check if result is abnormal (based on flag)
  bool get isFlagged => flag != null && flag!.isNotEmpty;

  @override
  String toString() {
    return 'LabResultItemEntity(test: $test, testEvent: $testEvent, resultValue: $resultValue, normalRange: $normalRange, labTestUom: $labTestUom, labTestComment: $labTestComment, flag: $flag)';
  }
}
