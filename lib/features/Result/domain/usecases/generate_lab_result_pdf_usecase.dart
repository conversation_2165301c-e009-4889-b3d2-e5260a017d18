import 'package:flutter/foundation.dart';
import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/core/use_cases/use_case.dart';
import 'package:hodan_hospital/features/Result/domain/params/generate_lab_result_pdf_params.dart';

import '../repositories/result_repository.dart';

class GenerateLabResultPdfUsecase
    extends UseCase<Uint8List, GenerateLabResultPdfParams> {
  final ResultRepository resultRepository;

  GenerateLabResultPdfUsecase({required this.resultRepository});

  @override
  FutureEitherFailOr<Uint8List> call({
    required GenerateLabResultPdfParams params,
  }) async {
    return await resultRepository.generateLabResultPDF(
      id: params.id,
      type: params.type,
      template: params.template,
    );
  }
}
