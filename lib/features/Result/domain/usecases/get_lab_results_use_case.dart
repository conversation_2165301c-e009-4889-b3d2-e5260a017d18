import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/core/use_cases/use_case.dart';
import 'package:hodan_hospital/features/Result/domain/params/get_lab_result_params.dart';

import '../entities/lab_result_entity.dart';
import '../repositories/result_repository.dart';

class GetLabResultsUseCase
    extends UseCase<List<LabResultEntity>, GetLabResultParams> {
  final ResultRepository resultRepository;

  GetLabResultsUseCase({required this.resultRepository});

  @override
  FutureEitherFailOr<List<LabResultEntity>> call(
      {required GetLabResultParams params}) async {
    return await resultRepository.getAllLabResults(
      forceFetch: params.forceFetch,
      mobileNumber: params.mobileNumber,
    );
  }
}
