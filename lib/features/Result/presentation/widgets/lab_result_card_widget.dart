import 'package:flutter/material.dart';
import 'package:hodan_hospital/core/config/theme/colors/app_colors_extension.dart';
import 'package:hodan_hospital/core/utils/extensions/build_context_extensions.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_container.dart';

import '../../domain/entities/lab_result_entity.dart';

class LabResultCardWidget extends StatelessWidget {
  final LabResultEntity labResult;
  final VoidCallback? onTap;

  const LabResultCardWidget({
    super.key,
    required this.labResult,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = context.textTheme;
    final appColors = context.appColors;
    return CustomContainer(
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header Row
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    labResult.labResultID,
                    style: textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 10),
                    decoration: BoxDecoration(
                      color: _getStatusColor(labResult.formattedStatus),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      labResult.formattedStatus,
                      style: textTheme.labelMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: appColors.whiteColor,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),

              // Patient Info
              // Patient and Doctor Info
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '${labResult.patientName} (${labResult.patientID})',
                    style: textTheme.bodyMedium,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    labResult.formattedDoctorName,
                    style: textTheme.bodySmall?.copyWith(
                      color: appColors.subtextColor,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
              const SizedBox(height: 12),

              // Lab Result Details
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  _buildDetailItem(
                    context,
                    icon: Icons.science,
                    title: 'Tests',
                    value: labResult.totalTests.toString(),
                  ),
                  _buildDetailItem(
                    context,
                    icon: Icons.warning,
                    title: 'Flags',
                    value: labResult.flaggedTestsCount.toString(),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String value,
  }) {
    final textTheme = context.textTheme;
    final appColors = context.appColors;
    return Column(
      children: [
        Icon(icon, size: 20, color: appColors.primaryColor),
        const SizedBox(height: 4),
        Text(
          title,
          style: textTheme.labelSmall,
        ),
        Text(
          value,
          style: textTheme.bodySmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'submitted':
        return Colors.green;
      case 'pending':
        return Colors.orange;
      case 'processing':
        return Colors.blue;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}
