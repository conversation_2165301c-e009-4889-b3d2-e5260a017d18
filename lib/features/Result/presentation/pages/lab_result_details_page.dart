import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hodan_hospital/core/config/router/extension/navigation_extension.dart';
import 'package:hodan_hospital/core/utils/extensions/app_bloc_extensions.dart';
import 'package:hodan_hospital/core/utils/helpers/snack_bar_helper.dart';
import 'package:hodan_hospital/features/Result/presentation/bloc/result_bloc.dart';
import 'package:hodan_hospital/features/shared/presentation/blocs%20or%20cubits/shared%20bloc/shared_bloc.dart';
import 'package:hodan_hospital/features/shared/presentation/pages/pdf_viewer_page.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/animations/animated_app_bar.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_button.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_container.dart';
import 'package:hodan_hospital/core/enums/button_state.dart';
import 'package:hodan_hospital/core/utils/extensions/build_context_extensions.dart';
import 'package:hodan_hospital/core/config/theme/colors/app_colors_extension.dart';

import '../../domain/entities/lab_result_entity.dart';

class LabResultDetailsPage extends StatelessWidget {
  final LabResultEntity labResult;

  const LabResultDetailsPage({super.key, required this.labResult});

  @override
  Widget build(BuildContext context) {
    final textTheme = context.textTheme;
    print('lab result is : $labResult');

    return Scaffold(
      appBar: AnimatedAppBar(title: 'Lab Result #${labResult.labResultID}'),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 16.h),
            _buildSummaryCard(context),
            SizedBox(height: 25.h),
            Text('Test Results:',
                style: textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                )),
            SizedBox(height: 8.h),
            _buildItemsTable(context),
            SizedBox(height: 24.h),
            _buildActionButtons(context),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryCard(BuildContext context) {
    final textTheme = context.textTheme;

    return CustomContainer(
      color: context.appColors.primaryColor.withValues(alpha: 0.1),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Patient Information',
                style: textTheme.titleLarge?.copyWith(fontSize: 18.sp)),
            SizedBox(height: 8.h),
            _buildInfoRow(Icons.person, 'Name', labResult.patientName),
            _buildInfoRow(
                Icons.medical_services, 'Patient ID', labResult.patientID),
            _buildInfoRow(
                Icons.person_2, 'Doctor', labResult.formattedDoctorName),
            _buildInfoRow(Icons.assignment, 'Total Tests',
                labResult.totalTests.toString()),
            _buildInfoRow(Icons.warning, 'Flagged Tests',
                labResult.flaggedTestsCount.toString()),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, size: 20, color: Colors.grey),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(label,
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 13.sp,
                    )),
                SizedBox(height: 2.h),
                Text(value,
                    style: const TextStyle(fontWeight: FontWeight.w500)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildItemsTable(BuildContext context) {
    final textTheme = context.textTheme;
    final appColors = context.appColors;

    return CustomContainer(
      child: Column(
        children: [
          // Table Header
          Container(
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            decoration: BoxDecoration(
              color: appColors.primaryColor.withValues(alpha: 0.05),
              borderRadius:
                  const BorderRadius.vertical(top: Radius.circular(8)),
            ),
            child: const Row(
              children: [
                Expanded(
                  flex: 2,
                  child: Text('Test',
                      style: TextStyle(fontWeight: FontWeight.bold)),
                ),
                Expanded(
                  flex: 2,
                  child: Text('Event',
                      style: TextStyle(fontWeight: FontWeight.bold),
                      textAlign: TextAlign.start),
                ),
                Expanded(
                  child: Text('Result',
                      style: TextStyle(fontWeight: FontWeight.bold),
                      textAlign: TextAlign.center),
                ),
                Expanded(
                  flex: 2,
                  child: Text('N-Range',
                      style: TextStyle(fontWeight: FontWeight.bold),
                      textAlign: TextAlign.center),
                ),
              ],
            ),
          ),

          // Items List
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: labResult.items.length,
            separatorBuilder: (context, index) => const Divider(height: 1),
            itemBuilder: (context, index) {
              final item = labResult.items[index];
              return Padding(
                padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 16.w),
                child: Row(
                  children: [
                    Expanded(
                      flex: 2,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(item.test, style: textTheme.bodyMedium),
                          if (item.labTestUom != null &&
                              item.labTestUom!.isNotEmpty)
                            Text(item.labTestUom!,
                                style: textTheme.bodySmall
                                    ?.copyWith(color: Colors.grey)),
                        ],
                      ),
                    ),
                    Expanded(
                      flex: 2,
                      child: Column(
                        children: [
                          Text(item.testEvent, style: textTheme.bodyMedium),
                          if (item.testEvent.trim().isNotEmpty &&
                              item.labTestUom != null &&
                              item.labTestUom!.isNotEmpty)
                            Text(item.labTestUom!,
                                style: textTheme.bodySmall
                                    ?.copyWith(color: Colors.grey)),
                        ],
                      ),
                    ),
                    Expanded(
                      child: Text(item.resultValue,
                          textAlign: TextAlign.center,
                          style: textTheme.bodyMedium),
                    ),
                    Expanded(
                      flex: 2,
                      child: Text(item.normalRange ?? '-',
                          textAlign: TextAlign.center,
                          style: textTheme.bodyMedium),
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return BlocConsumer<ResultBloc, ResultState>(
      buildWhen: (_, currentState) {
        return currentState is GetLabResultPdfFailure ||
            currentState is GetLabResultPdfLoaded ||
            currentState is GetLabResultPdfLoading;
      },
      listener: (context, state) {
        if (state is GetLabResultPdfFailure) {
          final errorMessage = state.appFailure.getErrorMessage();
          SnackBarHelper.showErrorSnackBar(context, message: errorMessage);
        }
        if (state is GetLabResultPdfLoaded) {
          final pdf = state.pdfBytes;
          if (context.mounted) {
            context.pushRoute(PdfViewerPage(
              pdfBytes: pdf,
              onDownload: () {
                //
              },
              title: 'Lab Result PDF',
            ));
          }
        }
      },
      builder: (context, state) {
        final isLoading = state is GetLabResultPdfLoading;
        return CustomButton(
          buttonState: isLoading ? ButtonState.loading : ButtonState.normal,
          width: 350,
          buttonText: 'Generate PDF',
          onTap: () async {
            context.resultBloc.add(GetLabResultPdf(
              id: labResult.labResultID,
              type: labResult.type,
              template: labResult.template,
            ));
          },
        );
      },
    );
  }
}
