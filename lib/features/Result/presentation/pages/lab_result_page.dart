import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hodan_hospital/core/config/router/extension/navigation_extension.dart';
import 'package:hodan_hospital/core/enums/loading_type.dart';
import 'package:hodan_hospital/core/utils/extensions/app_bloc_extensions.dart';
import 'package:hodan_hospital/core/utils/extensions/build_context_extensions.dart';
import 'package:hodan_hospital/features/Result/presentation/bloc/result_bloc.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/animations/animated_app_bar.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_list_grid_view.dart';

import '../../domain/entities/lab_result_entity.dart';
import '../widgets/lab_result_card_widget.dart';
import 'lab_result_details_page.dart';

class LabResultsPage extends StatefulWidget {
  const LabResultsPage({super.key});

  @override
  State<LabResultsPage> createState() => _LabResultsPageState();
}

class _LabResultsPageState extends State<LabResultsPage> {
  @override
  void initState() {
    super.initState();
    _refreshLabResults(forceFetch: false);
  }

  Future<void> _refreshLabResults({
    bool forceFetch = true,
  }) async {
    final resultBloc = context.resultBloc;
    final canRefresh = forceFetch || resultBloc.labResults.isEmpty;
    if (canRefresh) {
      final mobileNo = context.userBloc.currentUser?.phoneNumber ?? '';
      context.resultBloc.add(GetAllLabResults(
        mobileNumber: mobileNo,
        forceFetch: forceFetch,
      ));
    }
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = context.textTheme;

    return Scaffold(
      appBar: const AnimatedAppBar(title: 'My Lab Results'),
      body: BlocBuilder<ResultBloc, ResultState>(
        buildWhen: (previousState, currentState) {
          return currentState is GetLabResultLoading ||
              currentState is GetLabResultEmpty ||
              currentState is GetLabResultLoaded ||
              currentState is GetLabResultFailure;
        },
        builder: (context, state) {
          // print(state);
          return RefreshIndicator.adaptive(
            onRefresh: _refreshLabResults,
            child: CustomListGridView<LabResultEntity>(
              isLoading: state is GetLabResultLoading,
              isEmpty: state is GetLabResultEmpty,
              items: context.resultBloc.labResults,
              contentType: LoadingType.listView,
              itemBuilder: (context, labResult) {
                return LabResultCardWidget(
                  labResult: labResult,
                  onTap: () {
                    context
                        .pushRoute(LabResultDetailsPage(labResult: labResult));
                  },
                );
              },
              emptyDataBuilder: () {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.science_outlined,
                        size: 48,
                        color: Theme.of(context)
                            .colorScheme
                            .onSurface
                            .withValues(alpha: 0.3),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'No lab results available',
                        style: textTheme.bodyLarge?.copyWith(
                          color: Theme.of(context)
                              .colorScheme
                              .onSurface
                              .withValues(alpha: 0.5),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          );
        },
      ),
    );
  }
}
