part of 'result_bloc.dart';

sealed class ResultState extends Equatable {
  const ResultState();

  @override
  List<Object> get props => [];
}

final class LabResultInitial extends ResultState {}

//------ Get ALl Lab result
final class GetLabResultLoading  extends ResultState {}

final class GetLabResultLoaded extends ResultState {
  final List<LabResultEntity> labResults;

  const GetLabResultLoaded({required this.labResults});

  @override
  List<Object> get props => [labResults];
}

final class GetLabResultFailure extends ResultState {
  final AppFailure appFailure;

  const GetLabResultFailure({required this.appFailure});
}

final class GetLabResultEmpty extends ResultState {}



//------ Get Lab Result Pdf 
final class GetLabResultPdfLoading extends ResultState {}

final class GetLabResultPdfLoaded extends ResultState {
  final Uint8List pdfBytes;

  const GetLabResultPdfLoaded({required this.pdfBytes});

  @override
  List<Object> get props => [pdfBytes];
}

final class GetLabResultPdfFailure extends ResultState {
  final AppFailure appFailure;

  const GetLabResultPdfFailure({required this.appFailure});
}