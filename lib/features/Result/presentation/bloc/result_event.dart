part of 'result_bloc.dart';

sealed class ResultEvent extends Equatable {
  const ResultEvent();

  @override
  List<Object?> get props => [];
}

class GetAllLabResults extends ResultEvent {
  final bool forceFetch;
  final String mobileNumber;

  const GetAllLabResults({required this.mobileNumber, this.forceFetch = false});

  @override
  List<Object> get props => [mobileNumber, forceFetch];
}

class GetLabResultPdf extends ResultEvent {
  final String id;
  final String? type;
  final String? template;

  const GetLabResultPdf({
    required this.id,
    required this.type,
    required this.template,
  });

  @override
  List<Object?> get props => [id, type, template];
}
