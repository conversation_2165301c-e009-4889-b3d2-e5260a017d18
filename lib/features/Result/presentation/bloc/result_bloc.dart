import 'dart:async';

import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/core/utils/helpers/bloc_helper.dart';
import 'package:hodan_hospital/features/Result/domain/entities/lab_result_entity.dart';
import 'package:hodan_hospital/features/Result/domain/params/generate_lab_result_pdf_params.dart';
import 'package:hodan_hospital/features/Result/domain/params/get_lab_result_params.dart';
import 'package:hodan_hospital/features/Result/domain/usecases/generate_lab_result_pdf_usecase.dart';

import '../../domain/usecases/get_lab_results_use_case.dart';

part 'result_event.dart';
part 'result_state.dart';

class ResultBloc extends Bloc<ResultEvent, ResultState> {
  final GetLabResultsUseCase getLabResultsUseCase;
  final GenerateLabResultPdfUsecase generateLabResultPdfUsecase;

  ResultBloc({
    required this.getLabResultsUseCase,
    required this.generateLabResultPdfUsecase,
  }) : super(LabResultInitial()) {
    on<GetAllLabResults>(
      _onGetAllLabResults,
      transformer: BlocHelper.debounceHelper(),
    );
    on<GetLabResultPdf>(
      _onGetLabResultPdf,
      transformer: BlocHelper.debounceHelper(),
    );
  }
  List<LabResultEntity> _labResults = [];
  List<LabResultEntity> get labResults => _labResults;

  Future<void> _onGetAllLabResults(
    GetAllLabResults event,
    Emitter<ResultState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<List<LabResultEntity>, ResultState>(
        emit: emit,
        loadingState: GetLabResultLoading(),
        callUseCase: getLabResultsUseCase(
            params: GetLabResultParams(
          mobileNumber: event.mobileNumber,
          forceFetch: event.forceFetch,
        )),
        onSuccess: (results) {
          if (results.isEmpty) {
            return GetLabResultEmpty();
          }
          _labResults = results;

          return GetLabResultLoaded(labResults: results);
        },
        onFailure: (failure) {
          _labResults = [];
          return GetLabResultFailure(appFailure: failure);
        });
  }

  FutureOr<void> _onGetLabResultPdf(
      GetLabResultPdf event, Emitter<ResultState> emit) async {
    await BlocHelper.handleEventAndEmit<Uint8List, ResultState>(
        emit: emit,
        loadingState: GetLabResultPdfLoading(),
        callUseCase: generateLabResultPdfUsecase(
            params: GenerateLabResultPdfParams(
          id: event.id,
          template: event.template,
          type: event.type,
        )),
        onSuccess: (pdfBytes) {
          return GetLabResultPdfLoaded(pdfBytes: pdfBytes);
        },
        onFailure: (failure) {
          return GetLabResultPdfFailure(appFailure: failure);
        });
  }
}
