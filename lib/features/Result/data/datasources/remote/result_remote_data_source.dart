// ignore_for_file: unnecessary_null_comparison

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:fpdart/fpdart.dart';
import 'package:hodan_hospital/core/constants/api_end_points.dart';
import 'package:hodan_hospital/core/enums/http_method.dart';
import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/core/errors/http_error_handler.dart';
import 'package:hodan_hospital/core/network/api_client/dio_api_client.dart';
import 'package:hodan_hospital/core/utils/helpers/request_data.dart';
import 'package:hodan_hospital/core/utils/helpers/response_handler.dart';
import 'package:hodan_hospital/features/Result/data/models/lab_result_model.dart';

abstract class ResultRemoteDataSource {
  FutureEitherFailOr<List<LabResultModel>> getLabResults({
    required String mobileNo,
  });

  FutureEitherFailOr<Uint8List> generateLabResultPDF({
    required String id,
    required String? type,
    required String? template,
  });
}

class ResultRemoteDataSourceImpl implements ResultRemoteDataSource {
  final DioApiClient dioApiClient;
  final HttpErrorHandler httpErrorHandler;

  const ResultRemoteDataSourceImpl(
      {required this.dioApiClient, required this.httpErrorHandler});

  @override
  FutureEitherFailOr<List<LabResultModel>> getLabResults({
    required String mobileNo,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (data) => LabResultModel.fromJsonList(data as List<dynamic>),
      requestFunction: () => dioApiClient.request(
          method: HttpMethod.get,
          endPointUrl: ApiEndpoints.getLabResults,
          data: RequestData.json({
            'mobile': mobileNo,
          })),
    );

    return ResponseHandler<List<LabResultModel>>(response)
        .handleResponseAndExtractData(
      onFailure: (failure) => left(failure),
      onSuccess: (data) => right(data),
    );
  }

  @override
  FutureEitherFailOr<Uint8List> generateLabResultPDF({
    required String id,
    required String? type,
    required String? template,
  }) async {
    final doctype = 'Lab Result';
    const defaultFormat = 'lab result report';
    const groupRoutineTemplates = {'STOOL ROUTINE', 'Urine Routine', 'CBC'};

    final cleanedType = type?.trim();
    final cleanedTemplate = template?.trim();

    final String format = () {
      if (cleanedType == 'Group') {
        if (cleanedTemplate != null &&
            groupRoutineTemplates.contains(cleanedTemplate)) {
          return 'Urine Report';
        } else {
          return 'Urine Report2';
        }
      }
      return defaultFormat;
    }();

    final queryParams = {
      'doctype': doctype,
      'name': id,
      'format': format,
      'no_letterhead': '0',
      'letterhead': 'Logo',
    };

    final response = await httpErrorHandler.handleRequestBytes(
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.get,
        endPointUrl: ApiEndpoints.getPDF,
        responseType: ResponseType.bytes,
        queryParameters: queryParams,
      ),
    );

    return response.fold(
      (failure) => left(failure),
      (bytes) => right(bytes),
    );
  }
}
