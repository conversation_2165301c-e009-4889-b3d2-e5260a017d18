// import 'package:hodan_hospital/core/enums/database_failure_type.dart';
// import 'package:hodan_hospital/core/enums/database_operation_type.dart';
// import 'package:hodan_hospital/core/errors/database_error_handler.dart';
// import 'package:hodan_hospital/core/errors/app_failure.dart';
// import 'package:hodan_hospital/features/Result/data/models/lab_result_model.dart';

// abstract class ResultDatabaseManager {
//   FutureEitherFailOr<List<LabResultModel>> readLabResults();
//   FutureEitherFailOr<List<int>> saveLabResults({
//     required List<LabResultModel> labResults,
//   });
// }

// class ResultDatabaseManagerImpl implements ResultDatabaseManager {
//   final DatabaseErrorHandler databaseErrorHandler;

//   ResultDatabaseManagerImpl({required this.databaseErrorHandler});

//   /// 🟡 **Read Lab Results**
//   @override
//   FutureEitherFailOr<List<LabResultModel>> readLabResults() async {
//     return await databaseErrorHandler.handleDatabaseOperation(
//       operationType: DatabaseOperationType.read,
//       operationFunction: () async {
//         // final orderBox =
//         //     await databaseErrorHandler.databaseManager.getBox<LabResultModel>();

//         // // Find Orders and return
//         // final appointments = orderBox.query().build().find();
//         // if (appointments.isNotEmpty) {
//         //   return appointments;
//         // }

//         throw DatabaseFailure(
//           message: 'No Lab Results found in the database',
//           failureType: DatabaseFailureType.noDataFound,
//           stackTrace: StackTrace.current,
//         );
//       },
//     );
//   }

//   @override
//   FutureEitherFailOr<List<int>> saveLabResults(
//       {required List<LabResultModel> labResults}) async {
//     return await databaseErrorHandler.handleDatabaseOperation(
//       operationType: DatabaseOperationType.read,
//       operationFunction: () async {
//         // final orderBox =
//         //     await databaseErrorHandler.databaseManager.getBox<LabResultModel>();

//         // orderBox.removeAll();

//         // return orderBox.putMany(labResults);
//       },
//     );
//   }
// }
