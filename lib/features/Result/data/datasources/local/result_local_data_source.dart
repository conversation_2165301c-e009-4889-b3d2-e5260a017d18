// import 'package:fpdart/fpdart.dart';
// import 'package:hodan_hospital/core/enums/cache_failure_type.dart';
// import 'package:hodan_hospital/core/errors/app_failure.dart';
// import 'package:hodan_hospital/features/Result/data/datasources/local/result_database_manager.dart';

// import '../../models/lab_result_model.dart';

// abstract class ResultLocalDataSource {
//   FutureEitherFailOr<List<int>> saveLabResults({
//     required List<LabResultModel> labResults,
//   });
//   FutureEitherFailOr<List<LabResultModel>> readLabResults();
// }

// class ResultLocalDataSourceImpl implements ResultLocalDataSource {
//   final ResultDatabaseManager resultDatabaseManager;

//   ResultLocalDataSourceImpl({
//     required this.resultDatabaseManager,
//   });

//   @override
//   FutureEitherFailOr<List<LabResultModel>> readLabResults() async {
//     try {
//       return await resultDatabaseManager.readLabResults();
//     } catch (e, stackTrace) {
//       return left(
//         CacheFailure(
//           message: "Failed to read orders from Cache: ${e.toString()}",
//           failureType: CacheFailureType.readError,
//           stackTrace: stackTrace,
//         ),
//       );
//     }
//   }

//   @override
//   FutureEitherFailOr<List<int>> saveLabResults({
//     required List<LabResultModel> labResults,
//   }) async {
//     return await resultDatabaseManager.saveLabResults(
//       labResults: labResults,
//     );
//   }
// }
