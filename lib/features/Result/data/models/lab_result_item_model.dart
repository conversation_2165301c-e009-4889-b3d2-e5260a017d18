// import 'package:objectbox/objectbox.dart';

import '../../../../core/enums/parse_failure_type.dart';
import '../../../../core/errors/app_failure.dart';

// @Entity()
class LabResultItemModel {
  // @Id()
  int id = 0;

  final String test;
  final String testEvent;
  final String resultValue;
  final String? normalRange;
  final String? labTestUom;
  final String? labTestComment;
  final String? flag;

  // final labResult = ToOne<LabResultModel>();

  LabResultItemModel({
    required this.test,
    required this.testEvent,
    required this.resultValue,
    this.normalRange,
    this.labTestUom,
    this.labTestComment,
    this.flag,
  });

  factory LabResultItemModel.fromJson(Map<String, dynamic> map) {
    try {
      return LabResultItemModel(
        test: map['test'] ?? '',
        testEvent: map['lab_test_event'] ?? '',
        resultValue: map['result_value'] ?? '',
        normalRange: map['normal_range'],
        labTestUom: map['lab_test_uom'],
        labTestComment: map['lab_test_comment'],
        flag: map['flag'],
      );
    } catch (e, s) {
      throw ParsingFailure(
        message: 'Failed to parse LabResultItemModel: ${e.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: LabResultItemModel,
        stackTrace: s,
      );
    }
  }

  Map<String, dynamic> toMap() => {
        'test': test,
        'testEvent': testEvent,
        'resultValue': resultValue,
        'normalRange': normalRange,
        'labTestUom': labTestUom,
        'labTestComment': labTestComment,
        'flag': flag,
      };

  static List<LabResultItemModel> fromJsonList(List<dynamic> jsonList) {
    return jsonList.map((json) => LabResultItemModel.fromJson(json)).toList();
  }

  @override
  String toString() {
    return 'LabResultItemModel(test: $test,testEvent: $testEvent, resultValue: $resultValue, normalRange: $normalRange, labTestUom: $labTestUom, labTestComment: $labTestComment, flag: $flag)';
  }
}
