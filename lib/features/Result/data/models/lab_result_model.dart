import 'package:hodan_hospital/core/enums/parse_failure_type.dart';
import 'package:hodan_hospital/core/errors/app_failure.dart';
// import 'package:objectbox/objectbox.dart';

import 'lab_result_item_model.dart';

// @Entity()
class LabResultModel {
  // @Id()
  int id = 0;
  // @Index()
  final String labResultID;
  final String patientID;
  final String patientName;
  final String doctorName;
  final String status;
  final String type;
  final String template;

  /// ObjectBox relation for items
  // @Backlink('labResult')
  // ToMany<LabResultItemModel> items = ToMany<LabResultItemModel>();

  final List<LabResultItemModel> items;

  LabResultModel({
    required this.labResultID,
    required this.patientID,
    required this.patientName,
    required this.doctorName,
    required this.status,
    required this.type,
    required this.template,
    required this.items,
  });

  Map<String, dynamic> toMap() => {
        'labResultID': labResultID,
        'patientID': patientID,
        'patientName': patientName,
        'doctorName': doctorName,
        'status': status,
        // "items": items.map((item) => item.toMap()).toList(),
      };

  factory LabResultModel.fromJson(Map<String, dynamic> map) {
    try {
      final labResult = LabResultModel(
        labResultID: map['name'] ?? '',
        patientID: map['patient'] ?? '',
        patientName: map['patient_name'] ?? '',
        doctorName: map['practitioner'] ?? '',
        status: map['status'] ?? '',
        type: map['type'] ?? '',
        template: map['template'] ?? '',
        items: LabResultItemModel.fromJsonList(
          map['items'] as List<dynamic>? ?? [],
        ),
      );

      return labResult;
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message:
            "Failed to parse LabResultModel (ID: ${map['name']}): ${error.toString()}",
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: LabResultModel,
        stackTrace: stackTrace,
      );
    }
  }

  static List<LabResultModel> fromJsonList(List<dynamic> jsonList) {
    return jsonList.map((json) => LabResultModel.fromJson(json)).toList();
  }

  @override
  String toString() {
    return 'LabResultModel(labResultID: $labResultID, patientID: $patientID, patientName: $patientName, status: $status)';
  }
}
