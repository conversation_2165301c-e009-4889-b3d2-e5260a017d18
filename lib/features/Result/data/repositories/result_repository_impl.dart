import 'dart:typed_data';

import 'package:fpdart/fpdart.dart';
import 'package:hodan_hospital/core/config/logger/app_logger.dart';
import 'package:hodan_hospital/core/errors/app_failure.dart';
// import 'package:hodan_hospital/features/Result/data/datasources/local/result_local_data_source.dart';
import 'package:hodan_hospital/features/Result/data/mapper/lab_result_mapper.dart';
import 'package:hodan_hospital/features/Result/data/models/lab_result_model.dart';
import 'package:hodan_hospital/features/Result/domain/entities/lab_result_entity.dart';
import 'package:hodan_hospital/features/Result/domain/repositories/result_repository.dart';

import '../datasources/remote/result_remote_data_source.dart';

class ResultRepositoryImpl implements ResultRepository {
  final ResultRemoteDataSource remoteDataSource;
  // final ResultLocalDataSource localDataSource;

  ResultRepositoryImpl({
    required this.remoteDataSource,
    // required this.localDataSource,
  });

  @override
  FutureEitherFailOr<List<LabResultEntity>> getAllLabResults({
    required bool forceFetch,
    required String mobileNumber,
  }) async {
    try {
      // if (!forceFetch) {
      //   final cachedResponse = await _getCachedLabResults();
      //   if (cachedResponse.isRight()) {
      //     return cachedResponse;
      //   }
      // }

      final remoteResponse =
          await _fetchLabResultsFromServer(mobileNo: mobileNumber);
      return remoteResponse.fold(
        (failure) async {
          // final cachedResponse = await _getCachedLabResults();
          // if (cachedResponse.isRight()) {
          //   return cachedResponse;
          // }
          return left(failure);
        },
        (orders) async {
          // await localDataSource.saveLabResults(labResults: orders);
          final orderEntities = LabResultMapper.modelListToEntityList(orders);
          return right(orderEntities);
        },
      );
    } catch (error, stackTrace) {
      AppLogger().error(
        'Unexpected error in getAllLabResulst',
        error: error,
        stackTrace: stackTrace,
      );
      return left(UnexpectedFailure(
        message: 'Unexpected error in getAllLabResulst',
        stackTrace: stackTrace,
      ));
    }
  }

  // FutureEitherFailOr<List<LabResultEntity>> _getCachedLabResults() async {
  //   final cachedResponse = await localDataSource.readLabResults();
  //   return cachedResponse.map(
  //     (appointmentModels) {
  //       final appointmentEntities =
  //           LabResultMapper.modelListToEntityList(appointmentModels);
  //       return appointmentEntities;
  //     },
  //   );
  // }

  FutureEitherFailOr<List<LabResultModel>> _fetchLabResultsFromServer({
    required String mobileNo,
  }) async {
    return await remoteDataSource.getLabResults(mobileNo: mobileNo);
  }

  @override
  FutureEitherFailOr<Uint8List> generateLabResultPDF({
    required String id,
    required String? type,
    required String? template,
  }) async {
    return await remoteDataSource.generateLabResultPDF(
      id: id,
      type: type,
      template: template,
    );
  }
}
