import 'package:hodan_hospital/features/Order/data/models/order_model.dart';
import 'package:hodan_hospital/features/Order/domain/entities/order_entity.dart';

import '../../domain/entities/lab_result_entity.dart';
import '../models/lab_result_model.dart';
import 'lab_result_item_mapper.dart';

class LabResultMapper {
  /// Converts a LabResultEntity to a LabResultModel
  static LabResultModel entityToModel(LabResultEntity entity) {
    final labResultModel = LabResultModel(
      labResultID: entity.labResultID,
      patientID: entity.patientID,
      patientName: entity.patientName,
      doctorName: entity.doctorName,
      status: entity.status,
      type: entity.type,
      template: entity.template,
      items: LabResultItemMapper.entityListToModelList(entity.items),
    );

    /// Map and attach items + set parent reference
    // final items = LabResultItemMapper.entityListToModelList(entity.items);
    // for (final item in items) {
    //   item.labResult.target = labResultModel;
    // }
    // labResultModel.items.addAll(items);

    return labResultModel;
  }

  /// Converts a LabResultModel to a LabResultEntity
  static LabResultEntity modelToEntity(LabResultModel model) {
    return LabResultEntity(
      labResultID: model.labResultID,
      patientID: model.patientID,
      patientName: model.patientName,
      doctorName: model.doctorName,
      status: model.status,
      type: model.type,
      template: model.template,
      // items: LabResultItemMapper.modelListToEntityList(model.items),
      items: LabResultItemMapper.modelListToEntityList(model.items),
    );
  }

  /// Converts a list of LabResultModel to a list of LabResultEntity
  static List<LabResultEntity> modelListToEntityList(
      List<LabResultModel> models) {
    return models.map((model) => modelToEntity(model)).toList();
  }

  /// Converts a list of LabResultEntity to a list of LabResultModel
  static List<LabResultModel> entityListToModelList(
      List<LabResultEntity> entities) {
    return entities.map((entity) => entityToModel(entity)).toList();
  }
}

class OrderItemMapper {
  /// Converts a OrderItemEntity to a OrderItemModel
  static OrderItemModel entityToModel(OrderItemEntity entity) {
    return OrderItemModel(
      itemCode: entity.itemCode,
      itemName: entity.itemName,
      qty: entity.qty,
      rate: entity.rate,
      amount: entity.amount,
    );
  }

  /// Converts a OrderItemModel to a OrderItemEntity
  static OrderItemEntity modelToEntity(OrderItemModel model) {
    return OrderItemEntity(
      itemCode: model.itemCode,
      itemName: model.itemName,
      qty: model.qty,
      rate: model.rate,
      amount: model.amount,
    );
  }

  /// Converts a list of OrderItemModel to a list of OrderItemEntity
  static List<OrderItemEntity> modelListToEntityList(
      List<OrderItemModel> models) {
    return models.map((model) => modelToEntity(model)).toList();
  }

  /// Converts a list of OrderItemEntity to a list of OrderItemModel
  static List<OrderItemModel> entityListToModelList(
      List<OrderItemEntity> entities) {
    return entities.map((entity) => entityToModel(entity)).toList();
  }
}
