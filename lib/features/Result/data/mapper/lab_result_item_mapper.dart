import '../../domain/entities/lab_result_item_entity.dart';
import '../models/lab_result_item_model.dart';

class LabResultItemMapper {
  /// Converts a LabResultItemEntity to a LabResultItemModel
  static LabResultItemModel entityToModel(LabResultItemEntity entity) {
    return LabResultItemModel(
      test: entity.test,
      testEvent: entity.testEvent,
      resultValue: entity.resultValue,
      normalRange: entity.normalRange,
      labTestUom: entity.labTestUom,
      labTestComment: entity.labTestComment,
      flag: entity.flag,
    );
  }

  /// Converts a LabResultItemModel to a LabResultItemEntity
  static LabResultItemEntity modelToEntity(LabResultItemModel model) {
    return LabResultItemEntity(
      test: model.test,
      testEvent: model.testEvent,
      resultValue: model.resultValue,
      normalRange: model.normalRange,
      labTestUom: model.labTestUom,
      labTestComment: model.labTestComment,
      flag: model.flag,
    );
  }

  /// Converts a list of LabResultItemModel to a list of LabResultItemEntity
  static List<LabResultItemEntity> modelListToEntityList(
      List<LabResultItemModel> models) {
    return models.map((model) => modelToEntity(model)).toList();
  }

  /// Converts a list of LabResultItemEntity to a list of LabResultItemModel
  static List<LabResultItemModel> entityListToModelList(
      List<LabResultItemEntity> entities) {
    return entities.map((entity) => entityToModel(entity)).toList();
  }
}
