import 'package:fpdart/fpdart.dart';
import 'package:hodan_hospital/core/constants/app_constants.dart';
import 'package:hodan_hospital/core/enums/app_start_state.dart';
import 'package:hodan_hospital/core/enums/otp_type.dart';
import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/features/authentication/data/datasources/local/auth_local_data_source.dart';
import 'package:hodan_hospital/features/authentication/data/datasources/remote/auth_remote_data_source.dart';
import 'package:hodan_hospital/features/authentication/data/mappers/auth_mappers.dart';
import 'package:hodan_hospital/features/authentication/domain/repositories/auth_repository.dart';
import 'package:hodan_hospital/features/shared/domain/entities/user_entity.dart';

import '../../../../core/config/logger/app_logger.dart';
import '../../../../core/enums/cache_failure_type.dart';
import '../../../../core/services/sms_services.dart';
import '../models/otp_data_model.dart';

class AuthRepositoryImpl implements AuthRepository {
  final AuthRemoteDataSource authRemoteDataSource;
  final AuthLocalDataSource authLocalDataSource;
  final SmsServices smsServices;

  AuthRepositoryImpl({
    required this.authRemoteDataSource,
    required this.authLocalDataSource,
    required this.smsServices,
  });

  @override
  FutureEitherFailOr<UserEntity> login({required String mobileNumber}) async {
    final response =
        await authRemoteDataSource.login(mobileNumber: mobileNumber);

    return response.fold(
      (failure) {
        return left(failure);
      },
      (userModel) async {
        //
        await authLocalDataSource.saveUserData(userModel: userModel);
        final userEntity = AuthMapper.toUserEntity(userModel);
        // send otp
        try {
          await sendOtp(mobile: mobileNumber, isLogin: true);
        } catch (e, stackTrace) {
          AppLogger().error(
            'Failed to send OTP: ${e.toString()}',
            error: e,
            stackTrace: stackTrace,
          );
        }
        return right(userEntity);
      },
    );
  }

  @override
  FutureEitherFailOr<String> register({
    required String patFullName,
    required String patGender,
    required double patAge,
    // required String patAgeType,
    required String patMobileNumber,
    required String patDistrict,
  }) async {
    return await authRemoteDataSource.register(
      patFullName: patFullName,
      patGender: patGender,
      patAge: patAge,
      // patAgeType: patAgeType,
      patMobileNumber: patMobileNumber,
      patDistrict: patDistrict,
    );
  }

  @override
  FutureEitherFailOr<String> logout() async {
    final response = await authLocalDataSource.deleteUserData();
    return response.fold(
      (failure) => left(failure),
      (success) => right('Logged out successfully'),
    );
  }

  @override
  FutureEitherFailOr<AppStartState> checkUserAuthentication() async {
    return await authLocalDataSource.checkUserAuthentication();
  }

  // @override
  // FutureEitherFailOr<UserEntity> getCurrentUser() async {
  //   // final response = await authLocalDataSource.readUserData();

  //   // return response.fold((failure) => left(failure), (userModel) {
  //   //   final userEntity = AuthMapper.toUserEntity(userModel);
  //   //   return right(userEntity);
  //   // });
  // }

  @override
  FutureEitherFailOr<String> canRegister({
    required String fullName,
    required String mobile,
  }) async {
    final response = await authRemoteDataSource.canRegister(
      fullName: fullName,
      mobile: mobile,
    );

    return await response.fold(
      (failure) {
        return left(failure);
      },
      (message) async {
        try {
          await sendOtp(mobile: mobile, isLogin: false);
        } catch (e, s) {
          AppLogger().error(
            'Failed to send Otp in Can Register : $e',
            error: e,
            stackTrace: s,
          );
        }
        return right(message);
      },
    );
  }

  @override
  FutureEitherFailOr<void> completeOnboarding() async {
    return await authLocalDataSource.completeOnboarding();
  }

  // @override
  // FutureEitherFailOr<String> sendOtp({
  //   required String mobile,
  //   required bool isLogin,
  // }) async {
  //   try {
  //     final appName = AppConstants.appName;

  //     final otpData = OtpDataModel.generateOtp(
  //       mobile: mobile,
  //       otpType: isLogin ? OtpType.login : OtpType.registration,
  //     );

  //     final message =
  //         'Your OTP for ${isLogin ? 'login' : 'account registration'} is ${otpData.otp}. '
  //         'It will expire in 5 minutes. Do not share this code with anyone. '
  //         'This message is sent by $appName.';

  //     final smsResult = await smsServices.sendSMS(
  //       mobileNumber: mobile,
  //       message: message,
  //     );

  //     return await smsResult.fold(
  //       (failure) => left(failure),
  //       (success) async {
  //         final storeResult =
  //             await authLocalDataSource.storeOtp(otpData: otpData);
  //         return storeResult.fold(
  //           (storeFailure) => left(storeFailure),
  //           (_) => right('OTP sent to $mobile'),
  //         );
  //       },
  //     );
  //   } catch (e, stackTrace) {
  //     return left(
  //       CacheFailure(
  //         message: 'Unexpected error while sending OTP: ${e.toString()}',
  //         failureType: CacheFailureType.writeError,
  //         stackTrace: stackTrace,
  //       ),
  //     );
  //   }
  // }

  @override
  FutureEitherFailOr<String> sendOtp({
    required String mobile,
    required bool isLogin,
  }) async {
    try {
      final appName = AppConstants.appName;

      final otpData = OtpDataModel.generateOtp(
        mobile: mobile,
        otpType: isLogin ? OtpType.login : OtpType.registration,
      );

      final message =
          'Your OTP for ${isLogin ? 'login' : 'account registration'} is ${otpData.otp}. '
          'It will expire in 5 minutes. Do not share this code with anyone. '
          'This message is sent by $appName.';

      // ✅ Skip sending SMS if it's the Apple TestFlight test account
      if (mobile == AppConstants.testAccountNumber) {
        AppLogger().info('Skipping SMS for test account: $mobile');

        // Still store the OTP locally so verification works
        final storeResult =
            await authLocalDataSource.storeOtp(otpData: otpData);
        return storeResult.fold(
          (storeFailure) => left(storeFailure),
          (_) => right('OTP (Test) stored for $mobile'),
        );
      }

      // ✅ For real users, send SMS
      final smsResult = await smsServices.sendSMS(
        mobileNumber: mobile,
        message: message,
      );

      return await smsResult.fold(
        (failure) => left(failure),
        (success) async {
          final storeResult =
              await authLocalDataSource.storeOtp(otpData: otpData);
          return storeResult.fold(
            (storeFailure) => left(storeFailure),
            (_) => right('OTP sent to $mobile'),
          );
        },
      );
    } catch (e, stackTrace) {
      return left(
        CacheFailure(
          message: 'Unexpected error while sending OTP: ${e.toString()}',
          failureType: CacheFailureType.writeError,
          stackTrace: stackTrace,
        ),
      );
    }
  }

  @override
  FutureEitherFailOr<String> resendOtp({
    required String mobile,
    required bool isLogin,
  }) async {
    return await sendOtp(mobile: mobile, isLogin: isLogin);
  }

  @override
  FutureEitherFailOr<String> verifyOtp({
    required String otp,
    required String mobile,
  }) async {
    try {
      final otpResult = await authLocalDataSource.verifyOtp(
        mobile: mobile,
        enteredOtp: otp,
      );
      return await otpResult.fold(
        (failure) => left(failure),
        (isValid) async {
          if (!isValid) {
            return left(
              CacheFailure(
                message: 'Invalid OTP',
                failureType: CacheFailureType.notFound,
              ),
            );
          }

          return right('OTP verified');
        },
      );
    } catch (e, stackTrace) {
      return left(
        CacheFailure(
          // message: 'Unexpected error while verifying OTP: ${e.toString()}',
          message: 'Failed to verify otp',
          failureType: CacheFailureType.writeError,
          stackTrace: stackTrace,
        ),
      );
    }
  }
}
