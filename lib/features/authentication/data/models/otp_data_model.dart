import 'dart:math';

import 'package:equatable/equatable.dart';
import 'package:hodan_hospital/core/enums/otp_type.dart';
import 'package:hodan_hospital/core/utils/extensions/app_date_extensions.dart';

import '../../../../core/constants/app_constants.dart';
import '../../../../core/enums/parse_failure_type.dart';
import '../../../../core/errors/app_failure.dart';

class OtpDataModel extends Equatable {
  final String otp;
  final String mobile;
  final DateTime expiresAt;
  final OtpType otpType;

  const OtpDataModel({
    required this.otp,
    required this.mobile,
    required this.expiresAt,
    required this.otpType,
  });

  factory OtpDataModel.fromJson(Map<String, dynamic> json) {
    try {
      return OtpDataModel(
        otp: json['otp'] ?? '',
        mobile: json['mobile'] ?? '',
        expiresAt:
            (json['expires_at'] as String?)?.toDateTime() ?? DateTime.now(),
        otpType: OtpType.values.firstWhere(
          (e) => e.name == json['otp_type'],
          orElse: () => OtpType.login,
        ),
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse OtpDataModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: OtpDataModel,
        stackTrace: stackTrace,
      );
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'otp': otp,
      'mobile': mobile,
      'expires_at': expiresAt.toIso8601String(),
      'otp_type': otpType.name,
    };
  }

  @override
  String toString() {
    return 'OtpDataModel(otp: $otp, mobile: $mobile, expiresAt: $expiresAt, otpType: ${otpType.name})';
  }

  bool get isExpired => DateTime.now().isAfter(expiresAt);

  static String _generateSecureOtp() {
    final random = Random.secure();
    return (100000 + random.nextInt(900000)).toString();
  }

  // static OtpDataModel generateOtp({
  //   required String mobile,
  //   required OtpType otpType,
  // }) {
  //   final otp = _generateSecureOtp();
  //   final expiresAt = DateTime.now().add(const Duration(minutes: 5));
  //   return OtpDataModel(
  //     otp: otp,
  //     mobile: mobile,
  //     expiresAt: expiresAt,
  //     otpType: otpType,
  //   );
  // }

  static OtpDataModel generateOtp({
    required String mobile,
    required OtpType otpType,
  }) {
    // Force OTP for Apple TestFlight review account
    final isTestAccount = mobile == AppConstants.testAccountNumber;
    final otp = isTestAccount ? '000000' : _generateSecureOtp();
    final expiresAt = DateTime.now().add(const Duration(minutes: 5));

    return OtpDataModel(
      otp: otp,
      mobile: mobile,
      expiresAt: expiresAt,
      otpType: otpType,
    );
  }

  OtpDataModel copyWith({
    String? otp,
    String? mobile,
    DateTime? expiresAt,
    OtpType? otpType,
  }) {
    return OtpDataModel(
      otp: otp ?? this.otp,
      mobile: mobile ?? this.mobile,
      expiresAt: expiresAt ?? this.expiresAt,
      otpType: otpType ?? this.otpType,
    );
  }

  @override
  List<Object?> get props => [otp, mobile, expiresAt, otpType];
}
