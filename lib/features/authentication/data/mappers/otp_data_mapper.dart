import '../../domain/entities/otp_data_entity.dart';
import '../models/otp_data_model.dart';

class OtpDataMapper {
  static OtpDataEntity toEntity(OtpDataModel model) {
    return OtpDataEntity(
      otp: model.otp,
      mobile: model.mobile,
      expiresAt: model.expiresAt,
      otpType: model.otpType,
    );
  }

  static OtpDataModel toModel(OtpDataEntity entity) {
    return OtpDataModel(
      otp: entity.otp,
      mobile: entity.mobile,
      expiresAt: entity.expiresAt,
      otpType: entity.otpType,
    );
  }
}
