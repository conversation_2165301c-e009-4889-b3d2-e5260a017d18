import 'package:hodan_hospital/features/shared/data/models/user_model.dart';
import 'package:hodan_hospital/features/shared/domain/entities/user_entity.dart';

class AuthMapper {
  static UserModel toUserModel(UserEntity user) {
    return UserModel(
      pID: user.pID,
      fullName: user.fullName,
      age: user.age,
      district: user.district,
      phoneNumber: user.phoneNumber,
      gender: user.gender,
      profileImage: user.profileImage,
    );
  }

  static UserEntity toUserEntity(UserModel user) {
    return UserEntity(
      pID: user.pID,
      fullName: user.fullName,
      age: user.age,
      district: user.district,
      phoneNumber: user.phoneNumber,
      gender: user.gender,
      profileImage: user.profileImage,
    );
  }
}
