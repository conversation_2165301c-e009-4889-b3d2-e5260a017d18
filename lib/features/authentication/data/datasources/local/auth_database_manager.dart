// import 'package:fpdart/fpdart.dart';
// import '../../../../../core/enums/database_operation_type.dart';
// import '../../../../../core/errors/app_failure.dart';
// import '../../../../../core/errors/database_error_handler.dart';
// import '../../../../shared/data/models/user_model.dart';
// // import 'package:hodan_hospital/objectbox.g.dart';

// abstract class AuthDatabaseManager {
//   FutureEitherFailOr<int> saveUserData({required UserModel userModel});
//   FutureEitherFailOr<bool> deleteUserData({required String patientId});
//   FutureEitherFailOr<UserModel> readUserData({required String patientId});
// }

// class AuthDatabaseManagerImpl implements AuthDatabaseManager {
//   final DatabaseErrorHandler databaseErrorHandler;

//   AuthDatabaseManagerImpl({required this.databaseErrorHandler});

//   /// 🟢 **Save User Data**
//   @override
//   FutureEitherFailOr<int> saveUserData({required UserModel userModel}) async =>
//       await databaseErrorHandler.handleDatabaseOperation(
//         operationType: DatabaseOperationType.write,
//         operationFunction: () async {
//           await deleteUserData(patientId: userModel.pID);
//           // final userBox =
//           // await databaseErrorHandler.databaseManager.getBox<UserModel>();

//           // Insert or Update user
//           // return userBox.put(userModel);
//           return 1; // Simulating successful save with a dummy ID
//         },
//       );

//   /// 🔴 **Delete User Data**
//   @override
//   FutureEitherFailOr<bool> deleteUserData({required String patientId}) async =>
//       await databaseErrorHandler.handleDatabaseOperation(
//         operationType: DatabaseOperationType.delete,
//         operationFunction: () async {
//           // final userBox =
//           //     await databaseErrorHandler.databaseManager.getBox<UserModel>();

//           // // Find user by patientId
//           // final query = userBox.query(UserModel_.pID.equals(patientId)).build();
//           // final user = query.findFirst();

//           // if (user == null) {
//           //   return false; // User not found
//           // }

//           // userBox.remove(user.id);
//           return true;
//         },
//       );

//   /// 🟡 **Read User Data**
//   @override
//   FutureEitherFailOr<UserModel> readUserData(
//           {required String patientId}) async =>
//       await databaseErrorHandler.handleDatabaseOperation(
//         operationType: DatabaseOperationType.read,
//         operationFunction: () async {
//           // final userBox =
//           //     await databaseErrorHandler.databaseManager.getBox<UserModel>();

//           // // Find user by patientId
//           // final query = userBox.query(UserModel_.pID.equals(patientId)).build();
//           // return query.findFirst();

//           final user = UserModel(
//             pID: 'pID',
//             fullName: 'fullName',
//             phoneNumber: 'phoneNumber',
//             age: 22,
//             district: 'district',
//             gender: 'Male',
//           );
//           return user;
//         },
//       );
// }
