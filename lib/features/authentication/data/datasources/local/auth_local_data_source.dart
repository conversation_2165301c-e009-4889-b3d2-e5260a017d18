import 'dart:convert';

import 'package:fpdart/fpdart.dart';
import 'package:hodan_hospital/core/config/logger/app_logger.dart';
import 'package:hodan_hospital/core/constants/local_storage_key_constants.dart';
import 'package:hodan_hospital/core/enums/app_start_state.dart';
import 'package:hodan_hospital/core/enums/cache_failure_type.dart';
import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/core/services/flutter_secure_storage_services.dart';
// import 'package:hodan_hospital/features/authentication/data/datasources/local/auth_database_manager.dart';
import 'package:hodan_hospital/features/shared/data/models/user_model.dart';

import '../../models/otp_data_model.dart';

abstract class AuthLocalDataSource {
  FutureEitherFailOr<int> saveUserData({required UserModel userModel});
  FutureEitherFailOr<bool> deleteUserData();
  // FutureEitherFailOr<UserModel> readUserData();
  FutureEitherFailOr<AppStartState> checkUserAuthentication();
  FutureEitherFailOr<void> completeOnboarding();

  FutureEitherFailOr<void> storeOtp({
    required OtpDataModel otpData,
  });
  // FutureEitherFailOr<OtpDataModel> readOtp({
  //   required String mobile,
  // });

  FutureEitherFailOr<bool> verifyOtp({
    required String mobile,
    required String enteredOtp,
  });
}

class AuthLocalDataSourceImpl implements AuthLocalDataSource {
  final FlutterSecureStorageServices flutterSecureStorageServices;
  // final AuthDatabaseManager authDatabaseManager;

  AuthLocalDataSourceImpl({
    required this.flutterSecureStorageServices,
    // required this.authDatabaseManager,
  });

  /// 🟢 **Save User Data Locally**
  @override
  FutureEitherFailOr<int> saveUserData({required UserModel userModel}) async {
    // Save user in **ObjectBox Database**
    // final dbResponse =
    // await authDatabaseManager.saveUserData(userModel: userModel);

    // return dbResponse.fold(
    //   (failure) => left(failure),
    //   (userId) async {
    //     try {
    // Serialize and store user data in **FlutterSecureStorage**
    await flutterSecureStorageServices.storeData(
      key: LocalStorageKeyConstants.userDataKey,
      value: userModel.pID,
    );

    //     // Return user ID as confirmation
    return right(1);

    //   return right(userId);
    // } catch (e, stackTrace) {
    //   return left(
    //     CacheFailure(
    //       message: 'Failed to save user in Cache: ${e.toString()}',
    //       failureType: CacheFailureType.writeError,
    //       stackTrace: stackTrace,
    //     ),
    //   );
    // }
    // },
    // );
  }

  /// 🔴 **Delete User Data from Local Storage and Database**
  @override
  FutureEitherFailOr<bool> deleteUserData() async {
    // Read user ID before deleting
    // final dbResponse = await readUserData();

    // return dbResponse.fold(
    //   (failure) => left(failure),
    //   (user) async {
    //     try {
    // Delete from **ObjectBox Database**
    // final dbDeleteResponse =
    // await authDatabaseManager.deleteUserData(patientId: user.pID);

    //       return dbDeleteResponse.fold(
    //         (dbFailure) => left(dbFailure),
    //         (_) async {
    // Delete user from **FlutterSecureStorage**
    await Future.wait([
      flutterSecureStorageServices.removeData(
          key: LocalStorageKeyConstants.userDataKey),
      flutterSecureStorageServices.removeData(
          key: LocalStorageKeyConstants.otpVerified),
    ]);
    return right(true);
    // },

    //       );
    //     } catch (e, stackTrace) {
    //       return left(
    //         CacheFailure(
    //           message: 'Failed to delete user from Cache: ${e.toString()}',
    //           failureType: CacheFailureType.deleteError,
    //           stackTrace: stackTrace,
    //         ),
    //       );
    //     }
    //   },
    // );
  }

  /// 🟡 **Read User Data**
  // @override
  // FutureEitherFailOr<UserModel> readUserData() async {
  //   try {
  //     // Read user JSON from **FlutterSecureStorage**
  //     final pID = await flutterSecureStorageServices.readData(
  //         key: LocalStorageKeyConstants.userDataKey);
  //     if (pID == null) {
  //       return left(
  //         CacheFailure(
  //           message: 'No pID   found in Cache',
  //           failureType: CacheFailureType.notFound,
  //         ),
  //       );
  //     }

  //     // Verify with **ObjectBox Database**
  //     final dbResponse = await authDatabaseManager.readUserData(patientId: pID);

  //     return dbResponse.fold(
  //       (failure) => left(failure),
  //       (user) => right(user),
  //     );
  //   } catch (e, stackTrace) {
  //     return left(
  //       CacheFailure(
  //         message: 'Failed to read user from Cache: ${e.toString()}',
  //         failureType: CacheFailureType.readError,
  //         stackTrace: stackTrace,
  //       ),
  //     );
  //   }
  // }

  @override
  FutureEitherFailOr<AppStartState> checkUserAuthentication() async {
    try {
      // final onboarding = await flutterSecureStorageServices.readData(
      //     key: LocalStorageKeyConstants.onboardingCompletedKey);
      // if (onboarding == null || onboarding.isEmpty) {
      //   return right(AppStartState.onboardingRequired);
      // }

      // Check if user data exists
      final pID = await flutterSecureStorageServices.readData(
        key: LocalStorageKeyConstants.userDataKey,
      );

      if (pID == null || pID.isEmpty) {
        return right(AppStartState.loggedOut);
      }

      // ✅ Check if OTP is verified
      final otpVerified = await flutterSecureStorageServices.readData(
        key: LocalStorageKeyConstants.otpVerified,
      );

      if (otpVerified == null || otpVerified != 'verified') {
        return right(AppStartState.loggedOut); // OTP not verified
      }

      // ✅ Both user and verified OTP exist → Logged in
      return right(AppStartState.loggedIn);
    } catch (error) {
      return right(AppStartState.loggedOut);
    }
  }

  @override
  FutureEitherFailOr<void> completeOnboarding() async {
    try {
      await flutterSecureStorageServices.storeData(
        key: LocalStorageKeyConstants.onboardingCompletedKey,
        value: 'true',
      );
      return right(null);
    } catch (e, stackTrace) {
      return left(
        CacheFailure(
          message: 'Failed to complete onboarding: ${e.toString()}',
          failureType: CacheFailureType.readError,
          stackTrace: stackTrace,
        ),
      );
    }
  }

  @override
  FutureEitherFailOr<void> storeOtp({
    required OtpDataModel otpData,
  }) async {
    try {
      final otpKey = 'otp_data_${otpData.mobile}';
      final jsonString = otpData.toJson();

      await flutterSecureStorageServices.storeData(
        key: otpKey,
        value: jsonEncode(jsonString),
      );
      return right(null);
    } catch (e, stackTrace) {
      return left(
        CacheFailure(
          message: 'Failed to store OTP: ${e.toString()}',
          failureType: CacheFailureType.writeError,
          stackTrace: stackTrace,
        ),
      );
    }
  }

  // @override
  // FutureEitherFailOr<OtpDataModel> readOtp({
  FutureEitherFailOr<OtpDataModel> _readOtp({
    required String mobile,
  }) async {
    try {
      final otpKey = 'otp_data_$mobile';
      final jsonString =
          await flutterSecureStorageServices.readData(key: otpKey);

      if (jsonString == null) {
        return left(
          CacheFailure(
            message: 'OTP not found for this number.',
            failureType: CacheFailureType.notFound,
          ),
        );
      }

      final jsonMap = jsonDecode(jsonString);
      final otpData = OtpDataModel.fromJson(jsonMap);

      return right(otpData);
    } catch (e, stackTrace) {
      return left(
        CacheFailure(
          message: 'Failed to read OTP: ${e.toString()}',
          failureType: CacheFailureType.readError,
          stackTrace: stackTrace,
        ),
      );
    }
  }

  @override
  FutureEitherFailOr<bool> verifyOtp({
    required String mobile,
    required String enteredOtp,
  }) async {
    try {
      final otpResult = await _readOtp(mobile: mobile);

      return otpResult.fold(
        (failure) => left(failure),
        (otpData) async {
          // OTP Mismatch
          if (otpData.otp != enteredOtp) {
            return left(CacheFailure(
              message: 'Invalid OTP',
              failureType: CacheFailureType.invalidOtp,
            ));
          }

          // OTP Expired
          if (DateTime.now().isAfter(otpData.expiresAt)) {
            return left(CacheFailure(
              message: 'OTP expired',
              failureType: CacheFailureType.expired,
            ));
          }

          // Successful verification
          await _storeOtpVerified();
          return right(true);
        },
      );
    } catch (e, stackTrace) {
      return left(CacheFailure(
        message: 'OTP verification failed',
        failureType: CacheFailureType.verificationError,
        stackTrace: stackTrace,
      ));
    }
  }

  Future<void> _storeOtpVerified() async {
    try {
      await flutterSecureStorageServices.storeData(
        key: LocalStorageKeyConstants.otpVerified,
        value: 'verified',
      );
    } catch (e, s) {
      AppLogger()
          .error('storeOtpVerified failed : $e', error: e, stackTrace: s);
    }
  }
}
