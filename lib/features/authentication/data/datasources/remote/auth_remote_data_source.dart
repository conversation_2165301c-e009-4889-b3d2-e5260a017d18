import 'package:fpdart/fpdart.dart';
import 'package:hodan_hospital/core/constants/api_end_points.dart';
import 'package:hodan_hospital/core/enums/http_method.dart';
import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/core/errors/http_error_handler.dart';
import 'package:hodan_hospital/core/network/api_client/dio_api_client.dart';
import 'package:hodan_hospital/core/utils/helpers/request_data.dart';
import 'package:hodan_hospital/core/utils/helpers/response_handler.dart';
import 'package:hodan_hospital/features/shared/data/models/user_model.dart';

abstract class AuthRemoteDataSource {
  FutureEitherFailOr<UserModel> login({required String mobileNumber});
  FutureEitherFailOr<String> register({
    required String patFullName,
    required String patGender,
    required double patAge, //
    // required String patAgeType,
    required String patMobileNumber,
    required String patDistrict,
  });

  FutureEitherFailOr<String> canRegister({
    required String fullName,
    required String mobile,
  });
}

class AuthRemoteDataSourceImpl implements AuthRemoteDataSource {
  final DioApiClient dioApiClient;
  final HttpErrorHandler httpErrorHandler;

  AuthRemoteDataSourceImpl({
    required this.dioApiClient,
    required this.httpErrorHandler,
  });

  @override
  FutureEitherFailOr<UserModel> login({
    required String mobileNumber,
  }) async {
    final response = await httpErrorHandler.handleRequest<UserModel>(
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.post,
        endPointUrl: ApiEndpoints.login,
        data: RequestData.json({
          'mobile_number': mobileNumber,
        }),
      ),
      fromJsonT: (data) => UserModel.fromJson(data as Map<String, dynamic>),
    );

    return ResponseHandler<UserModel>(response).handleResponseAndExtractData(
      onSuccess: (userModel) {
        //
        return right(userModel);
      },
      onFailure: (failure) {
        //
        return left(failure);
      },
    );
  }

  @override
  FutureEitherFailOr<String> register({
    required String patFullName,
    required String patGender,
    required double patAge,
    // required String patAgeType,
    required String patMobileNumber,
    required String patDistrict,
  }) async {
    final response = await httpErrorHandler.handleRequest<String>(
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.post,
        endPointUrl: ApiEndpoints.register,
        data: RequestData.json(
          {
            'pat_full_name': patFullName,
            'pat_gender': patGender,
            'pat_age': patAge,
            // 'pat_age_type': patAgeType,
            'pat_mobile_number': patMobileNumber,
            'pat_district': patDistrict,
          },
        ),
      ),
    );

    return ResponseHandler(response).handleResponse(
      onSuccess: (apiResponse) => right(apiResponse.apiMessage),
      onFailure: (failure) => left(failure),
    );
  }

  @override
  FutureEitherFailOr<String> canRegister({
    required String fullName,
    required String mobile,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.post,
        endPointUrl: ApiEndpoints.canRegister,
        data: RequestData.json(
          {
            'full_name': fullName,
            'mobile_number': mobile,
          },
        ),
      ),
    );

    return response.fold(
      (failure) => left(failure),
      (apiResponse) {
        final apiMessage = apiResponse.apiMessage;
        return right(apiMessage);
      },
    );
  }
}
