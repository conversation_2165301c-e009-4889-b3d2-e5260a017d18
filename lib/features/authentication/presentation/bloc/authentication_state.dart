// part of 'authentication_bloc.dart';

// // ✅ Authentication States
// abstract class AuthenticationState extends Equatable {
//   const AuthenticationState();

//   @override
//   List<Object> get props => [];
// }

// // ✅ Initial State
// class AuthenticationInitial extends AuthenticationState {}

// // ✅ Loading State
// class AuthenticationLoading extends AuthenticationState {}

// class AuthenticationChecking extends AuthenticationState {}

// class AuthenticationLoginLoading extends AuthenticationState {}

// class AuthenticationRegisterLoading extends AuthenticationState {}

// // ✅ Onboarding State
// class OnboardingRequired extends AuthenticationState {}

// class OnboardingCompleted extends AuthenticationState {}

// // ✅ Authenticated State
// class Authenticated extends AuthenticationState {
//   final UserEntity? user;

//   const Authenticated({this.user});
// }

// // ✅ Registration Successful State
// class RegistrationSuccess extends AuthenticationState {
//   final String message;

//   const RegistrationSuccess({required this.message});

//   @override
//   List<Object> get props => [message];
// }

// // ✅ Unauthenticated State (For Logout or Failed Auth)
// class Unauthenticated extends AuthenticationState {}

// // ✅ Error State
// class AuthenticationFailure extends AuthenticationState {
//   final AppFailure appFailure;

//   const AuthenticationFailure({required this.appFailure});

//   @override
//   List<Object> get props => [appFailure];
// }

//-------------------       !!!!!!!!!!!!!!!!   ---------------------------
part of 'authentication_bloc.dart';

// ✅ Authentication States
abstract class AuthenticationState extends Equatable {
  const AuthenticationState();

  @override
  List<Object> get props => [];
}

// ✅ Initial State
class AuthenticationInitial extends AuthenticationState {}

// ✅ Common Loading States
class AuthenticationLoading extends AuthenticationState {}

class AuthenticationChecking extends AuthenticationState {}

// ✅ Onboarding States
class OnboardingRequired extends AuthenticationState {}

class OnboardingCompleted extends AuthenticationState {}

// ✅ Authenticated State
class Authenticated extends AuthenticationState {
  final UserEntity? user;

  const Authenticated({this.user});

  @override
  List<Object> get props => [user ?? ''];
}

// ✅ Unauthenticated State (For Logout or Failed Auth)
class Unauthenticated extends AuthenticationState {}

// =============================================
// ✅ Event-Specific States (Grouped by Event)
// =============================================

// 1️⃣ Login Event States
class AuthenticationLoginLoading extends AuthenticationState {}

class LoginSuccess extends AuthenticationState {
  final String message;
  const LoginSuccess({required this.message});
  @override
  List<Object> get props => [message];
}

class LoginFailure extends AuthenticationState {
  final AppFailure appFailure;
  const LoginFailure({required this.appFailure});
  @override
  List<Object> get props => [appFailure];
}

// 2️⃣ Register Event States
class AuthenticationRegisterLoading extends AuthenticationState {}

class RegistrationSuccess extends AuthenticationState {
  final String message;
  const RegistrationSuccess({required this.message});
  @override
  List<Object> get props => [message];
}

class RegistrationFailure extends AuthenticationState {
  final AppFailure appFailure;
  const RegistrationFailure({required this.appFailure});
  @override
  List<Object> get props => [appFailure];
}

// ✅ CanRegister Loading State
class CanRegisterChecking extends AuthenticationState {}

// ✅ CanRegister Success State
class CanRegisterSuccess extends AuthenticationState {
  final String message;

  const CanRegisterSuccess({
    required this.message,
  });

  @override
  List<Object> get props => [message];
}

// ✅ CanRegister Failure State
class CanRegisterFailure extends AuthenticationState {
  final AppFailure appFailure;

  const CanRegisterFailure({required this.appFailure});

  @override
  List<Object> get props => [appFailure];
}

// 3️⃣ Verify OTP Event States
class AuthenticationVerifyOtpLoading extends AuthenticationState {}

class VerifyOtpSuccess extends AuthenticationState {
  final String message;
  const VerifyOtpSuccess({required this.message});
  @override
  List<Object> get props => [message];
}

class VerifyOtpFailure extends AuthenticationState {
  final AppFailure appFailure;
  const VerifyOtpFailure({required this.appFailure});
  @override
  List<Object> get props => [appFailure];
}

// 4️⃣ Resend OTP Event States
class AuthenticationResendOtpLoading extends AuthenticationState {}

class ResendOtpSuccess extends AuthenticationState {
  final String message;
  const ResendOtpSuccess({required this.message});
  @override
  List<Object> get props => [message];
}

class ResendOtpFailure extends AuthenticationState {
  final AppFailure appFailure;
  const ResendOtpFailure({required this.appFailure});
  @override
  List<Object> get props => [appFailure];
}

// 5️⃣ Common Failure State (Fallback)
class AuthenticationFailure extends AuthenticationState {
  final AppFailure appFailure;
  const AuthenticationFailure({required this.appFailure});
  @override
  List<Object> get props => [appFailure];
}
