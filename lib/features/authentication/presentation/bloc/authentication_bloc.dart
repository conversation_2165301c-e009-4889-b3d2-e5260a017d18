// ignore_for_file: unreachable_switch_default

import 'dart:async';

import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hodan_hospital/core/enums/app_start_state.dart';
import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/core/params/no_params.dart';
import 'package:hodan_hospital/core/utils/helpers/bloc_helper.dart';
import 'package:hodan_hospital/features/authentication/domain/params/login_params.dart';
import 'package:hodan_hospital/features/authentication/domain/params/register_params.dart';
import 'package:hodan_hospital/features/authentication/domain/params/resend_otp_params.dart';
import 'package:hodan_hospital/features/authentication/domain/params/verify_otp_params.dart';
import 'package:hodan_hospital/features/authentication/domain/use_cases/can_register_usecase.dart';
import 'package:hodan_hospital/features/authentication/domain/use_cases/check_user_authentication_use_case.dart';
import 'package:hodan_hospital/features/authentication/domain/use_cases/complete_onboarding_use_case.dart';
import 'package:hodan_hospital/features/authentication/domain/use_cases/login_use_case.dart';
import 'package:hodan_hospital/features/authentication/domain/use_cases/logout_use_case.dart';
import 'package:hodan_hospital/features/authentication/domain/use_cases/register_use_case.dart';
import 'package:hodan_hospital/features/shared/domain/entities/user_entity.dart';

import '../../domain/params/can_register_params.dart';
import '../../domain/use_cases/resend_otp_usecase.dart';
import '../../domain/use_cases/verify_otp_use_case.dart';

part 'authentication_event.dart';
part 'authentication_state.dart';

class AuthenticationBloc
    extends Bloc<AuthenticationEvent, AuthenticationState> {
  final LoginUseCase loginUseCase;
  final RegisterUseCase registerUseCase;
  final LogoutUseCase logoutUseCase;
  final CheckUserAuthenticationUseCase checkUserAuthenticationUseCase;
  final CompleteOnboardingUseCase completeOnboardingUseCase;
  final ResendOtpUseCase resendOtpUseCase;
  final VerifyOtpUseCase verifyOtpUseCase;
  final CanRegisterUsecase canRegisterUseCase;

  AuthenticationBloc({
    required this.loginUseCase,
    required this.registerUseCase,
    required this.logoutUseCase,
    required this.checkUserAuthenticationUseCase,
    required this.completeOnboardingUseCase,
    required this.resendOtpUseCase,
    required this.verifyOtpUseCase,
    required this.canRegisterUseCase,
  }) : super(AuthenticationInitial()) {
    /// ✅ Handle Login
    on<LoginEvent>(_onLogin, transformer: BlocHelper.debounceHelper());

    /// ✅ Handle Registration
    on<RegisterEvent>(_onRegister, transformer: BlocHelper.debounceHelper());

    /// ✅ Handle CanRegister Check
    on<CanRegisterEvent>(_onCanRegister,
        transformer: BlocHelper.debounceHelper());

    /// ✅ Handle ResendOtp
    on<ResendOtpEvent>(_onResendOtp, transformer: BlocHelper.debounceHelper());

    /// ✅ Handle VerifyOtp
    on<VerifyOtpEvent>(_onVerifyOtp, transformer: BlocHelper.debounceHelper());

    /// ✅ Handle Logout
    on<LogoutEvent>(_onLogout, transformer: BlocHelper.debounceHelper());

    /// ✅ Check User Authentication
    on<CheckUserAuthenticationEvent>(_onCheckUserAuthentication,
        transformer: BlocHelper.debounceHelper());

    /// ✅ Complete onbaording
    on<CompleteOnBoardingEvent>(_onCompleteOnBoardingEvent,
        transformer: BlocHelper.debounceHelper());
  }

  /// 🟢 Handle Login
  Future<void> _onLogin(
      LoginEvent event, Emitter<AuthenticationState> emit) async {
    await BlocHelper.handleEventAndEmit<UserEntity, AuthenticationState>(
      emit: emit,
      loadingState: AuthenticationLoginLoading(),
      callUseCase:
          loginUseCase(params: LoginParams(mobileNumber: event.mobileNumber)),
      onSuccess: (userEntity) {
        return const LoginSuccess(message: 'Login Success');
      },
      onFailure: (failure) => LoginFailure(appFailure: failure),
    );
  }

  // 🟢 Handle CanRegister Check
  Future<void> _onCanRegister(
      CanRegisterEvent event, Emitter<AuthenticationState> emit) async {
    await BlocHelper.handleEventAndEmit<String, AuthenticationState>(
      emit: emit,
      loadingState: CanRegisterChecking(),
      callUseCase: canRegisterUseCase(
          params: CanRegisterParams(
        mobileNumber: event.mobileNumber,
        fullName: event.fullName,
      )),
      onSuccess: (message) => CanRegisterSuccess(
        message: message,
      ),
      onFailure: (failure) => CanRegisterFailure(appFailure: failure),
    );
  }

  /// 🟢 Handle Registration
  Future<void> _onRegister(
      RegisterEvent event, Emitter<AuthenticationState> emit) async {
    await BlocHelper.handleEventAndEmit<String, AuthenticationState>(
      emit: emit,
      loadingState: AuthenticationRegisterLoading(),
      callUseCase: registerUseCase(
        params: RegisterParams(
          patFullName: event.fullName,
          patGender: event.gender,
          patAge: event.age,
          // patAgeType: event.ageType,
          patMobileNumber: event.mobileNumber,
          patDistrict: event.district,
        ),
      ),
      onSuccess: (message) => RegistrationSuccess(message: message),
      onFailure: (failure) => RegistrationFailure(appFailure: failure),
    );
  }

  /// 🟢 Handle Resend Otp
  FutureOr<void> _onResendOtp(
      ResendOtpEvent event, Emitter<AuthenticationState> emit) async {
    await BlocHelper.handleEventAndEmit<String, AuthenticationState>(
      emit: emit,
      loadingState: AuthenticationResendOtpLoading(),
      callUseCase: resendOtpUseCase(
          params: ResendOtpParams(
        mobileNumber: event.mobileNumber,
        isLogin: event.isLogin,
      )),
      onSuccess: (message) {
        return ResendOtpSuccess(message: message);
      },
      onFailure: (failure) => ResendOtpFailure(appFailure: failure),
    );
  }

  /// 🟢 Handle Verify Otp
  FutureOr<void> _onVerifyOtp(
      VerifyOtpEvent event, Emitter<AuthenticationState> emit) async {
    await BlocHelper.handleEventAndEmit<String, AuthenticationState>(
      emit: emit,
      loadingState: AuthenticationVerifyOtpLoading(),
      callUseCase: verifyOtpUseCase(
          params: VerifyOtpParams(
        mobileNumber: event.mobileNumber,
        otp: event.otp,
      )),
      onSuccess: (message) {
        return VerifyOtpSuccess(message: message);
      },
      onFailure: (failure) => VerifyOtpFailure(appFailure: failure),
    );
  }

  /// 🟢 Handle Logout
  Future<void> _onLogout(
      LogoutEvent event, Emitter<AuthenticationState> emit) async {
    await BlocHelper.handleEventAndEmit<String, AuthenticationState>(
      emit: emit,
      loadingState: AuthenticationLoading(),
      callUseCase: logoutUseCase(params: NoParams()),
      onSuccess: (_) => Unauthenticated(),
      onFailure: (failure) => AuthenticationFailure(appFailure: failure),
    );
  }

  /// 🟢 Handle Logout
  Future<void> _onCompleteOnBoardingEvent(
      CompleteOnBoardingEvent event, Emitter<AuthenticationState> emit) async {
    await BlocHelper.handleEventAndEmit<void, AuthenticationState>(
      emit: emit,
      loadingState: AuthenticationLoading(),
      callUseCase: completeOnboardingUseCase(params: NoParams()),
      onSuccess: (_) => OnboardingCompleted(),
      onFailure: (failure) => AuthenticationFailure(appFailure: failure),
    );
  }

  /// 🟢  Check User Authentication
  Future<void> _onCheckUserAuthentication(CheckUserAuthenticationEvent event,
      Emitter<AuthenticationState> emit) async {
    await BlocHelper.handleEventAndEmit<AppStartState, AuthenticationState>(
      emit: emit,
      loadingState: AuthenticationChecking(),
      callUseCase: checkUserAuthenticationUseCase(params: NoParams()),
      onSuccess: (state) {
        switch (state) {
          case AppStartState.loggedIn:
            return const Authenticated();
          case AppStartState.onboardingRequired:
            return OnboardingRequired();
          case AppStartState.loggedOut:
          default:
            return Unauthenticated();
        }
      },
      onFailure: (failure) => Unauthenticated(),
    );
  }
}
