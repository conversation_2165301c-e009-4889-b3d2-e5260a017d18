// part of 'authentication_bloc.dart';

// abstract class AuthenticationEvent extends Equatable {
//   const AuthenticationEvent();

//   @override
//   List<Object> get props => [];
// }

// // ✅ Login Event
// class LoginEvent extends AuthenticationEvent {
//   final String mobileNumber;

//   const LoginEvent({required this.mobileNumber});

//   @override
//   List<Object> get props => [mobileNumber];
// }

// // ✅ Register Event
// class RegisterEvent extends AuthenticationEvent {
//   final String fullName;
//   final String gender;
//   final double age;
//   final String ageType;
//   final String mobileNumber;
//   final String district;

//   const RegisterEvent({
//     required this.fullName,
//     required this.gender,
//     required this.age,
//     required this.ageType,
//     required this.mobileNumber,
//     required this.district,
//   });

//   @override
//   List<Object> get props =>
//       [fullName, gender, age, ageType, mobileNumber, district];
// }

// // ✅ Logout Event
// class LogoutEvent extends AuthenticationEvent {}

// // ✅ CheckUserAuthentication Event
// class CheckUserAuthenticationEvent extends AuthenticationEvent {}

// // ✅ Complete Onboarding Event
// class CompleteOnBoardingEvent extends AuthenticationEvent {}

//--------------------!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! ----------------
part of 'authentication_bloc.dart';

abstract class AuthenticationEvent extends Equatable {
  const AuthenticationEvent();

  @override
  List<Object> get props => [];
}

// ✅ Login Event
class LoginEvent extends AuthenticationEvent {
  final String mobileNumber;

  const LoginEvent({required this.mobileNumber});

  @override
  List<Object> get props => [mobileNumber];
}

// ✅ Checks if this patient can be registered
class CanRegisterEvent extends AuthenticationEvent {
  final String mobileNumber;
  final String fullName;

  const CanRegisterEvent({
    required this.mobileNumber,
    required this.fullName,
  });

  @override
  List<Object> get props => [mobileNumber, fullName];
}

// ✅ Register Event
class RegisterEvent extends AuthenticationEvent {
  final String fullName;
  final String gender;
  final double age;
  // final String ageType;
  final String mobileNumber;
  final String district;

  const RegisterEvent({
    required this.fullName,
    required this.gender,
    required this.age,
    // required this.ageType,
    required this.mobileNumber,
    required this.district,
  });

  @override
  List<Object> get props =>
      [fullName, gender, age, mobileNumber, district];
}

// ✅ Verify OTP Event
class VerifyOtpEvent extends AuthenticationEvent {
  final String mobileNumber;
  final String otp;

  const VerifyOtpEvent({
    required this.mobileNumber,
    required this.otp,
  });

  @override
  List<Object> get props => [mobileNumber, otp];
}

// ✅ Resend OTP Event
class ResendOtpEvent extends AuthenticationEvent {
  final String mobileNumber;
  final bool isLogin;

  const ResendOtpEvent({
    required this.mobileNumber,
    required this.isLogin,
  });

  @override
  List<Object> get props => [mobileNumber, isLogin];
}

// ✅ Logout Event
class LogoutEvent extends AuthenticationEvent {}

// ✅ CheckUserAuthentication Event
class CheckUserAuthenticationEvent extends AuthenticationEvent {}

// ✅ Complete Onboarding Event
class CompleteOnBoardingEvent extends AuthenticationEvent {}
