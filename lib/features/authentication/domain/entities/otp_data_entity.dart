import 'package:equatable/equatable.dart';

import '../../../../core/enums/otp_type.dart';

class OtpDataEntity extends Equatable {
  final String otp;
  final String mobile;
  final DateTime expiresAt;
  final OtpType otpType;

  const OtpDataEntity({
    required this.otp,
    required this.mobile,
    required this.expiresAt,
    required this.otpType,
  });

  @override
  List<Object?> get props => [otp, mobile, expiresAt, otpType];
}
