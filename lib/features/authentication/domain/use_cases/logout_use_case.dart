import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/core/params/no_params.dart';
import 'package:hodan_hospital/core/use_cases/use_case.dart';
import 'package:hodan_hospital/features/authentication/domain/repositories/auth_repository.dart';

// ✅ Concrete Use Case for Logout
class LogoutUseCase implements UseCase<String, NoParams> {
  final AuthRepository repository;

  LogoutUseCase({required this.repository});

  @override
  FutureEitherFailOr<String> call({required NoParams params}) {
    return repository.logout();
  }
}
