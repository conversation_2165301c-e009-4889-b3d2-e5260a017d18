import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/core/use_cases/use_case.dart';
import 'package:hodan_hospital/features/authentication/domain/params/login_params.dart';
import 'package:hodan_hospital/features/authentication/domain/repositories/auth_repository.dart';
import 'package:hodan_hospital/features/shared/domain/entities/user_entity.dart';

// ✅ Concrete Use Case for Login
class LoginUseCase implements UseCase<UserEntity, LoginParams> {
  final AuthRepository repository;

  LoginUseCase({required this.repository});

  @override
  FutureEitherFailOr<UserEntity> call({required LoginParams params}) {
    return repository.login(mobileNumber: params.mobileNumber);
  }
}
