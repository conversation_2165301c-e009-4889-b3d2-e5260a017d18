import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/core/params/no_params.dart';
import 'package:hodan_hospital/core/use_cases/use_case.dart';
import 'package:hodan_hospital/features/authentication/domain/repositories/auth_repository.dart';

// ✅ Concrete Use Case for Completting onboarding
class CompleteOnboardingUseCase implements UseCase<void, NoParams> {
  final AuthRepository repository;

  CompleteOnboardingUseCase({required this.repository});

  @override
  FutureEitherFailOr<void> call({required NoParams params}) {
    return repository.completeOnboarding();
  }
}
