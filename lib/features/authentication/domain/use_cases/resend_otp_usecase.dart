import '../../../../core/errors/app_failure.dart';
import '../../../../core/use_cases/use_case.dart';
import '../params/resend_otp_params.dart';
import '../repositories/auth_repository.dart';

// ✅ Concrete Use Case for Resending OTP
class ResendOtpUseCase implements UseCase<String, ResendOtpParams> {
  final AuthRepository repository;

  ResendOtpUseCase({required this.repository});

  @override
  FutureEitherFailOr<String> call({required ResendOtpParams params}) {
    return repository.resendOtp(
      mobile: params.mobileNumber,
      isLogin: params.isLogin,
    );
  }
}
