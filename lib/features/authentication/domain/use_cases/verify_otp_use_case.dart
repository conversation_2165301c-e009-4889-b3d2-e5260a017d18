import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/core/use_cases/use_case.dart';
import 'package:hodan_hospital/features/authentication/domain/repositories/auth_repository.dart';

import '../params/verify_otp_params.dart';

// ✅ Concrete Use Case for Verifying OTP
class VerifyOtpUseCase implements UseCase<String, VerifyOtpParams> {
  final AuthRepository repository;

  VerifyOtpUseCase({required this.repository});

  @override
  FutureEitherFailOr<String> call({required VerifyOtpParams params}) {
    return repository.verifyOtp(
      otp: params.otp,
      mobile: params.mobileNumber,
    );
  }
}
