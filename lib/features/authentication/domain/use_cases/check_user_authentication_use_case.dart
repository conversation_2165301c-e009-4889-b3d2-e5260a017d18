import 'package:hodan_hospital/core/enums/app_start_state.dart';
import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/core/params/no_params.dart';
import 'package:hodan_hospital/core/use_cases/use_case.dart';
import 'package:hodan_hospital/features/authentication/domain/repositories/auth_repository.dart';

// ✅ Concrete Use Case for Logout
class CheckUserAuthenticationUseCase
    implements UseCase<AppStartState, NoParams> {
  final AuthRepository repository;

  CheckUserAuthenticationUseCase({required this.repository});

  @override
  FutureEitherFailOr<AppStartState> call({required NoParams params}) {
    return repository.checkUserAuthentication();
  }
}
