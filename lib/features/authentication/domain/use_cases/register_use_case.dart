import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/core/use_cases/use_case.dart';
import 'package:hodan_hospital/features/authentication/domain/params/register_params.dart';
import 'package:hodan_hospital/features/authentication/domain/repositories/auth_repository.dart';

class RegisterUseCase implements UseCase<String, RegisterParams> {
  final AuthRepository repository;

  RegisterUseCase({required this.repository});

  @override
  FutureEitherFailOr<String> call({required RegisterParams params}) {
    return repository.register(
      patFullName: params.patFullName,
      patGender: params.patGender,
      patAge: params.patAge,
      // patAgeType: params.patAgeType,
      patMobileNumber: params.patMobileNumber,
      patDistrict: params.patDistrict,
    );
  }
}
