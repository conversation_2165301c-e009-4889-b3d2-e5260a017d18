import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/core/use_cases/use_case.dart';
import 'package:hodan_hospital/features/authentication/domain/params/can_register_params.dart';

import '../repositories/auth_repository.dart';

// ✅ Concrete Use Case for checking if this patient can be registered
class CanRegisterUsecase implements UseCase<String, CanRegisterParams> {
  final AuthRepository repository;

  CanRegisterUsecase({required this.repository});

  @override
  FutureEitherFailOr<String> call({required CanRegisterParams params}) async {
    return repository.canRegister(
      fullName: params.fullName,
      mobile: params.mobileNumber,
    );
  }
}
