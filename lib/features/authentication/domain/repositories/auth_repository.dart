import 'package:hodan_hospital/core/enums/app_start_state.dart';
import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/features/shared/domain/entities/user_entity.dart';

abstract class AuthRepository {
  FutureEitherFailOr<UserEntity> login({required String mobileNumber});
  FutureEitherFailOr<String> register({
    required String patFullName,
    required String patGender,
    required double patAge,
    // required String patAgeType,
    required String patMobileNumber,
    required String patDistrict,
  });

  FutureEitherFailOr<String> logout();

  FutureEitherFailOr<String> verifyOtp({
    required String otp,
    required String mobile,
  });
  FutureEitherFailOr<String> sendOtp({
    required String mobile,
    required bool isLogin,
  });
  FutureEitherFailOr<String> resendOtp({
    required String mobile,
    required bool isLogin,
  });

  FutureEitherFailOr<String> canRegister({
    required String fullName,
    required String mobile,
  });

  // FutureEitherFailOr<UserEntity> getCurrentUser();
  FutureEitherFailOr<AppStartState> checkUserAuthentication();
  FutureEitherFailOr<void> completeOnboarding();
}
