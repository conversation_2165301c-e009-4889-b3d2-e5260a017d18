import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hodan_hospital/core/config/theme/colors/app_colors_extension.dart';
import 'package:hodan_hospital/core/utils/extensions/app_bloc_extensions.dart';
import 'package:hodan_hospital/core/utils/helpers/dialog_helper.dart';
import 'package:hodan_hospital/features/appointment/presentation/pages/appointment_page.dart';
import 'package:hodan_hospital/features/shared/presentation/blocs%20or%20cubits/dialog%20cubit/dialog_cubit.dart';
import "package:hodan_hospital/gen/assets.gen.dart";
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hodan_hospital/features/home/<USER>/pages/home_page.dart';
import 'package:hodan_hospital/features/user/presentation/pages/profile_page.dart';

import '../../../shared/presentation/blocs or cubits/bottom nav cubit/bottom_nav_cubit.dart';
import '../../../shared/presentation/widgets/support_fab_action.dart';

class MainPage extends StatelessWidget {
  const MainPage({super.key});

  // Screens associated with bottom navigation
  static final List<Widget> _pages = [
    HomePage(),
    AppointmentPage(),
    // DoctorsPage(),
    ProfilePage(),
  ];

  @override
  Widget build(BuildContext context) {
    return BlocListener<DialogCubit, DialogState>(
      listener: listenDialogCubit,
      // ignore: deprecated_member_use
      child: WillPopScope(
        onWillPop: () async {
          return _onBackPressed(context);
        },
        child: Scaffold(
          body: BlocBuilder<BottomNavCubit, int>(
            builder: (context, selectedIndex) {
              return _pages[selectedIndex]; // Show selected screen
            },
          ),
          bottomNavigationBar: SafeArea(
            child: BottomNavigationBar(
              currentIndex: context.watch<BottomNavCubit>().state,
              onTap: (index) {
                context.read<BottomNavCubit>().changeTab(index);
              },
              showSelectedLabels: true,
              showUnselectedLabels: true,
              items: [
                _buildNavItem(
                  context: context,
                  iconPath: Assets.images.svg.home,
                  label: "Home",
                ),
                _buildNavItem(
                  context: context,
                  iconPath: Assets.images.svg.calender,
                  label: "Appointments",
                ),
                // _buildNavItem(
                //   context: context,
                //   iconPath: Assets.images.svg.doctor,
                //   label: "Doctors",
                // ),
                _buildNavItem(
                  context: context,
                  iconPath: Assets.images.svg.person,
                  label: "Profile",
                ),
              ],
            ),
          ),
          floatingActionButton: const SupportFABAction(),
          floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
        ),
      ),
    );
  }

  /// Handles back button behavior
  Future<bool> _onBackPressed(BuildContext context) async {
    final bottomNavCubit = context.bottomNavCubit;

    if (bottomNavCubit.state != 0) {
      // If NOT on Home tab, navigate back to Home
      bottomNavCubit.changeTab(0);
      return false; // Prevent app from exiting
    }

    // If already on Home, show exit confirmation dialog via DialogCubit
    final completer = Completer<bool>();

    context.read<DialogCubit>().showConfirmDialog(
          title: "Exit App",
          message: "Are you sure you want to exit the app?",
          confirmButtonText: "Exit",
          cancelButtonText: "Cancel",
          onConfirm: () => completer.complete(true), // User confirms exit
          onCancel: () => completer.complete(false), // User cancels exit
        );

    return completer.future; // Wait for dialog result
  }

  /// Reusable method for BottomNavigationBarItem
  static BottomNavigationBarItem _buildNavItem({
    required BuildContext context,
    required String iconPath,
    required String label,
  }) {
    final appColors = context.appColors;

    return BottomNavigationBarItem(
      icon: SvgPicture.asset(
        iconPath,
        width: 20.w,
        height: 20.h,
        colorFilter: ColorFilter.mode(
          appColors.subtextColor,
          BlendMode.srcIn,
        ),
      ),
      activeIcon: SvgPicture.asset(
        iconPath,
        width: 22.w,
        height: 22.h,
        colorFilter: ColorFilter.mode(
          appColors.primaryColor,
          BlendMode.srcIn,
        ),
      ),
      label: label,
      tooltip: label,
    );
  }
}
