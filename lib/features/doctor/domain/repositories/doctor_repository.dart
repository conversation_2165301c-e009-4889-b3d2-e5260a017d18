import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/features/doctor/domain/entities/banner_entity.dart';
import 'package:hodan_hospital/features/doctor/domain/entities/doctor_department_entity.dart';
import 'package:hodan_hospital/features/doctor/domain/entities/doctor_entity.dart';
import 'package:hodan_hospital/features/doctor/domain/entities/recent_search_entity.dart';

abstract class DoctorRepository {
  FutureEitherFailOr<List<DoctorEntity>> getDoctors({required bool forceFetch});

  FutureEitherFailOr<List<DoctorEntity>> searchDoctor({
    required String query,
    required List<DoctorEntity> doctors,
  });
  FutureEitherFailOr<List<BannerEntity>> getBunners({required bool forceFetch});

  FutureEitherFailOr<List<RecentSearchEntity>> getRecentSearches();
  FutureEitherFailOr<int> saveRecentSearch(
      {required RecentSearchEntity recentSearchModel});

  FutureEitherFailOr<List<DoctorDepartmentEntity>> getDoctorDepartments({
    required bool forceFetch,
  });

  FutureEitherFailOr<List<DoctorEntity>> getDoctorsByDepartment({
    required String department,
    required bool forceFetch,
  });

  //
}
