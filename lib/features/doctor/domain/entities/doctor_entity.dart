class DoctorEntity {
  final String name;
  final double consultingCharge;
  final String department;
  final String services;
  final String? image;
  final String experience;
  final String avialableTime;

  DoctorEntity({
    required this.name,
    required this.consultingCharge,
    required this.department,
    required this.services,
    required this.image,
    required this.experience,
    required this.avialableTime,
  });

  /// Returns a cleaned list of services without numbering or new lines.
  String get formattedServices {
    final text = services
        .replaceAll('\n', ', ') // Replace new lines with commas
        .split(',') // Split services by commas
        .map((service) => service.trim()) // Trim spaces
        .where((service) => service.isNotEmpty) // Remove empty entries
        .join(', '); // Join back into a single string with ", "
    // return text.isNotEmpty ? text : "No Services Available For Now.";
    return text;
  }

  /// Returns formatted doctor name with "Dr." prefix if missing.
  String get formattedDoctorName {
    if (!name.startsWith('Dr') && !name.startsWith('Drs')) {
      return 'Dr. $name';
    }
    return name;
  }

  /// Returns formatted experience with "Years" suffix if missing.
  String get formattedExperience {
    final experienceText = 'of Experience';
    if (experience.trim().isEmpty) {
      return experience;
    }
    if (!experience.endsWith('Years')) {
      return '$experience Years $experienceText.';
    }
    return '$experience $experienceText.';
  }

  /// Returns formatted available time with "AM" or "PM" suffix if missing.
  String get formattedAvailableTime {
    // if (!avialableTime.endsWith("AM") && !avialableTime.endsWith("PM")) {
    //   return "$avialableTime AM";
    // }
    return avialableTime;
  }
}
