import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/core/use_cases/use_case.dart';
import 'package:hodan_hospital/features/doctor/domain/entities/banner_entity.dart';
import 'package:hodan_hospital/features/doctor/domain/params/get_banners_params.dart';
import 'package:hodan_hospital/features/doctor/domain/repositories/doctor_repository.dart';

// ✅ Concrete Use Case for Fetching all doctors
class GetBannersUseCase
    implements UseCase<List<BannerEntity>, GetBannersParams> {
  final DoctorRepository repository;

  GetBannersUseCase({required this.repository});

  @override
  FutureEitherFailOr<List<BannerEntity>> call(
      {required GetBannersParams params}) {
    return repository.getBunners(forceFetch: params.forceFetch);
  }
}
