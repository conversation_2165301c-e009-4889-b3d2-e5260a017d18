import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/core/use_cases/use_case.dart';
import 'package:hodan_hospital/features/doctor/domain/entities/doctor_entity.dart';
import 'package:hodan_hospital/features/doctor/domain/params/search_doctor_params.dart';
import 'package:hodan_hospital/features/doctor/domain/repositories/doctor_repository.dart';

/// ✅ Concrete Use Case for Filtering doctors
class SearchDoctorUseCase
    implements UseCase<List<DoctorEntity>, SearchDoctorParams> {
  final DoctorRepository repository;

  SearchDoctorUseCase({required this.repository});

  @override
  FutureEitherFailOr<List<DoctorEntity>> call(
      {required SearchDoctorParams params}) {
    return repository.searchDoctor(
        doctors: params.doctors, query: params.query);
  }
}
