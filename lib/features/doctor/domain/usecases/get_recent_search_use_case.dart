import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/core/params/no_params.dart';
import 'package:hodan_hospital/core/use_cases/use_case.dart';
import 'package:hodan_hospital/features/doctor/domain/entities/recent_search_entity.dart';
import 'package:hodan_hospital/features/doctor/domain/repositories/doctor_repository.dart';

/// ✅ Concrete Use Case for fetching recent searches
class GetRecentSearchUseCase
    implements UseCase<List<RecentSearchEntity>, NoParams> {
  final DoctorRepository repository;

  GetRecentSearchUseCase({required this.repository});

  @override
  FutureEitherFailOr<List<RecentSearchEntity>> call(
      {required NoParams params}) {
    return repository.getRecentSearches();
  }
}
