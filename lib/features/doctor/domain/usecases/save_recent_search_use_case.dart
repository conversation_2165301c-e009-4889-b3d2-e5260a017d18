import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/core/use_cases/use_case.dart';
import 'package:hodan_hospital/features/doctor/domain/params/save_recent_search_params.dart';
import 'package:hodan_hospital/features/doctor/domain/repositories/doctor_repository.dart';

/// ✅ Concrete Use Case for saving recent searches
class SaveRecentSearchUseCase implements UseCase<int, SaveRecentSearchParams> {
  final DoctorRepository repository;

  SaveRecentSearchUseCase({required this.repository});

  @override
  FutureEitherFailOr<int> call({required SaveRecentSearchParams params}) {
    return repository.saveRecentSearch(
        recentSearchModel: params.recentSearchEntity);
  }
}
