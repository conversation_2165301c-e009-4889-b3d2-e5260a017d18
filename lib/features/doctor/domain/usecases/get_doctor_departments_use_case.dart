import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/core/use_cases/use_case.dart';
import 'package:hodan_hospital/features/doctor/domain/entities/doctor_department_entity.dart';
import 'package:hodan_hospital/features/doctor/domain/params/get_doctor_departments_params.dart';
import 'package:hodan_hospital/features/doctor/domain/repositories/doctor_repository.dart';

class GetDoctorDepartmentsUseCase
    extends UseCase<List<DoctorDepartmentEntity>, GetDoctorDepartmentsParams> {
  final DoctorRepository repository;

  GetDoctorDepartmentsUseCase({required this.repository});

  @override
  FutureEitherFailOr<List<DoctorDepartmentEntity>> call(
      {required GetDoctorDepartmentsParams params}) async {
    return repository.getDoctorDepartments(forceFetch: params.forceFetch);
  }
}
