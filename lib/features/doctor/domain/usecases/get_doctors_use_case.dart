import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/core/use_cases/use_case.dart';
import 'package:hodan_hospital/features/doctor/domain/entities/doctor_entity.dart';
import 'package:hodan_hospital/features/doctor/domain/params/get_doctors_params.dart';
import 'package:hodan_hospital/features/doctor/domain/repositories/doctor_repository.dart';

// ✅ Concrete Use Case for Fetching all doctors
class GetDoctorsUseCase
    implements UseCase<List<DoctorEntity>, GetDoctorsParams> {
  final DoctorRepository repository;

  GetDoctorsUseCase({required this.repository});

  @override
  FutureEitherFailOr<List<DoctorEntity>> call(
      {required GetDoctorsParams params}) {
    return repository.getDoctors(forceFetch: params.forceFetch);
  }
}
