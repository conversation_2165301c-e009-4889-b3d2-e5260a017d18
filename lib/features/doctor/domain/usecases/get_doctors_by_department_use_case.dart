import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/core/use_cases/use_case.dart';
import 'package:hodan_hospital/features/doctor/domain/entities/doctor_entity.dart';
import 'package:hodan_hospital/features/doctor/domain/params/get_doctors_by_department_params.dart';
import 'package:hodan_hospital/features/doctor/domain/repositories/doctor_repository.dart';

class GetDoctorsByDepartmentUseCase
    extends UseCase<List<DoctorEntity>, GetDoctorsByDepartmentParams> {
  final DoctorRepository repository;

  GetDoctorsByDepartmentUseCase({required this.repository});

  @override
  FutureEitherFailOr<List<DoctorEntity>> call(
      {required GetDoctorsByDepartmentParams params}) async {
    return repository.getDoctorsByDepartment(
      department: params.department,
      forceFetch: params.forceFetch,
    );
  }
}
