import 'package:hodan_hospital/core/enums/parse_failure_type.dart';
import 'package:hodan_hospital/core/errors/app_failure.dart';
// import 'package:objectbox/objectbox.dart';

// @Entity()
class DoctorModel {
  // @Id()
  int id = 0;

  final String name;
  final double consultingCharge;
  final String department;
  final String services;
  final String? image;
  final String experience;
  final String avialableTime;

  DoctorModel({
    required this.name,
    required this.consultingCharge,
    required this.department,
    required this.services,
    required this.image,
    required this.experience,
    required this.avialableTime,
  });

  factory DoctorModel.fromJson(Map<String, dynamic> json) {
    try {
      // Extract name and ensure it starts with "Dr." or "Drs."
      // String name = json['practitioner_name'] ?? "N/A";
      // if (!name.startsWith("Dr.") && !name.startsWith("Drs.")) {
      //   name = "Dr. $name";
      // }
      return DoctorModel(
        // name: json['practitioner_name'] ?? '',
        name: json['name'] ?? '',
        consultingCharge:
            (json['op_consulting_charge'] as num?)?.toDouble() ?? 0,
        department: json['department'] ?? '',
        services: json['services'] ?? '',
        image: json['image'] ?? '',
        experience: json['experience'] ?? '',
        avialableTime: json['available_time'] ?? '',
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse DoctorModel : ${error.toString()} ',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: DoctorModel,
        stackTrace: stackTrace,
      );
    }
  }

  static List<DoctorModel> fromJsonList(List<dynamic> jsonList) {
    return jsonList
        .map((json) => DoctorModel.fromJson(json as Map<String, dynamic>))
        .toList();
  }
}
