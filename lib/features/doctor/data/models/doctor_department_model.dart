import 'package:hodan_hospital/core/enums/parse_failure_type.dart';
import 'package:hodan_hospital/core/errors/app_failure.dart';
// import 'package:objectbox/objectbox.dart';

// @Entity()
class DoctorDepartmentModel {
  // @Id(assignable: true)
  int id = 0;
  final String name;
  final String departmentImage;
  final String department;

  DoctorDepartmentModel({
    required this.name,
    required this.departmentImage,
    required this.department,
  });

  factory DoctorDepartmentModel.fromJson(Map<String, dynamic> json) {
    try {
      return DoctorDepartmentModel(
        name: json['name'],
        departmentImage: json['department_img'],
        department: json['department'],
      );
    } catch (e, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse DoctorDepartmentModel : ${e.toString()} ',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: DoctorDepartmentModel,
        stackTrace: stackTrace,
      );
    }
  }

  static List<DoctorDepartmentModel> fromJsonList(List<dynamic> jsonList) {
    return jsonList
        .map((json) => DoctorDepartmentModel.fromJson(json))
        .toList();
  }

  @override
  String toString() {
    return 'DoctorDepartmentModel(id: $id, name: $name, image: $departmentImage, description: $department)';
  }
}
