import 'package:hodan_hospital/features/doctor/domain/entities/doctor_entity.dart';
import 'package:hodan_hospital/features/doctor/data/models/doctor_model.dart';

class <PERSON><PERSON><PERSON><PERSON> {
  /// Converts a DoctorEntity to a DoctorModel
  static DoctorModel entityToModel(DoctorEntity entity) {
    return DoctorModel(
      name: entity.name,
      consultingCharge: entity.consultingCharge,
      department: entity.department,
      services: entity.services,
      image: entity.image,
      experience: entity.experience,
      avialableTime: entity.avialableTime,
    );
  }

  /// Converts a DoctorModel to a DoctorEntity
  static DoctorEntity modelToEntity(DoctorModel model) {
    return DoctorEntity(
      name: model.name,
      consultingCharge: model.consultingCharge,
      department: model.department,
      services: model.services,
      image: model.image,
      experience: model.experience,
      avialableTime: model.avialableTime,
    );
  }

  /// Converts a list of DoctorModel to a list of DoctorEntity
  static List<DoctorEntity> modelListToEntityList(List<DoctorModel> models) {
    return models.map((model) => modelToEntity(model)).toList();
  }

  /// Converts a list of DoctorEntity to a list of DoctorModel
  static List<DoctorModel> entityListToModelList(List<DoctorEntity> entities) {
    return entities.map((entity) => entityToModel(entity)).toList();
  }
}
