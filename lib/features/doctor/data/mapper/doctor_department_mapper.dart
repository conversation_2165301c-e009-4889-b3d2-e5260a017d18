import 'package:hodan_hospital/features/doctor/data/models/doctor_department_model.dart';
import 'package:hodan_hospital/features/doctor/domain/entities/doctor_department_entity.dart';

class DoctorDepartmentMapper {
  static DoctorDepartmentEntity modelToEntity(DoctorDepartmentModel model) {
    return DoctorDepartmentEntity(
      name: model.name,
      departmentImage: model.departmentImage,
      department: model.department,
    );
  }

  static DoctorDepartmentModel entityToModel(DoctorDepartmentEntity entity) {
    return DoctorDepartmentModel(
      name: entity.name,
      departmentImage: entity.departmentImage,
      department: entity.department,
    );
  }

  static List<DoctorDepartmentEntity> modelListToEntityList(
      List<DoctorDepartmentModel> modelList) {
    return modelList.map((model) => modelToEntity(model)).toList();
  }

  static List<DoctorDepartmentModel> entityListToModelList(
      List<DoctorDepartmentEntity> entityList) {
    return entityList.map((entity) => entityToModel(entity)).toList();
  }
}
