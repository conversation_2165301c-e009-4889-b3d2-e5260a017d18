import 'package:hodan_hospital/features/doctor/data/models/recent_search_model.dart';
import 'package:hodan_hospital/features/doctor/domain/entities/recent_search_entity.dart';

class RecentSearchMapper {
  /// Converts a RecentSearchEntity to a RecentSearchModel
  static RecentSearchModel entityToModel(RecentSearchEntity entity) {
    return RecentSearchModel(
      query: entity.query,
    );
  }

  /// Converts a RecentSearchModel to a RecentSearchEntity
  static RecentSearchEntity modelToEntity(RecentSearchModel model) {
    return RecentSearchEntity(
      query: model.query,
    );
  }

  /// Converts a list of RecentSearchModel to a list of RecentSearchEntity
  static List<RecentSearchEntity> modelListToEntityList(
      List<RecentSearchModel> models) {
    return models.map((model) => modelToEntity(model)).toList();
  }

  /// Converts a list of RecentSearchEntity to a list of RecentSearchModel
  static List<RecentSearchModel> entityListToModelList(
      List<RecentSearchEntity> entities) {
    return entities.map((entity) => entityToModel(entity)).toList();
  }
}
