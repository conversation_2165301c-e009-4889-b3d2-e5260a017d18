import 'package:fpdart/fpdart.dart';
import 'package:hodan_hospital/core/config/logger/app_logger.dart';
import 'package:hodan_hospital/core/errors/app_failure.dart';
// import 'package:hodan_hospital/features/doctor/data/datasources/local/doctor_local_data_source.dart';
import 'package:hodan_hospital/features/doctor/data/datasources/remote/doctor_remote_data_source.dart';
import 'package:hodan_hospital/features/doctor/data/mapper/banner_mapper.dart';
import 'package:hodan_hospital/features/doctor/data/mapper/doctor_department_mapper.dart';
import 'package:hodan_hospital/features/doctor/data/mapper/doctor_mapper.dart';
// import 'package:hodan_hospital/features/doctor/data/mapper/recent_search_mapper.dart';
import 'package:hodan_hospital/features/doctor/data/models/banner_model.dart';
import 'package:hodan_hospital/features/doctor/data/models/doctor_department_model.dart';
import 'package:hodan_hospital/features/doctor/data/models/doctor_model.dart';
import 'package:hodan_hospital/features/doctor/domain/entities/banner_entity.dart';
import 'package:hodan_hospital/features/doctor/domain/entities/doctor_department_entity.dart';
import 'package:hodan_hospital/features/doctor/domain/entities/doctor_entity.dart';
import 'package:hodan_hospital/features/doctor/domain/entities/recent_search_entity.dart';
import 'package:hodan_hospital/features/doctor/domain/repositories/doctor_repository.dart';

class DoctorRepositoryImpl implements DoctorRepository {
  final DoctorRemoteDataSource doctorRemoteDataSource;
  // final DoctorLocalDataSource doctorLocalDataSource;

  DoctorRepositoryImpl({
    required this.doctorRemoteDataSource,
    // required this.doctorLocalDataSource,
  });

  @override
  FutureEitherFailOr<List<DoctorEntity>> getDoctors(
      {required bool forceFetch}) async {
    try {
      // if (!forceFetch) {
      //   final cachedResponse = await _getCachedDoctors();
      //   if (cachedResponse.isRight()) {
      //     return cachedResponse;
      //   }
      // }

      final remoteResponse = await _fetchDoctorsFromServer();
      return await remoteResponse.fold(
        (failure) async {
          // final cachedResponse = await _getCachedDoctors();
          // if (cachedResponse.isRight()) {
          //   return cachedResponse;
          // }
          return left(failure);
        },
        (doctorModels) async {
          // await doctorLocalDataSource.saveDoctors(doctors: doctorModels);
          final doctorsEntity =
              DoctorMapper.modelListToEntityList(doctorModels);
          return right(doctorsEntity);
        },
      );
    } catch (error, stackTrace) {
      AppLogger().error(
        "Error fetching doctors from ${forceFetch ? 'Server' : 'Cache'}",
        error: error,
        stackTrace: stackTrace,
      );
      return left(UnexpectedFailure(
        message:
            'Failed to load doctors from ${forceFetch ? 'server' : 'cache'}',
        stackTrace: stackTrace,
      ));
    }
  }

  // FutureEitherFailOr<List<DoctorEntity>> _getCachedDoctors() async {
  //   final cachedResponse = await doctorLocalDataSource.readDoctors();
  //   return cachedResponse.map(
  //     (doctorModels) {
  //       final doctorsEntity = DoctorMapper.modelListToEntityList(doctorModels);
  //       return doctorsEntity;
  //     },
  //   );
  // }

  FutureEitherFailOr<List<DoctorModel>> _fetchDoctorsFromServer() async {
    return await doctorRemoteDataSource.getDoctors();
  }

  @override
  FutureEitherFailOr<List<DoctorEntity>> searchDoctor({
    required String query,
    required List<DoctorEntity> doctors,
  }) async {
    /// 🟢 **If query is empty, return the full list immediately**
    if (query.trim().isEmpty) {
      return right(doctors);
    }

    final filteredDoctors = doctors.where((doctor) {
      final queryLower = query.toLowerCase();
      return [
        doctor.name.toLowerCase(),
        doctor.department.toLowerCase(), // Include specialization
      ].any((field) => field.contains(queryLower));
    }).toList();

    return right(filteredDoctors);
  }

  @override
  FutureEitherFailOr<List<BannerEntity>> getBunners(
      {required bool forceFetch}) async {
    try {
      // if (!forceFetch) {
      //   final cachedResponse = await _getCachedBanners();
      //   if (cachedResponse.isRight()) {
      //     return cachedResponse;
      //   }
      // }

      final remoteResponse = await _fetchBannersFromServer();
      return await remoteResponse.fold(
        (failure) async {
          // final cachedResponse = await _getCachedBanners();
          // if (cachedResponse.isRight()) {
          //   return cachedResponse;
          // }
          return left(failure);
        },
        (bannerModels) async {
          // await doctorLocalDataSource.saveBanners(banners: bannerModels);
          final bannersEntity =
              BannerMapper.modelListToEntityList(bannerModels);
          return right(bannersEntity);
        },
      );
    } catch (error, stackTrace) {
      AppLogger().error(
        "Error fetching banners from ${forceFetch ? 'Server' : 'Cache'}",
        error: error,
        stackTrace: stackTrace,
      );
      return left(
        UnexpectedFailure(
          message:
              'Failed to load banners from ${forceFetch ? 'server' : 'cache'}',
          stackTrace: stackTrace,
        ),
      );
    }
  }

  // FutureEitherFailOr<List<BannerEntity>> _getCachedBanners() async {
  //   final cachedResponse = await doctorLocalDataSource.readBanners();
  //   return cachedResponse.map(
  //     (bannerModels) {
  //       final bannersEntity = BannerMapper.modelListToEntityList(bannerModels);
  //       return bannersEntity;
  //     },
  //   );
  // }

  FutureEitherFailOr<List<BannerModel>> _fetchBannersFromServer() async {
    return await doctorRemoteDataSource.getBunners();
  }

  @override
  FutureEitherFailOr<List<RecentSearchEntity>> getRecentSearches() async {
    // final response = await doctorLocalDataSource.getRecentSearches();
    // return response.fold(
    //   (failure) {
    //     //
    //     return left(failure);
    //   },
    //   (recentSearchModels) {
    //     //
    //     final recentSearchEntities =
    //         RecentSearchMapper.modelListToEntityList(recentSearchModels);
    //     return right(recentSearchEntities);
    //   },
    // );
    return right([]);
  }

  @override
  FutureEitherFailOr<int> saveRecentSearch(
      {required RecentSearchEntity recentSearchModel}) async {
    // final recentSearchEntities =
    // RecentSearchMapper.entityToModel(recentSearchModel);

    // return await doctorLocalDataSource.saveRecentSearch(
    //   recentSearchModel: recentSearchEntities,
    // );
    return right(0); // Placeholder for actual implementation
  }

  @override
  FutureEitherFailOr<List<DoctorDepartmentEntity>> getDoctorDepartments({
    required bool forceFetch,
  }) async {
    try {
      // if (!forceFetch) {
      //   final cachedResponse = await _getCachedDoctorDepartments();
      //   if (cachedResponse.isRight()) {
      //     return cachedResponse;
      //   }
      // }

      final remoteResponse = await _fetchDoctorDepartmentsFromServer();
      return await remoteResponse.fold(
        (failure) async {
          // final cachedResponse = await _getCachedDoctorDepartments();
          // if (cachedResponse.isRight()) {
          //   return cachedResponse;
          // }
          return left(failure);
        },
        (doctorDepartmentModels) async {
          // await _saveDoctorDepartmentsToLocal(
          // doctorDepartments: doctorDepartmentModels,
          // );

          // Convert models to entities
          final doctorDepartmentEntities =
              DoctorDepartmentMapper.modelListToEntityList(
                  doctorDepartmentModels);
          return right(doctorDepartmentEntities);
        },
      );
    } catch (error, stackTrace) {
      AppLogger().error(
        "Error fetching doctor departments from ${forceFetch ? 'Server' : 'Cache'}",
        error: error,
        stackTrace: stackTrace,
      );
      return left(UnexpectedFailure(
        message:
            'Failed to load doctor departments from ${forceFetch ? 'server' : 'cache'}',
        stackTrace: stackTrace,
      ));
    }
  }

  // FutureEitherFailOr<List<DoctorDepartmentEntity>>
  //     _getCachedDoctorDepartments() async {
  //   final cachedResponse = await doctorLocalDataSource.readDoctorDepartments();
  //   return cachedResponse.map(
  //     (doctorDepartmentModels) {
  //       final doctorDepartmentEntities =
  //           DoctorDepartmentMapper.modelListToEntityList(
  //               doctorDepartmentModels);
  //       return doctorDepartmentEntities;
  //     },
  //   );
  // }

  FutureEitherFailOr<List<DoctorDepartmentModel>>
      _fetchDoctorDepartmentsFromServer() async {
    return await doctorRemoteDataSource.getDoctorDepartments();
  }

  // FutureEitherFailOr<List<int>> _saveDoctorDepartmentsToLocal(
  //     {required List<DoctorDepartmentModel> doctorDepartments}) async {
  //   return await doctorLocalDataSource.saveDoctorDepartments(
  //     doctorDepartments: doctorDepartments,
  //   );
  // }

  @override
  FutureEitherFailOr<List<DoctorEntity>> getDoctorsByDepartment({
    required String department,
    required bool forceFetch,
  }) async {
    try {
      // if (!forceFetch) {
      //   final cachedResponse = await _getCachedDoctorsByDepartment(
      //     department: department,
      //   );
      //   if (cachedResponse.isRight()) {
      //     return cachedResponse;
      //   }
      // }

      final remoteResponse = await _fetchDoctorsByDepartmentFromServer(
        department: department,
      );
      return await remoteResponse.fold(
        (failure) async {
          // final cachedResponse = await _getCachedDoctorsByDepartment(
          //   department: department,
          // );
          // if (cachedResponse.isRight()) {
          //   return cachedResponse;
          // }
          return left(failure);
        },
        (doctorModels) async {
          /// 🟢 **No need to save**
          // await doctorLocalDataSource.saveDoctors(doctors: doctorModels);
          final doctorsEntity =
              DoctorMapper.modelListToEntityList(doctorModels);
          return right(doctorsEntity);
        },
      );
    } catch (error, stackTrace) {
      AppLogger().error(
        'Error fetching doctors by department $department',
        error: error,
        stackTrace: stackTrace,
      );
      return left(UnexpectedFailure(
        message: 'Failed to load doctors by department $department',
        stackTrace: stackTrace,
      ));
    }
  }

  FutureEitherFailOr<List<DoctorModel>> _fetchDoctorsByDepartmentFromServer(
      {required String department}) async {
    return await doctorRemoteDataSource.getDoctorsByDepartment(
      department: department,
    );
  }

  // FutureEitherFailOr<List<DoctorEntity>> _getCachedDoctorsByDepartment(
  //     {required String department}) async {
  //   final cachedResponse = await doctorLocalDataSource.readDoctorsByDepartment(
  //     department: department,
  //   );
  //   return cachedResponse.map(
  //     (doctorModels) {
  //       final doctorsEntity = DoctorMapper.modelListToEntityList(doctorModels);
  //       return doctorsEntity;
  //     },
  //   );
  // }
}
