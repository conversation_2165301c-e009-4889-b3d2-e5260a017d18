// import 'package:hodan_hospital/core/enums/database_failure_type.dart';
// import 'package:hodan_hospital/core/enums/database_operation_type.dart';
// import 'package:hodan_hospital/core/errors/app_failure.dart';
// import 'package:hodan_hospital/core/errors/database_error_handler.dart';
// import 'package:hodan_hospital/features/doctor/data/models/banner_model.dart';
// import 'package:hodan_hospital/features/doctor/data/models/doctor_department_model.dart';
// import 'package:hodan_hospital/features/doctor/data/models/doctor_model.dart';
// import 'package:hodan_hospital/features/doctor/data/models/recent_search_model.dart';
// // import 'package:hodan_hospital/objectbox.g.dart';

// abstract class DoctorDatabaseManager {
//   FutureEitherFailOr<List<int>> saveDoctors({
//     required List<DoctorModel> doctors,
//   });
//   FutureEitherFailOr<List<DoctorModel>> readDoctors();
//   // FutureEitherFailOr<bool> addDoctorToFavorite({required DoctorModel doctor});
//   // FutureEitherFailOr<bool> removeDoctorFromFavorite(
//   // {required DoctorModel doctor});

//   FutureEitherFailOr<List<int>> saveBanners({
//     required List<BannerModel> banners,
//   });
//   FutureEitherFailOr<List<BannerModel>> readBanners();

//   FutureEitherFailOr<List<RecentSearchModel>> getRecentSearches();
//   FutureEitherFailOr<int> saveRecentSearch({
//     required RecentSearchModel recentSearchModel,
//   });

//   FutureEitherFailOr<List<DoctorDepartmentModel>> readDoctorDepartments();
//   FutureEitherFailOr<List<int>> saveDoctorDepartments({
//     required List<DoctorDepartmentModel> doctorDepartments,
//   });

//   FutureEitherFailOr<List<DoctorModel>> readDoctorsByDepartment({
//     required String department,
//   });
// }

// class DoctorDatabaseManagerImpl implements DoctorDatabaseManager {
//   final DatabaseErrorHandler databaseErrorHandler;

//   DoctorDatabaseManagerImpl({required this.databaseErrorHandler});

//   /// 🟢 **Save Doctors**
//   @override
//   FutureEitherFailOr<List<int>> saveDoctors({
//     required List<DoctorModel> doctors,
//   }) async {
//     return await databaseErrorHandler.handleDatabaseOperation(
//       operationType: DatabaseOperationType.write,
//       operationFunction: () async {
//         // await _deleteDoctors();
//         // final doctorBox =
//         //     await databaseErrorHandler.databaseManager.getBox<DoctorModel>();

//         // // Insert or Update doctor
//         // return doctorBox.putMany(doctors);
//         return []; // Simulating successful save with a dummy ID
//       },
//     );
//   }

//   /// 🔴 **Delete All Doctors**
//   FutureEitherFailOr<bool> _deleteDoctors() async {
//     return await databaseErrorHandler.handleDatabaseOperation(
//       operationType: DatabaseOperationType.delete,
//       operationFunction: () async {
//         // final doctorBox =
//         // await databaseErrorHandler.databaseManager.getBox<DoctorModel>();

//         // doctorBox.removeAll();

//         return true;
//       },
//     );
//   }

//   /// 🟡 **Read Doctors**
//   @override
//   FutureEitherFailOr<List<DoctorModel>> readDoctors() async {
//     return await databaseErrorHandler.handleDatabaseOperation(
//       operationType: DatabaseOperationType.read,
//       operationFunction: () async {
//         // final doctorBox =
//         // await databaseErrorHandler.databaseManager.getBox<DoctorModel>();

//         // Find doctors and return
//         // final doctors = doctorBox.getAll();
//         // if (doctors.isNotEmpty) {
//         //   // return doctors.reversed.toList();
//         //   return doctors;
//         // }

//         throw DatabaseFailure(
//           message: 'No doctors found in the database',
//           failureType: DatabaseFailureType.noDataFound,
//           stackTrace: StackTrace.current,
//         );
//       },
//     );
//   }

//   /// 🟢 **Add Doctor to Favorite**
//   // @override
//   // FutureEitherFailOr<bool> addDoctorToFavorite({required DoctorModel doctor}) async {
//   //   return await databaseErrorHandler.handleDatabaseOperation(
//   //     operationType: DatabaseOperationType.write,
//   //     operationFunction: () async {
//   //       final favoriteBox = await databaseErrorHandler.databaseManager
//   //           .getBox<FavoriteModel>();

//   //       return favoriteBox.put(doctor);
//   //     },
//   //   );
//   // }

//   /// 🔴 **Delete All Banners**
//   FutureEitherFailOr<bool> _deleteBanners() async {
//     return await databaseErrorHandler.handleDatabaseOperation(
//       operationType: DatabaseOperationType.delete,
//       operationFunction: () async {
//         // final bannerBox =
//         //     await databaseErrorHandler.databaseManager.getBox<BannerModel>();

//         // bannerBox.removeAll();

//         return true;
//       },
//     );
//   }

//   /// 🟡 **Read Banners**
//   @override
//   FutureEitherFailOr<List<BannerModel>> readBanners() async {
//     return await databaseErrorHandler.handleDatabaseOperation(
//       operationType: DatabaseOperationType.read,
//       operationFunction: () async {
//         // final bannerBox =
//         //     await databaseErrorHandler.databaseManager.getBox<BannerModel>();

//         // // Find banners and return
//         // final doctors = bannerBox.getAll();
//         // if (doctors.isNotEmpty) {
//         //   return doctors;
//         // }

//         throw DatabaseFailure(
//           message: 'No banners found in the database',
//           failureType: DatabaseFailureType.noDataFound,
//           stackTrace: StackTrace.current,
//         );
//       },
//     );
//   }

//   /// 🟢 **Save Banners**
//   @override
//   FutureEitherFailOr<List<int>> saveBanners({
//     required List<BannerModel> banners,
//   }) async {
//     return await databaseErrorHandler.handleDatabaseOperation(
//       operationType: DatabaseOperationType.write,
//       operationFunction: () async {
//         //   await _deleteBanners();
//         //   final bannerBox =
//         //       await databaseErrorHandler.databaseManager.getBox<BannerModel>();

//         //   // Insert or Update banners
//         //   return bannerBox.putMany(banners);
//         // },
//         // );
//         return []; // Simulating successful save with a dummy ID
//       },
//     );
//   }

//   /// 🟢 **Save RecentSearch Data**
//   @override
//   FutureEitherFailOr<int> saveRecentSearch({
//     required RecentSearchModel recentSearchModel,
//   }) async {
//     return await databaseErrorHandler.handleDatabaseOperation(
//       operationType: DatabaseOperationType.write,
//       operationFunction: () async {
//         // await _deleteRecentSearchesData(query: recentSearchModel.query);
//         // final recentSearchBox = await databaseErrorHandler.databaseManager
//         //     .getBox<RecentSearchModel>();

//         // final existingSearch = recentSearchBox
//         //     .query(RecentSearchModel_.query.equals(recentSearchModel.query))
//         //     .build()
//         //     .findFirst();

//         // if (existingSearch == null) {
//         //   return recentSearchBox.put(recentSearchModel);
//         // }
//         //
//         // else {
//         // return null; // No need to update
//         // }
//         return 1; // Simulating successful save with a dummy ID
//       },
//     );
//   }

//   /// 🔴 **Delete RecentSearch Data**
//   FutureEitherFailOr<bool> _deleteRecentSearchesData({
//     required String query,
//   }) async {
//     return await databaseErrorHandler.handleDatabaseOperation(
//       operationType: DatabaseOperationType.delete,
//       operationFunction: () async {
//         // final userBox = await databaseErrorHandler.databaseManager
//         //     .getBox<RecentSearchModel>();

//         // // Find RecentSearch by query
//         // final recentSearchModel = userBox
//         //     .query(RecentSearchModel_.query.equals(query))
//         //     .build()
//         //     .findFirst();

//         // if (recentSearchModel == null) {
//         //   return false; // User not found
//         // }

//         // userBox.remove(recentSearchModel.id);
//         return true;
//       },
//     );
//   }

//   /// 🟡 **Read RecentSearch Data**
//   @override
//   FutureEitherFailOr<List<RecentSearchModel>> getRecentSearches() async {
//     return await databaseErrorHandler.handleDatabaseOperation(
//       operationType: DatabaseOperationType.read,
//       operationFunction: () async {
//         // final recentSearchBox = await databaseErrorHandler.databaseManager
//         //     .getBox<RecentSearchModel>();

//         // final data = recentSearchBox.getAll().reversed.toList();

//         // return data;
//         return []; // Simulating empty list for recent searches
//       },
//     );
//   }

//   /// 🟢 **Save Doctor Departments**
//   @override
//   FutureEitherFailOr<List<int>> saveDoctorDepartments({
//     required List<DoctorDepartmentModel> doctorDepartments,
//   }) async {
//     return await databaseErrorHandler.handleDatabaseOperation(
//       operationType: DatabaseOperationType.write,
//       operationFunction: () async {
//         // await _deleteDoctorDepartments();
//         // final doctorDepartmentBox = await databaseErrorHandler.databaseManager
//         //     .getBox<DoctorDepartmentModel>();

//         // return doctorDepartmentBox.putMany(doctorDepartments);
//         return []; // Simulating successful save with a dummy ID
//       },
//     );
//   }

//   /// 🔴 **Delete All Doctor Departments**
//   FutureEitherFailOr<bool> _deleteDoctorDepartments() async {
//     return await databaseErrorHandler.handleDatabaseOperation(
//       operationType: DatabaseOperationType.delete,
//       operationFunction: () async {
//         // final doctorDepartmentBox = await databaseErrorHandler.databaseManager
//         //     .getBox<DoctorDepartmentModel>();

//         // doctorDepartmentBox.removeAll();

//         return true;
//       },
//     );
//   }

//   /// 🟡 **Read Doctor Departments**
//   @override
//   FutureEitherFailOr<List<DoctorDepartmentModel>>
//       readDoctorDepartments() async {
//     return await databaseErrorHandler.handleDatabaseOperation(
//       operationType: DatabaseOperationType.read,
//       operationFunction: () async {
//         // final doctorDepartmentBox = await databaseErrorHandler.databaseManager
//         //     .getBox<DoctorDepartmentModel>();

//         // return doctorDepartmentBox.getAll();

//         throw DatabaseFailure(
//           message: 'No doctor departments found in the database',
//           failureType: DatabaseFailureType.noDataFound,
//           stackTrace: StackTrace.current,
//         );
//       },
//     );
//   }

//   /// 🟡 **Read Doctors By Department**
//   @override
//   FutureEitherFailOr<List<DoctorModel>> readDoctorsByDepartment({
//     required String department,
//   }) async {
//     return await databaseErrorHandler.handleDatabaseOperation(
//       operationType: DatabaseOperationType.read,
//       operationFunction: () async {
//         // final doctorBox =
//         //     await databaseErrorHandler.databaseManager.getBox<DoctorModel>();

//         // final doctors = doctorBox
//         //     .query(DoctorModel_.department.equals(department))
//         //     .build()
//         //     .find();

//         // if (doctors.isNotEmpty) {
//         //   return doctors;
//         // }

//         throw DatabaseFailure(
//           message: 'No doctors found in the $department department',
//           failureType: DatabaseFailureType.noDataFound,
//           stackTrace: StackTrace.current,
//         );
//       },
//     );
//   }
// }
