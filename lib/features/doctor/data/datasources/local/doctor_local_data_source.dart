// import 'package:hodan_hospital/core/errors/app_failure.dart';
// import 'package:hodan_hospital/features/doctor/data/datasources/local/doctor_database_manager.dart';
// import 'package:hodan_hospital/features/doctor/data/models/banner_model.dart';
// import 'package:hodan_hospital/features/doctor/data/models/doctor_department_model.dart';
// import 'package:hodan_hospital/features/doctor/data/models/doctor_model.dart';
// import 'package:hodan_hospital/features/doctor/data/models/recent_search_model.dart';

// abstract class DoctorLocalDataSource {
//   FutureEitherFailOr<List<int>> saveDoctors(
//       {required List<DoctorModel> doctors});
//   FutureEitherFailOr<List<DoctorModel>> readDoctors();

//   FutureEitherFailOr<List<int>> saveBanners(
//       {required List<BannerModel> banners});
//   FutureEitherFailOr<List<BannerModel>> readBanners();

//   FutureEitherFailOr<List<RecentSearchModel>> getRecentSearches();
//   FutureEitherFailOr<int> saveRecentSearch(
//       {required RecentSearchModel recentSearchModel});

//   FutureEitherFailOr<List<int>> saveDoctorDepartments(
//       {required List<DoctorDepartmentModel> doctorDepartments});
//   FutureEitherFailOr<List<DoctorDepartmentModel>> readDoctorDepartments();

//   FutureEitherFailOr<List<DoctorModel>> readDoctorsByDepartment(
//       {required String department});
// }

// class DoctorLocalDataSourceImpl implements DoctorLocalDataSource {
//   // final DoctorDatabaseManager doctorDatabaseManager;

//   DoctorLocalDataSourceImpl({
//     // required this.doctorDatabaseManager
//     });

//   @override
//   @override
//   FutureEitherFailOr<List<DoctorModel>> readDoctors() async {
//     return await doctorDatabaseManager.readDoctors();
//   }

//   @override
//   FutureEitherFailOr<List<int>> saveDoctors(
//       {required List<DoctorModel> doctors}) async {
//     return await doctorDatabaseManager.saveDoctors(doctors: doctors);
//   }

//   @override
//   FutureEitherFailOr<List<BannerModel>> readBanners() async {
//     return await doctorDatabaseManager.readBanners();
//   }

//   @override
//   FutureEitherFailOr<List<int>> saveBanners(
//       {required List<BannerModel> banners}) async {
//     return await doctorDatabaseManager.saveBanners(banners: banners);
//   }

//   @override
//   FutureEitherFailOr<List<RecentSearchModel>> getRecentSearches() async {
//     return await doctorDatabaseManager.getRecentSearches();
//   }

//   @override
//   FutureEitherFailOr<int> saveRecentSearch(
//       {required RecentSearchModel recentSearchModel}) async {
//     return await doctorDatabaseManager.saveRecentSearch(
//       recentSearchModel: recentSearchModel,
//     );
//   }

//   @override
//   FutureEitherFailOr<List<DoctorDepartmentModel>>
//       readDoctorDepartments() async {
//     return await doctorDatabaseManager.readDoctorDepartments();
//   }

//   @override
//   FutureEitherFailOr<List<int>> saveDoctorDepartments(
//       {required List<DoctorDepartmentModel> doctorDepartments}) async {
//     return await doctorDatabaseManager.saveDoctorDepartments(
//       doctorDepartments: doctorDepartments,
//     );
//   }

//   @override
//   FutureEitherFailOr<List<DoctorModel>> readDoctorsByDepartment(
//       {required String department}) async {
//     return await doctorDatabaseManager.readDoctorsByDepartment(
//         department: department);
//   }
// }
