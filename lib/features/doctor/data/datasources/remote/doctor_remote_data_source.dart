import 'package:fpdart/fpdart.dart';
import 'package:hodan_hospital/core/constants/api_end_points.dart';
import 'package:hodan_hospital/core/enums/http_method.dart';
import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/core/errors/http_error_handler.dart';
import 'package:hodan_hospital/core/network/api_client/dio_api_client.dart';
import 'package:hodan_hospital/core/utils/helpers/request_data.dart';
import 'package:hodan_hospital/core/utils/helpers/response_handler.dart';
import 'package:hodan_hospital/features/doctor/data/models/banner_model.dart';
import 'package:hodan_hospital/features/doctor/data/models/doctor_department_model.dart';
import 'package:hodan_hospital/features/doctor/data/models/doctor_model.dart';

abstract class DoctorRemoteDataSource {
  FutureEitherFailOr<List<DoctorModel>> getDoctors();
  FutureEitherFailOr<List<BannerModel>> getBunners();
  FutureEitherFailOr<List<DoctorDepartmentModel>> getDoctorDepartments();
  FutureEitherFailOr<List<DoctorModel>> getDoctorsByDepartment({
    required String department,
  });
}

class DoctorRemoteDataSourceImpl implements DoctorRemoteDataSource {
  final DioApiClient dioApiClient;
  final HttpErrorHandler httpErrorHandler;

  DoctorRemoteDataSourceImpl({
    required this.dioApiClient,
    required this.httpErrorHandler,
  });

  @override
  FutureEitherFailOr<List<DoctorModel>> getDoctors() async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (data) => DoctorModel.fromJsonList(data as List<dynamic>),
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.get,
        endPointUrl: ApiEndpoints.getDoctors,
      ),
    );

    return ResponseHandler<List<DoctorModel>>(response)
        .handleResponseAndExtractData(
      onFailure: (failure) => left(failure),
      onSuccess: (data) => right(data),
    );
  }

  @override
  FutureEitherFailOr<List<BannerModel>> getBunners() async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (data) => BannerModel.fromJsonList(data as List<dynamic>),
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.get,
        endPointUrl: ApiEndpoints.getBanners,
      ),
    );
    return ResponseHandler<List<BannerModel>>(response)
        .handleResponseAndExtractData(
      onFailure: (failure) => left(failure),
      onSuccess: (data) => right(data),
    );
  }

  @override
  FutureEitherFailOr<List<DoctorDepartmentModel>> getDoctorDepartments() async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (data) =>
          DoctorDepartmentModel.fromJsonList(data as List<dynamic>),
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.get,
        endPointUrl: ApiEndpoints.getDoctorDepartments,
      ),
    );
    return ResponseHandler<List<DoctorDepartmentModel>>(response)
        .handleResponseAndExtractData(
      onFailure: (failure) => left(failure),
      onSuccess: (data) => right(data),
    );
  }

  @override
  FutureEitherFailOr<List<DoctorModel>> getDoctorsByDepartment({
    required String department,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (data) => DoctorModel.fromJsonList(data as List<dynamic>),
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.get,
        endPointUrl: ApiEndpoints.getDoctorDepartments,
        data: RequestData.json({
          "department": department,
        }),
      ),
    );
    return ResponseHandler<List<DoctorModel>>(response)
        .handleResponseAndExtractData(
      onFailure: (failure) => left(failure),
      onSuccess: (data) => right(data),
    );
  }
}
