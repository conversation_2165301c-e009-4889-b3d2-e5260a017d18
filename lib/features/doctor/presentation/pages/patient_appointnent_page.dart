// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hodan_hospital/core/config/theme/colors/app_colors_extension.dart';
import 'package:hodan_hospital/core/enums/image_type.dart';
import 'package:hodan_hospital/core/enums/loading_type.dart';
import 'package:hodan_hospital/core/utils/extensions/app_bloc_extensions.dart';
import 'package:hodan_hospital/core/utils/extensions/build_context_extensions.dart';
import 'package:hodan_hospital/core/utils/helpers/bottom_sheet_helper.dart';
import 'package:hodan_hospital/features/doctor/domain/entities/doctor_entity.dart';
import 'package:hodan_hospital/features/doctor/presentation/pages/patient_appointment_confirm_page.dart';
import 'package:hodan_hospital/features/shared/data/models/gender.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/animations/animated_item_wrapper.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_app_bar.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_button.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_image_picker_card.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_list_grid_view.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_textfield.dart';
import 'package:hodan_hospital/features/user/domain/entities/district_entity.dart';
import 'package:hodan_hospital/features/user/domain/entities/users_by_phone_entity.dart';
import 'package:hodan_hospital/features/user/presentation/bloc/user_bloc.dart';

import '../../../../core/utils/helpers/snack_bar_helper.dart';

class PatientAppointnentPage extends StatefulWidget {
  final DoctorEntity doctor;
  const PatientAppointnentPage({super.key, required this.doctor});

  @override
  State<PatientAppointnentPage> createState() => _PatientAppointnentPageState();
}

class _PatientAppointnentPageState extends State<PatientAppointnentPage> {
  final appointmentFormKey = GlobalKey<FormState>();
  final TextEditingController _pIDController = TextEditingController();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController patientNameController = TextEditingController();
  final TextEditingController ageController = TextEditingController();

  UsersByPhoneEntity? selectedUser;
  Gender? selectedGender;
  DistrictEntity? selectedDistrict;

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      final currentUser = context.userBloc.currentUser;
      final phoneNumber = currentUser?.phoneNumber ?? '';
      print('Current User Phone: $phoneNumber');
      context.userBloc.add(const GetDistrictsEvent(forceFetch: true));
      context.userBloc.add(
        GetUsersByPhoneNumberEvent(
          phoneNumber: phoneNumber,
          doctorName: widget.doctor.name,
        ),
      );

      // Wait for users to load, then set the first user by default
      context.userBloc.stream.firstWhere(
        (state) {
          return state is UsersLoaded;
        },
      ).then((_) {
        if (mounted) {
          final firstUser = context.userBloc.usersByPhone.firstOrNull;
          if (firstUser != null) {
            setState(() {
              selectedUser = firstUser;
              _pIDController.text = selectedUser?.pID ?? '';
              _nameController.text = selectedUser?.firstName ?? '';
            });
          }
        }
      });
    });
  }

  @override
  void dispose() {
    _pIDController.dispose();
    _nameController.dispose();
    patientNameController.dispose();
    ageController.dispose();
    selectedUser = null;
    selectedGender = null;
    selectedDistrict = null;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = context.textTheme;
    final userBloc = context.userBloc;
    final currentUserPhone = userBloc.currentUser?.phoneNumber ?? '';

    return BlocConsumer<UserBloc, UserState>(
      listener: (context, state) {
        if (state is UserRegistrationLoading) {
          context.dialogCubit.showLoadingDialog();
        }

        if (state is UserFailure) {
          context.dialogCubit.closeDialog();

          //
          final message = state.appFailure.getErrorMessage();
          SnackBarHelper.showErrorSnackBar(
            context,
            message: message,
          );
        }

        if (state is UserRegistered) {
          context.dialogCubit.closeDialog();

          // refresh
          userBloc.add(GetUsersByPhoneNumberEvent(
            phoneNumber: currentUserPhone,
            doctorName: widget.doctor.name,
          ));

          // show success snackbar
          SnackBarHelper.showSuccessSnackBar(
            context,
            message: state.message,
          );

          // clear fields and pop
          patientNameController.clear();
          ageController.clear();
          Navigator.pop(context);
        }
      },
      builder: (context, state) {
        final usersByPhone = userBloc.usersByPhone;
        final isLoading = state is UserRegistrationLoading;
        return Scaffold(
          body: Form(
            key: appointmentFormKey,
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CustomAppBar(
                    title: 'Patient',
                    // trailing: ,
                    // make trailing add new patient button
                    trailing: InkWell(
                      onTap: () {
                        // context.userBloc
                        //     .add(GetDistrictsEvent(forceFetch: true));
                        // show add new patient bottom sheet
                        BottomSheetHelper.instance.showAddNewPatientBottomSheet(
                          context: context,
                          isLoading: isLoading,
                          isDismissible: !isLoading,
                          enableDrag: !isLoading,
                          ageController: ageController,
                          nameController: patientNameController,
                          onPatientAdd: () {
                            //  adding a new patient
                            userBloc.add(RegisterNewPatientEvent(
                              fullName: patientNameController.text.trim(),
                              mobileNumber: currentUserPhone,
                              age: double.parse(ageController.text.trim()),
                              ageType: 'Year',
                              district: selectedDistrict?.name ?? '',
                              gender: selectedGender?.genderType ?? '',
                            ));
                          },
                          onDistrictSelected: (value) {
                            setState(() {
                              selectedDistrict = value;
                            });
                          },
                          onGenderSelected: (value) {
                            setState(() {
                              selectedGender = value;
                            });
                          },
                          selectedDistrict: selectedDistrict,
                          selectedGender: selectedGender,
                        );
                      },
                      child: Container(
                        height: 32,
                        padding: const EdgeInsets.symmetric(horizontal: 12),
                        decoration: BoxDecoration(
                          color: context.appColors.primaryColor
                              .withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.person_add_rounded,
                              size: 16,
                              color: context.appColors.primaryColor,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'New Patient',
                              style: TextStyle(
                                color: context.appColors.primaryColor,
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  SizedBox(height: 20.h),
                  // CustomButton(
                  //   buttonText: "Add new",
                  //   width: context.screenWidth * 0.8,
                  //   height: 40,
                  //   onTap: () {
                  //     // show add new patient bottom sheet
                  //     BottomSheetHelper.instance.showAddNewPatientBottomSheet(
                  //       context: context,
                  //       isLoading: isLoading,
                  //       isDismissible: !isLoading,
                  //       enableDrag: !isLoading,
                  //       ageController: ageController,
                  //       nameController: patientNameController,
                  //       onPatientAdd: () {
                  //         //  adding a new patient
                  //         userBloc.add(RegisterNewPatientEvent(
                  //           fullName: patientNameController.text.trim(),
                  //           mobileNumber: currentUserPhone,
                  //           age: double.parse(ageController.text.trim()),
                  //           ageType: "Year",
                  //           district: selectedDistrict?.name ?? '',
                  //           gender: selectedGender?.genderType ?? '',
                  //         ));
                  //       },
                  //       onDistrictSelected: (value) {
                  //         setState(() {
                  //           selectedDistrict = value;
                  //         });
                  //       },
                  //       onGenderSelected: (value) {
                  //         setState(() {
                  //           selectedGender = value;
                  //         });
                  //       },
                  //       selectedDistrict: selectedDistrict,
                  //       selectedGender: selectedGender,
                  //     );
                  //   },
                  // ),
                  SizedBox(height: 20.h),
                  // DoctorInformationWidget(
                  //   butonIsNeeded: false,
                  //   widget: widget.doctor,
                  //   onTap: () {
                  //     //
                  //   },
                  // ),

                  SizedBox(height: 20.h),
                  Padding(
                    padding: EdgeInsets.only(left: 20.w),
                    child: AnimatedItemWrapper(
                        delay: const Duration(milliseconds: 500),
                        child: Text(
                          'Appointment For :',
                          style: textTheme.titleMedium,
                        )),
                  ),
                  SizedBox(height: 10.h),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 20.w),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(height: 10.h),
                        AnimatedItemWrapper(
                          delay: const Duration(milliseconds: 600),
                          child: CustomTextField(
                            isReadOnly: true,
                            controller: _pIDController,
                            labelText: 'Patient ID',
                            // validator: (value) {
                            //   if (value!.isEmpty) {
                            //     return 'Please enter patient id';
                            //   }
                            //   return null;
                            // },
                          ),
                        ),
                        SizedBox(height: 15.h),
                        AnimatedItemWrapper(
                          delay: const Duration(milliseconds: 700),
                          child: CustomTextField(
                            isReadOnly: true,
                            controller: _nameController,
                            labelText: 'Patient name',
                            // validator: (value) {
                            //   if (value!.isEmpty) {
                            //     return 'Please enter patient name';
                            //   }
                            //   return null;
                            // },
                          ),
                        ),
                        Container(
                          height: 15.h,
                          color: context.appColors.backgroundColor,
                        ),
                        Container(
                          width: double.infinity,
                          // color: Colors.grey,
                          color: context.appColors.backgroundColor,
                          padding: EdgeInsets.symmetric(horizontal: 20.w),
                          child: AnimatedItemWrapper(
                            delay: const Duration(milliseconds: 800),
                            child: Text(
                              'Who is the patient?',
                              style: textTheme.titleSmall,
                            ),
                          ),
                        ),

                        ///
                        SizedBox(
                          height: 250.h,
                          child: CustomListGridView(
                            items: usersByPhone,
                            isLoading: state is UserLoading,
                            isEmpty: state is UsersLoaded && state.user.isEmpty,
                            contentType: LoadingType.listView,
                            showFooter: false,
                            itemBuilder: (context, user) {
                              return Material(
                                color: Colors.transparent, // Prevent color leak
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(
                                      20.r), // Adjust the radius as needed
                                  child: Container(
                                    decoration: BoxDecoration(
                                      color: selectedUser == user
                                          ? context.appColors.primaryColor
                                              .withValues(alpha: 0.7)
                                          : Colors
                                              .transparent, // Only apply color if selected
                                      borderRadius: BorderRadius.circular(12.r),
                                    ),
                                    child: ListTile(
                                      tileColor: selectedUser == user
                                          ? context.appColors.primaryColor
                                              .withValues(alpha: 0.7)
                                          : null,
                                      selected: selectedUser == user,
                                      leading: SizedBox(
                                        width: 30.w,
                                        height: 30.h,
                                        child: CustomImagePickerCard(
                                          imageUrl: user.patientImage,
                                          userName: user.firstName,
                                          imageType: ImageType.profile,
                                          radius: 20,
                                        ),
                                      ),
                                      title: Text(
                                        user.firstName,
                                        style: context.textTheme.bodyMedium
                                            ?.copyWith(
                                          color: selectedUser == user
                                              ? context.appColors.whiteColor
                                              : context
                                                  .textTheme.bodyMedium?.color,
                                        ),
                                      ),
                                      onTap: () {
                                        setState(() {
                                          selectedUser = user;
                                          _pIDController.text = user.pID;
                                          _nameController.text = user.firstName;
                                        });
                                      },
                                    ),
                                  ),
                                ),
                              );
                            },
                            onRefresh: () {
                              context.userBloc.add(
                                GetUsersByPhoneNumberEvent(
                                  phoneNumber: context
                                          .userBloc.currentUser?.phoneNumber ??
                                      '',
                                  doctorName: widget.doctor.name,
                                ),
                              );
                            },
                          ),
                        ),
                        Container(
                          height: 20.h,
                          color: context.appColors.backgroundColor,
                        ),
                      ],
                    ),
                  ),

                  ///
                  AnimatedItemWrapper(
                    delay: const Duration(milliseconds: 900),
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 20.w),
                      child: CustomButton(
                        buttonText: 'Book Now',
                        onTap: () {
                          if (selectedUser == null) {
                            SnackBarHelper.showErrorSnackBar(
                              context,
                              message:
                                  'Please select a patient before proceeding with the appointment booking.',
                            );
                            return;
                          }

                          //
                          if (appointmentFormKey.currentState!.validate()) {
                            // Navigate to the booking page with the selected user's details
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) =>
                                    PatientAppointmentConfirmPage(
                                  doctor: widget.doctor,
                                  user: selectedUser!,
                                ),
                              ),
                            );
                          }
                        },
                      ),
                    ),
                  ),
                  SizedBox(height: 20.h), // Space at the bottom
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
