import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hodan_hospital/core/config/logger/app_logger.dart';
import 'package:hodan_hospital/core/config/router/extension/navigation_extension.dart';
import 'package:hodan_hospital/core/config/theme/colors/app_colors_extension.dart';
import 'package:hodan_hospital/core/constants/app_constants.dart';
import 'package:hodan_hospital/core/utils/extensions/app_bloc_extensions.dart';
import 'package:hodan_hospital/core/utils/extensions/app_date_extensions.dart';
import 'package:hodan_hospital/core/utils/extensions/build_context_extensions.dart';
import 'package:hodan_hospital/core/utils/helpers/bottom_sheet_helper.dart';
import 'package:hodan_hospital/features/appointment/presentation/bloc/appointment_bloc.dart';
import 'package:hodan_hospital/features/doctor/domain/entities/doctor_entity.dart';
import 'package:hodan_hospital/features/main/presentation/pages/main_page.dart';
import 'package:hodan_hospital/features/shared/presentation/blocs%20or%20cubits/shared%20bloc/shared_bloc.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_app_bar.dart';
import 'package:hodan_hospital/features/user/domain/entities/users_by_phone_entity.dart';
import 'package:table_calendar/table_calendar.dart';

class PatientAppointmentConfirmPage extends StatefulWidget {
  final DoctorEntity doctor;
  final UsersByPhoneEntity user;
  const PatientAppointmentConfirmPage({
    super.key,
    required this.doctor,
    required this.user,
  });

  @override
  State<PatientAppointmentConfirmPage> createState() =>
      _PatientAppointmentConfirmPageState();
}

class _PatientAppointmentConfirmPageState
    extends State<PatientAppointmentConfirmPage> {
  // ignore: prefer_final_fields
  DateTime _focusDay = DateTime.now();

  // Get today's date at midnight for comparison
  DateTime get _today {
    final now = DateTime.now();
    return DateTime(now.year, now.month, now.day);
  }

  String? selectedTime;

  String get formatedDate => _focusDay.toFormattedString();

  @override
  Widget build(BuildContext context) {
    final appointmentBloc = context.appointmentBloc;
    final dialogCubit = context.dialogCubit;
    return Scaffold(
      body: BlocConsumer<AppointmentBloc, AppointmentState>(
        listenWhen: (previousState, currentState) {
          return currentState is AppointmentSuccess ||
              currentState is MakeAppointmentLoading ||
              currentState is AppointmentFailure;
        },
        listener: (context, state) {
          if (state is MakeAppointmentLoading) {
            dialogCubit.showLoadingDialog(
              title: 'Please Wait',
              message: 'Scheduling your appointment, please wait...',
            );
          }

          if (state is AppointmentFailure) {
            final message = state.failure.getErrorMessage();
            AppLogger().warning(
              'Payment Failure is : ${state.failure.toString()}',
            );

            AppLogger().error('Payment Failure Meessage is : $message');

            dialogCubit.showErrorDialog(
              title: 'Appointment Failed',
              message: message,
            );
          }
          if (state is AppointmentSuccess) {
            appointmentBloc.add(
              FetchAppointmentEvent(
                mobileNo: context.userBloc.currentUser?.phoneNumber ?? '',
                forceFetch: true,
              ),
            );
            final contactPhone = AppConstants.phoneNumber;
            final appName = 'Hodan Hospital'; // AppConstants.appName;
            final mobile = context.userBloc.currentUser?.phoneNumber ?? '';
            final smsMessage = '''
Your appointment in $appName is confirmed.

Date: $formatedDate  
Doctor: ${widget.doctor.formattedDoctorName} (${widget.doctor.department})  
Location: Hodan Hospital  
Fee: ${widget.user.hasFollowUpOn(_focusDay) ? 'Free follow-up' : widget.user.hasMembership ? '\$${(widget.doctor.consultingCharge * 0.5).toStringAsFixed(2)} with membership' : '\$${widget.doctor.consultingCharge.toStringAsFixed(2)}'}  

Be on time. Call $contactPhone if needed.
''';

            final dialogMessage = '''
✅ Appointment Booked Successfully!  

Date: $formatedDate  
Doctor: ${widget.doctor.formattedDoctorName}

Fee: ${widget.user.hasFollowUpOn(_focusDay) ? 'Free follow-up visit' : widget.user.hasMembership ? '\$${(widget.doctor.consultingCharge * 0.5).toStringAsFixed(2)} (50% discount)' : '\$${widget.doctor.consultingCharge.toStringAsFixed(2)}'}  

Please arrive on time or early. Thank you''';

            // show success dialog
            dialogCubit.showSuccessDialog(
              // title: '🎉 Appointment Confirmed!',
              title: '  ',
              message: dialogMessage,
              confirmButtonText: 'Go Home',
              confirmButtonwidth: 150,
              // cancelButtonwidth: 80,
              onConfirm: () {
                context.popRoute();
                context.pushAndRemoveUntilRoute(const MainPage());
              },
            );
            // send sms

            context.sharedBloc.add(SendSMS(
              mobileNumber: mobile,
              message: smsMessage,
            ));
          }
        },
        builder: (context, state) {
          // print the current state
          return SingleChildScrollView(
            child: Column(
              children: [
                SizedBox(height: 10.h),
                //! title
                const CustomAppBar(title: 'Appointment'),

                SizedBox(height: 20.h),

                //! table calender
                AppointmentCalendar(
                  focusedDay: _focusDay,
                  firstDay: _today,
                  followUpDays: widget.user.followupRemaingDays,
                  enabledDayPredicate: (day) => !day.isBefore(_today),
                  selectedDayPredicate: (day) => isSameDay(day, _focusDay),
                  onDaySelected:
                      (DateTime selectedDay, DateTime focusedDay) async {
                    setState(() {
                      _focusDay = selectedDay;
                    });

                    final hasFollowUp = widget.user.hasFollowUpOn(selectedDay);
                    print('selectedDay : $selectedDay');
                    print('hasFollowUp : $hasFollowUp');

                    await _confirmAppointment(
                      context: context,
                      selectedDay: selectedDay,
                    );
                  },
                ),

                SizedBox(height: 50.h),
                Text('selected day is : $formatedDate'),
              ],
            ),
          );
        },
      ),
    );
  }

  Future<void> _confirmAppointment({
    required BuildContext context,
    required DateTime selectedDay,
  }) async {
    final doctorName = widget.doctor.name;
    final doctorAmount = widget.doctor.consultingCharge;
    final pID = widget.user.pID;
    final mobile = context.userBloc.currentUser?.phoneNumber ?? '';
    final appointmentDate =
        '${selectedDay.day}/${selectedDay.month}/${selectedDay.year}';

    BottomSheetHelper.instance.showAppointmentConfirmationBottomSheet(
      context: context,
      title: '📅 Appointment Confirmation',
      message:
          'Would you like to confirm your appointment? Tap **Confirm** to proceed.',
      confirmButtonText: 'Confirm Appointment',
      onConfirm: () {
        final formattedAppointmentDate = selectedDay.toFormattedString();
        final hasFollowUp = widget.user.hasFollowUpOn(selectedDay);
        print('hasFollowUp : $hasFollowUp');
        context.appointmentBloc.add(
          MakeAppointmentEvent(
            pID: pID,
            doctorPractitioner: doctorName,
            patientMobile: mobile,
            doctAmount: doctorAmount,
            hasFollowUp: hasFollowUp,
            appointmentDate: formattedAppointmentDate,
            hasMembership: widget.user.hasMembership,
          ),
        );
      },
      content: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTextSpan(
            textTheme: context.textTheme,
            label: 'Patient : ',
            value: widget.user.firstName,
          ),
          _buildTextSpan(
            textTheme: context.textTheme,
            label: 'Doctor : ',
            value: doctorName,
          ),
          SizedBox(height: 5.h),
          _buildTextSpan(
            textTheme: context.textTheme,
            label: 'Date : ',
            value: appointmentDate,
          ),
          SizedBox(height: 5.h),
          if (widget.user.hasFollowUpOn(selectedDay)) ...[
            _buildTextSpan(
              textTheme: context.textTheme,
              label: 'Consultation Fee : ',
              value: '\$0.00',
              valueStyle: context.textTheme.bodyMedium?.copyWith(
                color: Colors.green,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 5.h),
            Center(
              child: Chip(
                label: const Text('✅ FREE — Follow-up Appointment'),
                backgroundColor: Colors.green.withValues(alpha: 0.2),
                shape: const StadiumBorder(
                  side: BorderSide(color: Colors.green),
                ),
              ),
            ),
          ] else if (widget.user.hasMembership) ...[
            _buildTextSpan(
              textTheme: context.textTheme,
              label: 'Original Fee : ',
              value: '\$${doctorAmount.toStringAsFixed(2)}',
              valueStyle: context.textTheme.bodyMedium?.copyWith(
                decoration: TextDecoration.lineThrough,
              ),
            ),
            SizedBox(height: 5.h),
            _buildTextSpan(
              textTheme: context.textTheme,
              label: 'Discounted Fee (50%): ',
              value: '\$${(doctorAmount * 0.5).toStringAsFixed(2)}',
              valueStyle: context.textTheme.bodyMedium?.copyWith(
                color: Colors.green, // Highlight discount
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 5.h),
            Center(
              child: Chip(
                label: const Text('MEMBERSHIP DISCOUNT APPLIED'),
                backgroundColor: Colors.green.withValues(alpha: 0.2),
                shape: const StadiumBorder(
                  side: BorderSide(color: Colors.green),
                ),
              ),
            ),
          ] else
            _buildTextSpan(
              textTheme: context.textTheme,
              label: 'Consultation Fee : ',
              value: '\$${doctorAmount.toStringAsFixed(2)}',
            ),
          SizedBox(height: 5.h),
        ],
      ),
    );
  }

  Widget _buildTextSpan({
    required TextTheme textTheme,
    required String label,
    required String value,
    TextStyle? valueStyle,
  }) {
    return Padding(
      padding: EdgeInsets.only(left: 60.w),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          Expanded(
            child: Text(
              value,
              style: valueStyle ?? textTheme.bodyMedium,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}

class AppointmentCalendar extends StatelessWidget {
  final DateTime focusedDay;
  final DateTime? firstDay;
  final DateTime? lastDay;
  final Function(DateTime, DateTime) onDaySelected;
  final bool Function(DateTime)? enabledDayPredicate;
  final bool Function(DateTime)? selectedDayPredicate;
  final List<DateTime>? followUpDays;

  const AppointmentCalendar({
    super.key,
    required this.focusedDay,
    this.firstDay,
    this.lastDay,
    required this.onDaySelected,
    required this.enabledDayPredicate,
    required this.selectedDayPredicate,
    required this.followUpDays,
  });

  @override
  Widget build(BuildContext context) {
    print('followUpDays : $followUpDays');
    final appColors = context.appColors;
    final textTheme = Theme.of(context).textTheme;
    final today = DateTime.now();
    final defaultBuilders =
        const CalendarBuilders(); // Create default builders instance

    return TableCalendar(
      calendarStyle: CalendarStyle(
        selectedTextStyle: textTheme.bodyMedium!.copyWith(
          color: appColors.whiteColor,
        ),
        outsideTextStyle: textTheme.bodyMedium!.copyWith(
          color: appColors.subtextColor,
          fontSize: 15.sp,
        ),
        defaultTextStyle: textTheme.bodyMedium!,
        selectedDecoration: BoxDecoration(
          color: appColors.primaryColor,
          shape: BoxShape.circle,
        ),
        tablePadding: EdgeInsets.all(10.w),
      ),
      daysOfWeekStyle: DaysOfWeekStyle(
        weekdayStyle: textTheme.bodyMedium!,
        weekendStyle: textTheme.bodyMedium!.copyWith(
          color: appColors.subtextColor,
        ),
      ),
      headerStyle: HeaderStyle(
        headerMargin: EdgeInsets.only(
          left: 20.w,
          right: 20.h,
          bottom: 5.h,
        ),
        formatButtonVisible: false,
        titleCentered: true,
        titleTextStyle: textTheme.titleMedium!.copyWith(
          color: appColors.whiteColor,
        ),
        leftChevronIcon: Icon(
          Icons.arrow_back_ios,
          color: appColors.whiteColor,
        ),
        rightChevronIcon: Icon(
          Icons.arrow_forward_ios,
          color: appColors.whiteColor,
        ),
        decoration: BoxDecoration(
          color: appColors.primaryColor,
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(20.r),
          ),
        ),
      ),
      focusedDay: focusedDay,
      firstDay: firstDay ?? DateTime(today.year, today.month, today.day),
      lastDay: lastDay ?? DateTime(2035, 12, 31),
      enabledDayPredicate: enabledDayPredicate,
      selectedDayPredicate: selectedDayPredicate,
      onDaySelected: onDaySelected,
      calendarBuilders: CalendarBuilders(
        defaultBuilder: (context, day, focusedDay) {
          // final isFollowUpDay =
          //     followUpDays?.any((d) => isSameDay(d, day)) ?? false;
          final isFollowUpDay = followUpDays?.any((d) =>
                  d.year == day.year &&
                  d.month == day.month &&
                  d.day == day.day) ??
              false;

          if (isFollowUpDay) {
            return Container(
              margin: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: Colors.green.withOpacity(0.2),
                shape: BoxShape.circle,
                border: Border.all(color: Colors.green),
              ),
              child: Center(
                child: Text(
                  '${day.day}',
                  style: textTheme.bodyMedium?.copyWith(
                    color: Colors.green[800],
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            );
          }
          return null; // Let TableCalendar handle default rendering
        },
        todayBuilder: (context, day, focusedDay) {
          // final isFollowUpDay =
          //     followUpDays?.any((d) => isSameDay(d, day)) ?? false;
          final isFollowUpDay = followUpDays?.any((d) =>
                  d.year == day.year &&
                  d.month == day.month &&
                  d.day == day.day) ??
              false;

          if (isFollowUpDay) {
            return Container(
              margin: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: Colors.green.withOpacity(0.3),
                shape: BoxShape.circle,
                border: Border.all(color: Colors.green, width: 2),
              ),
              child: Center(
                child: Text(
                  '${day.day}',
                  style: textTheme.bodyMedium?.copyWith(
                    color: Colors.green[800],
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            );
          }

          // Default "today" styling if not follow-up
          return Container(
            margin: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: appColors.secondaryColor,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                '${day.day}',
                style: textTheme.bodyMedium?.copyWith(
                  color: appColors.whiteColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          );
        },
        selectedBuilder: (context, day, focusedDay) {
          final isFollowUpDay =
              followUpDays?.any((d) => isSameDay(d, day)) ?? false;
          final defaultSelected =
              defaultBuilders.selectedBuilder?.call(context, day, focusedDay);

          if (isFollowUpDay) {
            return Container(
              margin: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: appColors.primaryColor.withOpacity(0.8),
                shape: BoxShape.circle,
                border: Border.all(color: Colors.green, width: 2),
              ),
              child: Center(
                child: defaultSelected ??
                    Text(
                      '${day.day}',
                      style: textTheme.bodyMedium?.copyWith(
                        color: appColors.whiteColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
              ),
            );
          }
          return defaultSelected;
        },
      ),
    );
  }
}
