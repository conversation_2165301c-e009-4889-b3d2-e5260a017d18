part of 'doctor_bloc.dart';

abstract class Doctor<PERSON><PERSON> extends Equatable {
  const DoctorEvent();

  @override
  List<Object> get props => [];
}

class GetDoctors extends Doctor<PERSON>vent {
  final bool forceFetch;

  const GetDoctors({this.forceFetch = false});

  @override
  List<Object> get props => [forceFetch];
}

class GetBanners extends Doctor<PERSON>vent {
  final bool forceFetch;

  const GetBanners({this.forceFetch = false});

  @override
  List<Object> get props => [forceFetch];
}

class SearchDoctor extends DoctorEvent {
  final String query;

  const SearchDoctor({required this.query});

  @override
  List<Object> get props => [
        query,
      ];
}

class GetRecentSearch extends DoctorEvent {}

class SaveRecentSearch extends Doctor<PERSON>vent {
  final RecentSearchEntity recentSearchEntity;

  const SaveRecentSearch({required this.recentSearchEntity});

  @override
  List<Object> get props => [
        recentSearchEntity,
      ];
}

class GetDoctorDepartments extends Doctor<PERSON><PERSON> {
  final bool forceFetch;

  const GetDoctorDepartments({this.forceFetch = false});

  @override
  List<Object> get props => [forceFetch];
}

class GetDoctorsByDepartment extends DoctorEvent {
  final String department;
  final bool forceFetch;

  const GetDoctorsByDepartment({
    required this.department,
    this.forceFetch = false,
  });

  @override
  List<Object> get props => [department, forceFetch];
}
