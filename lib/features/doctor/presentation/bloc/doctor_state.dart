part of 'doctor_bloc.dart';

abstract class <PERSON><PERSON><PERSON> extends Equatable {
  const DoctorState();

  @override
  List<Object> get props => [];
}

class <PERSON><PERSON><PERSON>tial extends Doctor<PERSON>tate {}

class DoctorLoading extends Doctor<PERSON>tate {}

class BannersLoading extends Doctor<PERSON>tate {}

class RecentSearchSaving extends Doctor<PERSON><PERSON> {}

class RecentSearchesLoading extends Doctor<PERSON><PERSON> {}

class RecentSearchSaved extends Doctor<PERSON><PERSON> {}

class DoctorFilterLoading extends Doctor<PERSON><PERSON> {}

class DoctorLoaded extends DoctorState {
  final List<DoctorEntity> doctors;

  const DoctorLoaded({required this.doctors});

  @override
  List<Object> get props => [doctors];
}

class BannersLoaded extends DoctorState {
  final List<BannerEntity> banners;

  const BannersLoaded({required this.banners});

  @override
  List<Object> get props => [banners];
}

class BannerFailure extends DoctorState {
  final AppFailure failure;

  const BannerFailure({required this.failure});

  @override
  List<Object> get props => [failure];
}

class DoctorFiltered extends Doctor<PERSON>tate {
  final List<DoctorEntity> doctors;

  const DoctorFiltered({required this.doctors});

  @override
  List<Object> get props => [doctors];
}

class RecentSearchesLoaded extends Doctor<PERSON>tate {
  final List<RecentSearchEntity> recentSearches;

  const RecentSearchesLoaded({required this.recentSearches});

  @override
  List<Object> get props => [recentSearches];
}

class DoctorFailure extends DoctorState {
  final AppFailure failure;

  const DoctorFailure({required this.failure});

  @override
  List<Object> get props => [failure];
}

class DoctorDepartmentsLoading extends DoctorState {}

class DoctorDepartmentsLoaded extends DoctorState {
  final List<DoctorDepartmentEntity> doctorDepartments;

  const DoctorDepartmentsLoaded({required this.doctorDepartments});

  @override
  List<Object> get props => [doctorDepartments];
}

class DoctorsByDepartmentLoading extends DoctorState {}

class DoctorsByDepartmentLoaded extends DoctorState {
  final List<DoctorEntity> doctors;

  const DoctorsByDepartmentLoaded({required this.doctors});

  @override
  List<Object> get props => [doctors];
}
