import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/core/utils/helpers/bloc_helper.dart';
import 'package:hodan_hospital/features/user/domain/entities/district_entity.dart';
import 'package:hodan_hospital/features/user/domain/entities/users_by_phone_entity.dart';
import 'package:hodan_hospital/features/user/domain/params/get_district_params.dart';
import 'package:hodan_hospital/features/user/domain/params/get_users_by_phone_number_params.dart';
import 'package:hodan_hospital/features/user/domain/params/register_new_patient_params.dart';
import 'package:hodan_hospital/features/user/domain/usecases/get_current_user_use_case.dart';
import 'package:hodan_hospital/features/user/domain/usecases/get_districts_use_case.dart';
import 'package:hodan_hospital/features/user/domain/usecases/get_users_by_phone_number_use_case.dart';
import 'package:hodan_hospital/features/shared/domain/entities/user_entity.dart';
import 'package:hodan_hospital/features/user/domain/usecases/register_new_patient_use_case.dart';

import '../../domain/params/get_current_patient_params.dart';
import '../../domain/params/send_feedback_params.dart';
import '../../domain/usecases/send_feedback_usecase.dart';

part 'user_event.dart';
part 'user_state.dart';

class UserBloc extends Bloc<UserEvent, UserState> {
  final GetUsersByPhoneNumberUseCase getUsersByPhoneNumberUseCase;
  final GetCurrentUserUseCase getCurrentUserUseCase;
  final RegisterNewPatientUseCase registerNewPatientUseCase;
  final GetDistrictsUseCase getDistrictsUseCase;
  final SendFeedbackUseCase sendFeedbackUseCase;

  UserBloc({
    required this.getUsersByPhoneNumberUseCase,
    required this.getCurrentUserUseCase,
    required this.registerNewPatientUseCase,
    required this.getDistrictsUseCase,
    required this.sendFeedbackUseCase,
  }) : super(UserInitial()) {
    // ✅ Get Current User
    on<GetCurrentUserEvent>(
      _onGetCurrentUser,
      transformer: BlocHelper.debounceHelper(),
    );

    // ✅ Get Users By Phone Number
    on<GetUsersByPhoneNumberEvent>(
      _onGetUsersByPhoneNumber,
      transformer: BlocHelper.debounceHelper(),
    );

    // ✅ Register New Patient
    on<RegisterNewPatientEvent>(
      _onRegisterNewPatient,
      transformer: BlocHelper.debounceHelper(),
    );

    // ✅ Get Districts
    on<GetDistrictsEvent>(
      _onGetDistricts,
      transformer: BlocHelper.debounceHelper(),
    );

    // ✅ Send Feedback
    on<SendFeedbackEvent>(
      _onSendFeedback,
      transformer: BlocHelper.debounceHelper(),
    );
  }

  UserEntity? _currentUser;
  UserEntity? get currentUser => _currentUser;
  List<UsersByPhoneEntity> _usersByPhone = [];
  List<UsersByPhoneEntity> get usersByPhone => _usersByPhone;

  // Store districts in bloc
  List<DistrictEntity> _districts = [];
  List<DistrictEntity> get districts => _districts;

  // 🟢 Handle Get Current User
  Future<void> _onGetCurrentUser(
      GetCurrentUserEvent event, Emitter<UserState> emit) async {
    await BlocHelper.handleEventAndEmit<UserEntity, UserState>(
      emit: emit,
      loadingState: UserDataLoading(),
      callUseCase: getCurrentUserUseCase(
          params: GetCurrentPatientParams(
        forceFetch: event.forceFetch,
      )),
      onSuccess: (userEntity) {
        _currentUser = userEntity;
        return UserDataLoaded(user: userEntity);
      },
      onFailure: (failure) => UserFailure(appFailure: failure),
    );
  }

  // 🟢 Handle Get Current User
  Future<void> _onGetUsersByPhoneNumber(
      GetUsersByPhoneNumberEvent event, Emitter<UserState> emit) async {
    await BlocHelper.handleEventAndEmit<List<UsersByPhoneEntity>, UserState>(
      emit: emit,
      loadingState: UserLoading(),
      callUseCase: getUsersByPhoneNumberUseCase(
          params: GetUsersByPhoneNumberParams(
        mobileNumber: event.phoneNumber,
        doctorName: event.doctorName,
        forceFetch: event.forceFetch,
      )),
      onSuccess: (usersByPhone) {
        _usersByPhone = usersByPhone;
        return UsersLoaded(user: usersByPhone);
      },
      onFailure: (failure) => UserFailure(appFailure: failure),
    );
  }

  // 🟢 Handle Register New Patient
  Future<void> _onRegisterNewPatient(
      RegisterNewPatientEvent event, Emitter<UserState> emit) async {
    await BlocHelper.handleEventAndEmit<void, UserState>(
      emit: emit,
      loadingState: UserRegistrationLoading(),
      callUseCase: registerNewPatientUseCase(
          params: RegisterNewPatientParams(
        patFullName: event.fullName,
        patGender: event.gender,
        patAge: event.age,
        patAgeType: event.ageType,
        patMobileNumber: event.mobileNumber,
        patDistrict: event.district,
      )),
      onSuccess: (_) =>
          const UserRegistered(message: 'Patient Registered Successfully'),
      onFailure: (failure) => UserFailure(appFailure: failure),
    );
  }

  // 🟢 Handle Get Districts
  Future<void> _onGetDistricts(
    GetDistrictsEvent event,
    Emitter<UserState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<List<DistrictEntity>, UserState>(
      emit: emit,
      loadingState: DistrictsLoading(),
      callUseCase: getDistrictsUseCase(
          params: GetDistrictParams(forceFetch: event.forceFetch)),
      onSuccess: (districts) {
        _districts = districts;
        return DistrictsLoaded(districts: districts);
      },
      onFailure: (failure) => UserFailure(appFailure: failure),
    );
  }

  // 🟢 Handle Send Feedback
  Future<void> _onSendFeedback(
      SendFeedbackEvent event, Emitter<UserState> emit) async {
    await BlocHelper.handleEventAndEmit<String, UserState>(
      emit: emit,
      loadingState: FeedbackSending(),
      callUseCase: sendFeedbackUseCase(
          params: SendFeedbackParams(
        patientId: _currentUser?.pID ?? '',
        feedbackType: event.feedbackType,
        rating: event.rating,
        appFeedbackCategory: event.appFeedbackCategory,
        comments: event.comments,
        appVersion: event.appVersion,
        deviceInfo: event.deviceInfo,
      )),
      onSuccess: (message) => FeedbackSent(message: message),
      onFailure: (failure) => FeedbackFailure(appFailure: failure),
    );
  }
}
