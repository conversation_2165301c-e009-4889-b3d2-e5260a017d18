part of 'user_bloc.dart';

abstract class UserState extends Equatable {
  const UserState();

  @override
  List<Object> get props => [];
}

class UserInitial extends UserState {}

class UserLoading extends UserState {}

class UserDataLoading extends UserState {}

class UserRegistrationLoading extends UserState {}

class UserRegistered extends UserState {
  final String message;

  const UserRegistered({required this.message});

  @override
  List<Object> get props => [message];
}

class UserDataLoaded extends UserState {
  final UserEntity user;

  const UserDataLoaded({required this.user});

  @override
  List<Object> get props => [user];
}

class UsersLoaded extends UserState {
  final List<UsersByPhoneEntity> user;

  const UsersLoaded({required this.user});

  @override
  List<Object> get props => [user];
}

class UserFailure extends UserState {
  final AppFailure appFailure;

  const UserFailure({required this.appFailure});

  @override
  List<Object> get props => [appFailure];
}

class DistrictsLoading extends UserState {}

class DistrictsLoaded extends UserState {
  final List<DistrictEntity> districts;

  const DistrictsLoaded({required this.districts});

  @override
  List<Object> get props => [districts];
}


class FeedbackSending extends UserState {}

class FeedbackSent extends UserState {
  final String message;

  const FeedbackSent({required this.message});

  @override
  List<Object> get props => [message];
}

class FeedbackFailure extends UserState {
  final AppFailure appFailure;

  const FeedbackFailure({required this.appFailure});

  @override
  List<Object> get props => [appFailure];
}
