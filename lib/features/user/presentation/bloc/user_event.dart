part of 'user_bloc.dart';

abstract class UserEvent extends Equatable {
  const UserEvent();

  @override
  List<Object?> get props => [];
}

// ✅ Get Current user
class GetCurrentUserEvent extends UserEvent {
  final bool forceFetch;

  const GetCurrentUserEvent({
    this.forceFetch = false,
  });

  @override
  List<Object> get props => [forceFetch];
}

// ✅ Get users by phone number
class GetUsersByPhoneNumberEvent extends UserEvent {
  final String phoneNumber;
  final String doctorName;
  final bool forceFetch;

  const GetUsersByPhoneNumberEvent({
    required this.phoneNumber,
    required this.doctorName,
    // this.forceFetch = false,
    this.forceFetch = true,
  });

  @override
  List<Object> get props => [phoneNumber, forceFetch, doctorName];
}

// ✅ Register New Patient
class RegisterNewPatientEvent extends UserEvent {
  final String fullName;
  final String gender;
  final double age;
  final String ageType;
  final String mobileNumber;
  final String district;

  const RegisterNewPatientEvent({
    required this.fullName,
    required this.gender,
    required this.age,
    required this.ageType,
    required this.mobileNumber,
    required this.district,
  });

  @override
  List<Object> get props =>
      [fullName, gender, age, ageType, mobileNumber, district];
}

// ✅ Get Districts
class GetDistrictsEvent extends UserEvent {
  final bool forceFetch;

  const GetDistrictsEvent({
    this.forceFetch = false,
  });
}

// ✅ Send Feedback
class SendFeedbackEvent extends UserEvent {
  final String feedbackType;
  final double rating;
  final String? appFeedbackCategory;
  final String comments;
  final String appVersion;
  final String deviceInfo;

  const SendFeedbackEvent({
    required this.feedbackType,
    required this.rating,
    required this.appFeedbackCategory,
    required this.comments,
    required this.appVersion,
    required this.deviceInfo,
  });

  @override
  List<Object?> get props =>
      [feedbackType, rating, appFeedbackCategory, comments, appVersion, deviceInfo];
}
