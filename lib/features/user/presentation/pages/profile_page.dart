// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:hodan_hospital/core/config/di/dependency_injection.dart';
import 'package:hodan_hospital/core/config/router/extension/navigation_extension.dart';
import 'package:hodan_hospital/core/config/theme/colors/app_colors_extension.dart';
import 'package:hodan_hospital/core/database/database_manager.dart';
import 'package:hodan_hospital/core/enums/language_enum.dart';
import 'package:hodan_hospital/core/utils/extensions/app_bloc_extensions.dart';
import 'package:hodan_hospital/core/utils/extensions/build_context_extensions.dart';
import 'package:hodan_hospital/features/authentication/presentation/bloc/authentication_bloc.dart';
import 'package:hodan_hospital/features/authentication/presentation/pages/login_page.dart';
import 'package:hodan_hospital/features/shared/presentation/blocs%20or%20cubits/theme-cubit/theme_cubit.dart';
import 'package:hodan_hospital/features/user/presentation/pages/about_developer_page.dart';
import 'package:hodan_hospital/features/user/presentation/pages/contact_us_page.dart';
import 'package:hodan_hospital/features/user/presentation/pages/edit_profile_page.dart';
import 'package:hodan_hospital/features/user/presentation/pages/help_support_page.dart';
import 'package:hodan_hospital/features/user/presentation/widgets/profile_header.dart';
import 'package:hodan_hospital/features/user/presentation/widgets/profile_info_card.dart';
import 'package:hodan_hospital/features/user/presentation/widgets/profile_menu_item.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/animations/animated_item_wrapper.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_button.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/flexible_page_layout_widget.dart';
import 'package:package_info_plus/package_info_plus.dart';

class ProfilePage extends StatelessWidget {
  const ProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    final appColors = context.appColors;
    return Scaffold(
      body: Column(
        children: [
          // Fixed Header Section
          const FlexibleLayoutWidget(
            topChildHeight: 150,
            totalHeight: 300,
            topChildOverlayFactor: 0.3,
            topChildWidget: ProfileHeader(),
            bottomChildWidget: ProfileInfoCard(),
            bottomChildHorizontalPadding: 50,
          ),

          // Scrollable Content Section
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  SizedBox(height: 20.h),
                  ProfileMenuItem(
                    icon: FontAwesomeIcons.user,
                    title: 'Personal Info',
                    duration: const Duration(milliseconds: 600),
                    onTap: () {
                      //
                      context.pushRoute(const EditProfilePage());
                    },
                  ),
                  // SizedBox(height: 15.h),
                  // ProfileMenuItem(
                  //   title: 'Language',
                  //   icon: FontAwesomeIcons.globe,
                  //   duration: const Duration(milliseconds: 800),
                  //   onTap: () {
                  //     showLanguageSelectorBottomSheet(
                  //       context: context,
                  //       currentLanguage:
                  //           Language.english, // Replace with the actual selected one
                  //       onSelect: (selectedLang) {
                  //         // Handle language change
                  //         // print('Selected: ${selectedLang.name}');
                  //         // Example: context.setLocale(Locale(selectedLang.languageCode, selectedLang.countryCode));
                  //       },
                  //     );
                  //   },
                  // ),
                  SizedBox(height: 15.h),
                  ProfileMenuItem(
                    icon: FontAwesomeIcons.moon,
                    title: 'Dark Mode',
                    duration: const Duration(milliseconds: 1000),
                    trailing: Switch(
                      inactiveThumbColor: appColors.primaryColor,
                      inactiveTrackColor: appColors.surfaceColor,
                      activeColor: appColors.primaryColor,
                      value: context.read<ThemeCubit>().state == ThemeMode.dark,
                      onChanged: (isDark) {
                        context.read<ThemeCubit>().toggleTheme(isDark);
                      },
                    ),
                  ),
                  SizedBox(height: 15.h),
                  ProfileMenuItem(
                    icon: FontAwesomeIcons.circleQuestion,
                    title: 'Help & Support',
                    duration: const Duration(milliseconds: 1200),
                    onTap: () {
                      context.pushRoute(const HelpSupportPage());
                    },
                  ),
                  SizedBox(height: 15.h),
                  ProfileMenuItem(
                    icon: FontAwesomeIcons.envelope,
                    title: 'Contact Us',
                    duration: const Duration(milliseconds: 1400),
                    onTap: () {
                      context.pushRoute(const ContactUsPage());
                    },
                  ),

                  SizedBox(height: 15.h),
                  ProfileMenuItem(
                    icon: FontAwesomeIcons.code,
                    title: 'About Developer',
                    duration: const Duration(milliseconds: 1800),
                    onTap: () {
                      context.pushRoute(const AboutDeveloperPage());
                    },
                  ),
                  SizedBox(height: 15.h),
                  ProfileMenuItem(
                    icon: FontAwesomeIcons.circleInfo,
                    title: 'App Version',
                    duration: const Duration(milliseconds: 1600),
                    trailing: FutureBuilder<PackageInfo>(
                      future: PackageInfo.fromPlatform(),
                      builder: (context, snapshot) {
                        if (snapshot.connectionState ==
                            ConnectionState.waiting) {
                          return SizedBox(
                            width: 16.w,
                            height: 16.h,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                context.appColors.primaryColor,
                              ),
                            ),
                          );
                        } else if (snapshot.hasError) {
                          return Text(
                            'Unknown',
                            style: context.textTheme.bodySmall?.copyWith(
                              color: context.appColors.subtextColor,
                            ),
                          );
                        } else {
                          final packageInfo = snapshot.data!;
                          return Text(
                            'v${packageInfo.version}',
                            style: context.textTheme.bodyMedium?.copyWith(
                              color: context.appColors.primaryColor,
                              fontWeight: FontWeight.w600,
                            ),
                          );
                        }
                      },
                    ),
                  ),
                  SizedBox(height: 30.h),
                  _buildLogoutButton(context),
                  SizedBox(height: 20.h),
                  // AnimatedItemWrapper(
                  //   delay: const Duration(milliseconds: 1500),
                  //   child: FutureBuilder<PackageInfo>(
                  //     future: PackageInfo.fromPlatform(),
                  //     builder: (context, snapshot) {
                  //       if (snapshot.connectionState == ConnectionState.waiting) {
                  //         return CircularProgressIndicator();
                  //       } else if (snapshot.hasError) {
                  //         return Text('Error: ${snapshot.error}');
                  //       } else {
                  //         final packageInfo = snapshot.data!;
                  //         return Text(
                  //           'Version: ${packageInfo.version} / ${packageInfo.buildNumber}',
                  //           style: context.textTheme.bodyMedium,
                  //         );
                  //       }
                  //     },
                  //   ),
                  // ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  BlocListener<AuthenticationBloc, AuthenticationState> _buildLogoutButton(
      BuildContext context) {
    return BlocListener<AuthenticationBloc, AuthenticationState>(
      listener: (context, state) {
        if (state is Unauthenticated) {
          context.pushAndRemoveUntilRoute(const LoginPage());

          final dbManager = sl<DatabaseManager>();

          Future.microtask(() async {
            await dbManager.clearAllData();
            context.bottomNavCubit.changeTab(0);
          });
        }
      },
      child: AnimatedItemWrapper(
        delay: const Duration(milliseconds: 1500),
        child: CustomButton(
          buttonText: 'Logout',
          backgroundColor: context.appColors.errorColor,
          onTap: () {
            context.dialogCubit.showWarningDialog(
              title: 'Logout',
              message: 'Are you sure you want to logout?',
              confirmButtonwidth: 70,
              cancelButtonwidth: 80,
              onConfirm: () async {
                context.authenticationBloc.add(LogoutEvent());
              },
            );
          },
        ),
      ),
    );
  }

  void showLanguageSelectorBottomSheet({
    required BuildContext context,
    required Language currentLanguage,
    required Function(Language selectedLanguage) onSelect,
  }) {
    final appColors = context.appColors;
    final textTheme = context.textTheme;
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      builder: (context) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Select Language',
                style: context.textTheme.titleMedium,
              ),
              const SizedBox(height: 16),
              ...Language.values.map((lang) {
                final isSelected = lang == currentLanguage;

                return ListTile(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  selected: isSelected,
                  leading: Text(lang.flag, style: textTheme.titleLarge),
                  title: Text(lang.name),
                  trailing: isSelected
                      ? Icon(Icons.check_circle, color: appColors.successColor)
                      : null,
                  onTap: () {
                    Navigator.pop(context);
                    onSelect(lang);
                  },
                );
              }),
            ],
          ),
        );
      },
    );
  }
}
