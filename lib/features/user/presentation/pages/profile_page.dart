// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:hodan_hospital/core/config/di/dependency_injection.dart';
import 'package:hodan_hospital/core/config/router/extension/navigation_extension.dart';
import 'package:hodan_hospital/core/config/theme/colors/app_colors_extension.dart';
import 'package:hodan_hospital/core/database/database_manager.dart';
import 'package:hodan_hospital/core/enums/language_enum.dart';
import 'package:hodan_hospital/core/utils/extensions/app_bloc_extensions.dart';
import 'package:hodan_hospital/core/utils/extensions/build_context_extensions.dart';
import 'package:hodan_hospital/features/authentication/presentation/bloc/authentication_bloc.dart';
import 'package:hodan_hospital/features/authentication/presentation/pages/login_page.dart';
import 'package:hodan_hospital/features/shared/presentation/blocs%20or%20cubits/theme-cubit/theme_cubit.dart';
import 'package:hodan_hospital/features/user/presentation/pages/contact_us_page.dart';
import 'package:hodan_hospital/features/user/presentation/pages/edit_profile_page.dart';
import 'package:hodan_hospital/features/user/presentation/pages/help_support_page.dart';
import 'package:hodan_hospital/features/user/presentation/widgets/profile_header.dart';
import 'package:hodan_hospital/features/user/presentation/widgets/profile_info_card.dart';
import 'package:hodan_hospital/features/user/presentation/widgets/profile_menu_item.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/animations/animated_item_wrapper.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_button.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/flexible_page_layout_widget.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:url_launcher/url_launcher.dart';

class ProfilePage extends StatelessWidget {
  const ProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    final appColors = context.appColors;
    return Scaffold(
      body: SingleChildScrollView(
        child: Column(
          children: [
            const FlexibleLayoutWidget(
              topChildHeight: 150,
              totalHeight: 300,
              topChildOverlayFactor: 0.3,
              topChildWidget: ProfileHeader(),
              bottomChildWidget: ProfileInfoCard(),
              bottomChildHorizontalPadding: 50,
            ),
            SizedBox(height: 20.h),
            ProfileMenuItem(
              icon: FontAwesomeIcons.user,
              title: 'Personal Info',
              duration: const Duration(milliseconds: 600),
              onTap: () {
                //
                context.pushRoute(const EditProfilePage());
              },
            ),
            // SizedBox(height: 15.h),
            // ProfileMenuItem(
            //   title: 'Language',
            //   icon: FontAwesomeIcons.globe,
            //   duration: const Duration(milliseconds: 800),
            //   onTap: () {
            //     showLanguageSelectorBottomSheet(
            //       context: context,
            //       currentLanguage:
            //           Language.english, // Replace with the actual selected one
            //       onSelect: (selectedLang) {
            //         // Handle language change
            //         // print('Selected: ${selectedLang.name}');
            //         // Example: context.setLocale(Locale(selectedLang.languageCode, selectedLang.countryCode));
            //       },
            //     );
            //   },
            // ),
            SizedBox(height: 15.h),
            ProfileMenuItem(
              icon: FontAwesomeIcons.moon,
              title: 'Dark Mode',
              duration: const Duration(milliseconds: 1000),
              trailing: Switch(
                inactiveThumbColor: appColors.primaryColor,
                inactiveTrackColor: appColors.surfaceColor,
                activeColor: appColors.primaryColor,
                value: context.read<ThemeCubit>().state == ThemeMode.dark,
                onChanged: (isDark) {
                  context.read<ThemeCubit>().toggleTheme(isDark);
                },
              ),
            ),
            SizedBox(height: 15.h),
            ProfileMenuItem(
              icon: FontAwesomeIcons.circleQuestion,
              title: 'Help & Support',
              duration: const Duration(milliseconds: 1200),
              onTap: () {
                context.pushRoute(const HelpSupportPage());
              },
            ),
            SizedBox(height: 15.h),
            ProfileMenuItem(
              icon: FontAwesomeIcons.envelope,
              title: 'Contact Us',
              duration: const Duration(milliseconds: 1400),
              onTap: () {
                context.pushRoute(const ContactUsPage());
              },
            ),
            
            SizedBox(height: 15.h),
            ProfileMenuItem(
              icon: FontAwesomeIcons.code,
              title: 'About Developer',
              duration: const Duration(milliseconds: 1800),
              onTap: () {
                _showAboutDeveloperDialog(context);
              },
            ),
            SizedBox(height: 15.h),
            ProfileMenuItem(
              icon: FontAwesomeIcons.mobileScreenButton,
              title: 'App Version',
              duration: const Duration(milliseconds: 1600),
              trailing: FutureBuilder<PackageInfo>(
                future: PackageInfo.fromPlatform(),
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return SizedBox(
                      width: 16.w,
                      height: 16.h,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          context.appColors.primaryColor,
                        ),
                      ),
                    );
                  } else if (snapshot.hasError) {
                    return Text(
                      'Unknown',
                      style: context.textTheme.bodySmall?.copyWith(
                        color: context.appColors.subtextColor,
                      ),
                    );
                  } else {
                    final packageInfo = snapshot.data!;
                    return Text(
                      'v${packageInfo.version}',
                      style: context.textTheme.bodyMedium?.copyWith(
                        color: context.appColors.primaryColor,
                        fontWeight: FontWeight.w600,
                      ),
                    );
                  }
                },
              ),
            ),
            SizedBox(height: 30.h),
            _buildLogoutButton(context),
            SizedBox(height: 20.h),
            // AnimatedItemWrapper(
            //   delay: const Duration(milliseconds: 1500),
            //   child: FutureBuilder<PackageInfo>(
            //     future: PackageInfo.fromPlatform(),
            //     builder: (context, snapshot) {
            //       if (snapshot.connectionState == ConnectionState.waiting) {
            //         return CircularProgressIndicator();
            //       } else if (snapshot.hasError) {
            //         return Text('Error: ${snapshot.error}');
            //       } else {
            //         final packageInfo = snapshot.data!;
            //         return Text(
            //           'Version: ${packageInfo.version} / ${packageInfo.buildNumber}',
            //           style: context.textTheme.bodyMedium,
            //         );
            //       }
            //     },
            //   ),
            // ),
          ],
        ),
      ),
    );
  }

  BlocListener<AuthenticationBloc, AuthenticationState> _buildLogoutButton(
      BuildContext context) {
    return BlocListener<AuthenticationBloc, AuthenticationState>(
      listener: (context, state) {
        if (state is Unauthenticated) {
          context.pushAndRemoveUntilRoute(const LoginPage());

          final dbManager = sl<DatabaseManager>();

          Future.microtask(() async {
            await dbManager.clearAllData();
            context.bottomNavCubit.changeTab(0);
          });
        }
      },
      child: AnimatedItemWrapper(
        delay: const Duration(milliseconds: 1500),
        child: CustomButton(
          buttonText: 'Logout',
          backgroundColor: context.appColors.errorColor,
          onTap: () {
            context.dialogCubit.showWarningDialog(
              title: 'Logout',
              message: 'Are you sure you want to logout?',
              confirmButtonwidth: 70,
              cancelButtonwidth: 80,
              onConfirm: () async {
                context.authenticationBloc.add(LogoutEvent());
              },
            );
          },
        ),
      ),
    );
  }

  void showLanguageSelectorBottomSheet({
    required BuildContext context,
    required Language currentLanguage,
    required Function(Language selectedLanguage) onSelect,
  }) {
    final appColors = context.appColors;
    final textTheme = context.textTheme;
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      builder: (context) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Select Language',
                style: context.textTheme.titleMedium,
              ),
              const SizedBox(height: 16),
              ...Language.values.map((lang) {
                final isSelected = lang == currentLanguage;

                return ListTile(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  selected: isSelected,
                  leading: Text(lang.flag, style: textTheme.titleLarge),
                  title: Text(lang.name),
                  trailing: isSelected
                      ? Icon(Icons.check_circle, color: appColors.successColor)
                      : null,
                  onTap: () {
                    Navigator.pop(context);
                    onSelect(lang);
                  },
                );
              }),
            ],
          ),
        );
      },
    );
  }

  void _showAboutDeveloperDialog(BuildContext context) {
    final appColors = context.appColors;
    final textTheme = context.textTheme;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20.r),
          ),
          child: Container(
            padding: EdgeInsets.all(24.w),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20.r),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  appColors.primaryColor.withValues(alpha: 0.05),
                  appColors.primaryColor.withValues(alpha: 0.1),
                ],
              ),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Header with Icon
                Container(
                  padding: EdgeInsets.all(16.w),
                  decoration: BoxDecoration(
                    color: appColors.primaryColor.withValues(alpha: 0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    FontAwesomeIcons.code,
                    size: 32.sp,
                    color: appColors.primaryColor,
                  ),
                ),
                SizedBox(height: 20.h),

                // Title
                Text(
                  'About Developer',
                  style: textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: appColors.textColor,
                  ),
                ),
                SizedBox(height: 16.h),

                // Company Info
                Container(
                  padding: EdgeInsets.all(16.w),
                  decoration: BoxDecoration(
                    color: appColors.whiteColor,
                    borderRadius: BorderRadius.circular(12.r),
                    border: Border.all(
                      color: appColors.primaryColor.withValues(alpha: 0.2),
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Company Description
                      Text(
                        'We are a leading technology company specializing in comprehensive ERP solutions since 2016. Our expertise spans across multiple industries, providing tailored digital solutions that streamline operations and enhance efficiency.',
                        style: textTheme.bodyMedium?.copyWith(
                          color: appColors.textColor,
                          height: 1.5,
                        ),
                        textAlign: TextAlign.justify,
                      ),
                      SizedBox(height: 16.h),

                      // Services Section
                      Text(
                        'Our Services:',
                        style: textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: appColors.primaryColor,
                        ),
                      ),
                      SizedBox(height: 8.h),

                      // Service Items
                      _buildServiceItem(
                          context, '🏥', 'Hospital Management Systems'),
                      _buildServiceItem(
                          context, '🎓', 'School Management Systems'),
                      _buildServiceItem(
                          context, '📱', 'Mobile App Development'),
                      _buildServiceItem(context, '⚙️', 'Custom ERP Solutions'),
                      _buildServiceItem(
                          context, '🔧', 'Frappe Framework Development'),

                      SizedBox(height: 16.h),

                      // Technology Stack
                      Container(
                        padding: EdgeInsets.all(12.w),
                        decoration: BoxDecoration(
                          color: appColors.primaryColor.withValues(alpha: 0.05),
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              FontAwesomeIcons.gears,
                              size: 16.sp,
                              color: appColors.primaryColor,
                            ),
                            SizedBox(width: 8.w),
                            Text(
                              'Built with Frappe Framework & Flutter',
                              style: textTheme.bodySmall?.copyWith(
                                color: appColors.primaryColor,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 24.h),

                // Action Buttons
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () => Navigator.of(context).pop(),
                        icon: Icon(
                          FontAwesomeIcons.xmark,
                          size: 16.sp,
                        ),
                        label: const Text('Close'),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: appColors.subtextColor,
                          side: BorderSide(color: appColors.dividerColor),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12.r),
                          ),
                          padding: EdgeInsets.symmetric(vertical: 12.h),
                        ),
                      ),
                    ),
                    SizedBox(width: 12.w),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () => _launchWebsite(),
                        icon: Icon(
                          FontAwesomeIcons.globe,
                          size: 16.sp,
                        ),
                        label: const Text('Visit Website'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: appColors.primaryColor,
                          foregroundColor: appColors.whiteColor,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12.r),
                          ),
                          padding: EdgeInsets.symmetric(vertical: 12.h),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildServiceItem(BuildContext context, String emoji, String service) {
    return Padding(
      padding: EdgeInsets.only(bottom: 4.h),
      child: Row(
        children: [
          Text(emoji, style: TextStyle(fontSize: 14.sp)),
          SizedBox(width: 8.w),
          Expanded(
            child: Text(
              service,
              style: context.textTheme.bodySmall?.copyWith(
                color: context.appColors.textColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _launchWebsite() async {
    const url =
        'https://frappe.io'; // You can replace this with your company website
    final Uri uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    }
  }
}
