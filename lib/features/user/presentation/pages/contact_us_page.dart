import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:hodan_hospital/core/constants/app_constants.dart';
import 'package:hodan_hospital/core/utils/extensions/build_context_extensions.dart';
import 'package:hodan_hospital/core/utils/helpers/snack_bar_helper.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/animations/animated_app_bar.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_container.dart';
import 'package:url_launcher/url_launcher.dart';

class ContactUsPage extends StatelessWidget {
  const ContactUsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const AnimatedAppBar(title: 'Contact Us'),
      body: Padding(
        padding: const EdgeInsets.all(20),
        child: ListView(
          children: [
            _contactTile(
              context: context,
              icon: Icons.email,
              title: 'Email',
              subtitle: 'Send us an email for inquiries or support',
              onTap: () async {
                final email = '<EMAIL>';
                final subject = Uri.encodeComponent('Support Request');
                final body = Uri.encodeComponent(
                    'Hello Hodan Hospital,\n\nI need help with...');
                final url = 'mailto:$email?subject=$subject&body=$body';
                await _launchUrl(context, url);
              },
            ),
            const SizedBox(height: 12),
            _contactTile(
              context: context,
              icon: Icons.phone,
              title: 'Phone',
              subtitle: 'Call us directly using our short code: 1234',
              onTap: () async {
                final telUrl = 'tel:${AppConstants.phoneNumber}';
                await _launchUrl(context, telUrl);
              },
            ),
            const SizedBox(height: 12),
            _contactTile(
              context: context,
              icon: FontAwesomeIcons.whatsapp,
              title: 'WhatsApp',
              subtitle: 'Chat with us on WhatsApp for quick support',
              onTap: () async {
                final whatsAppUrl =
                    'https://wa.me/${AppConstants.whatsappNumber}';
                await _launchUrl(context, whatsAppUrl);
              },
            ),
            const SizedBox(height: 12),
            _contactTile(
              context: context,
              icon: Icons.location_on,
              title: 'Address',
              subtitle: 'Visit us at Abdiqasim Street, Mogadisho, Somalia',
              onTap: () async {
                final latitude = 2.0370514;
                final longitude = 45.3107854;
                final googleMapsUrl =
                    'https://www.google.com/maps/search/?api=1&query=$latitude,$longitude';
                await _launchUrl(context, googleMapsUrl);
              },
            ),
            const SizedBox(height: 12),
            _contactTile(
              context: context,
              icon: Icons.language,
              title: 'Website',
              subtitle: 'Explore our services and departments online',
              onTap: () async {
                await _launchUrl(context, 'https://hodanhospital.com');
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _contactTile({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String subtitle,
    VoidCallback? onTap,
  }) {
    final textTheme = context.textTheme;
    return CustomContainer(
      padding: const EdgeInsets.symmetric(horizontal: 10),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(vertical: 8),
        leading: Icon(icon, size: 28),
        title: Text(
          title,
          style: textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w900),
        ),
        subtitle: Text(subtitle, style: textTheme.bodyMedium),
        onTap: onTap,
      ),
    );
  }

  Future<void> _launchUrl(BuildContext context, String url) async {
    final uri = Uri.parse(url);
    try {
      if (await canLaunchUrl(uri)) {
        final launched = await launchUrl(uri);
        if (!launched) {
          _showError(context, 'Could not launch URL');
        }
      } else {
        _showError(context, 'This action is not supported on your device.');
      }
    } catch (e) {
      _showError(context, 'Error launching URL: $e');
    }
  }

  void _showError(BuildContext context, String message) {
    SnackBarHelper.showErrorSnackBar(context, message: message);
  }
}
