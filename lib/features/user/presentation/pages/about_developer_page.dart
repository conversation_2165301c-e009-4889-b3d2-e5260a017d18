import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:hodan_hospital/core/config/theme/colors/app_colors_extension.dart';
import 'package:hodan_hospital/core/utils/extensions/build_context_extensions.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/animations/animated_item_wrapper.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_app_bar.dart';
import 'package:url_launcher/url_launcher.dart';

class AboutDeveloperPage extends StatelessWidget {
  const AboutDeveloperPage({super.key});

  @override
  Widget build(BuildContext context) {
    final appColors = context.appColors;
    final textTheme = context.textTheme;

    return Scaffold(
      backgroundColor: appColors.backgroundColor,
      body: Column(
        children: [
          const CustomAppBar(
            title: 'About Developer',
          ),
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  // Header Section with Company Branding
                  _buildHeaderSection(context, appColors, textTheme),

                  // Main Content
                  Padding(
                    padding: EdgeInsets.all(20.w),
                    child: Column(
                      children: [
                        // Company Overview Card
                        _buildCompanyOverviewCard(
                            context, appColors, textTheme),
                        SizedBox(height: 24.h),

                        // Services Grid
                        _buildServicesGrid(context, appColors, textTheme),
                        SizedBox(height: 24.h),

                        // Technology Stack Card
                        _buildTechnologyStackCard(
                            context, appColors, textTheme),
                        SizedBox(height: 24.h),

                        // Experience & Achievements Card
                        _buildExperienceCard(context, appColors, textTheme),
                        SizedBox(height: 24.h),

                        // Contact & Website Section
                        _buildContactSection(context, appColors, textTheme),
                        SizedBox(height: 40.h),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderSection(
      BuildContext context, dynamic appColors, TextTheme textTheme) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            appColors.primaryColor,
            appColors.primaryColor.withValues(alpha: 0.8),
          ],
        ),
      ),
      child: SafeArea(
        bottom: false,
        child: Padding(
          padding: EdgeInsets.fromLTRB(20.w, 20.h, 20.w, 40.h),
          child: Column(
            children: [
              AnimatedItemWrapper(
                delay: const Duration(milliseconds: 200),
                child: Container(
                  width: 100.w,
                  height: 100.h,
                  decoration: BoxDecoration(
                    color: appColors.whiteColor,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.2),
                        blurRadius: 15,
                        offset: const Offset(0, 8),
                      ),
                    ],
                  ),
                  child: Icon(
                    FontAwesomeIcons.code,
                    size: 50.sp,
                    color: appColors.primaryColor,
                  ),
                ),
              ),
              SizedBox(height: 20.h),
              AnimatedItemWrapper(
                delay: const Duration(milliseconds: 400),
                child: Text(
                  'ERP Solutions Provider',
                  style: textTheme.headlineMedium?.copyWith(
                    color: appColors.whiteColor,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              SizedBox(height: 8.h),
              AnimatedItemWrapper(
                delay: const Duration(milliseconds: 600),
                child: Text(
                  'Transforming businesses with innovative technology solutions since 2016',
                  style: textTheme.bodyLarge?.copyWith(
                    color: appColors.whiteColor.withValues(alpha: 0.9),
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCompanyOverviewCard(
      BuildContext context, dynamic appColors, TextTheme textTheme) {
    return AnimatedItemWrapper(
      delay: const Duration(milliseconds: 800),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(24.w),
        decoration: BoxDecoration(
          color: appColors.whiteColor,
          borderRadius: BorderRadius.circular(16.r),
          boxShadow: [
            BoxShadow(
              color: appColors.dividerColor.withValues(alpha: 0.3),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(12.w),
                  decoration: BoxDecoration(
                    color: appColors.primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: Icon(
                    FontAwesomeIcons.building,
                    color: appColors.primaryColor,
                    size: 24.sp,
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Company Overview',
                        style: textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: appColors.textColor,
                        ),
                      ),
                      Text(
                        'Founded in 2016',
                        style: textTheme.bodyMedium?.copyWith(
                          color: appColors.primaryColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(height: 20.h),
            Text(
              'We are a leading technology company specializing in comprehensive ERP (Enterprise Resource Planning) solutions. Our mission is to transform businesses across various industries by providing tailored digital solutions that streamline operations, enhance efficiency, and drive growth.',
              style: textTheme.bodyMedium?.copyWith(
                color: appColors.textColor,
                height: 1.6,
              ),
              textAlign: TextAlign.justify,
            ),
            SizedBox(height: 16.h),
            Text(
              'Our expertise spans multiple sectors including healthcare, education, and various other industries that require robust ERP systems. We believe in creating solutions that are not just functional, but also user-friendly and scalable.',
              style: textTheme.bodyMedium?.copyWith(
                color: appColors.textColor,
                height: 1.6,
              ),
              textAlign: TextAlign.justify,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildServicesGrid(
      BuildContext context, dynamic appColors, TextTheme textTheme) {
    final services = [
      {
        'icon': FontAwesomeIcons.hospital,
        'title': 'Hospital Management',
        'desc': 'Complete healthcare ERP solutions'
      },
      {
        'icon': FontAwesomeIcons.graduationCap,
        'title': 'School Management',
        'desc': 'Educational institution systems'
      },
      {
        'icon': FontAwesomeIcons.mobile,
        'title': 'Mobile Apps',
        'desc': 'Cross-platform mobile solutions'
      },
      {
        'icon': FontAwesomeIcons.gears,
        'title': 'Custom ERP',
        'desc': 'Tailored business solutions'
      },
      {
        'icon': FontAwesomeIcons.code,
        'title': 'Frappe Development',
        'desc': 'Framework-based applications'
      },
      {
        'icon': FontAwesomeIcons.cloud,
        'title': 'Cloud Solutions',
        'desc': 'Scalable cloud infrastructure'
      },
    ];

    return AnimatedItemWrapper(
      delay: const Duration(milliseconds: 1000),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(24.w),
        decoration: BoxDecoration(
          color: appColors.whiteColor,
          borderRadius: BorderRadius.circular(16.r),
          boxShadow: [
            BoxShadow(
              color: appColors.dividerColor.withValues(alpha: 0.3),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Our Services',
              style: textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: appColors.textColor,
              ),
            ),
            SizedBox(height: 20.h),
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: 16.w,
                mainAxisSpacing: 16.h,
                childAspectRatio: 1.1,
              ),
              itemCount: services.length,
              itemBuilder: (context, index) {
                final service = services[index];
                return Container(
                  padding: EdgeInsets.all(16.w),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        appColors.primaryColor.withValues(alpha: 0.05),
                        appColors.primaryColor.withValues(alpha: 0.1),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(12.r),
                    border: Border.all(
                      color: appColors.primaryColor.withValues(alpha: 0.2),
                    ),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        service['icon'] as IconData,
                        color: appColors.primaryColor,
                        size: 32.sp,
                      ),
                      SizedBox(height: 12.h),
                      Text(
                        service['title'] as String,
                        style: textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: appColors.textColor,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        service['desc'] as String,
                        style: textTheme.bodySmall?.copyWith(
                          color: appColors.subtextColor,
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTechnologyStackCard(
      BuildContext context, dynamic appColors, TextTheme textTheme) {
    final technologies = [
      {
        'name': 'Frappe Framework',
        'icon': FontAwesomeIcons.python,
        'color': Colors.blue
      },
      {
        'name': 'Flutter',
        'icon': FontAwesomeIcons.mobile,
        'color': Colors.cyan
      },
      {'name': 'Python', 'icon': FontAwesomeIcons.code, 'color': Colors.green},
      {
        'name': 'JavaScript',
        'icon': FontAwesomeIcons.js,
        'color': Colors.yellow
      },
      {
        'name': 'MySQL',
        'icon': FontAwesomeIcons.database,
        'color': Colors.orange
      },
      {'name': 'Docker', 'icon': FontAwesomeIcons.docker, 'color': Colors.blue},
    ];

    return AnimatedItemWrapper(
      delay: const Duration(milliseconds: 1200),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(24.w),
        decoration: BoxDecoration(
          color: appColors.whiteColor,
          borderRadius: BorderRadius.circular(16.r),
          boxShadow: [
            BoxShadow(
              color: appColors.dividerColor.withValues(alpha: 0.3),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(12.w),
                  decoration: BoxDecoration(
                    color: appColors.primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: Icon(
                    FontAwesomeIcons.gears,
                    color: appColors.primaryColor,
                    size: 24.sp,
                  ),
                ),
                SizedBox(width: 16.w),
                Text(
                  'Technology Stack',
                  style: textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: appColors.textColor,
                  ),
                ),
              ],
            ),
            SizedBox(height: 20.h),
            Wrap(
              spacing: 12.w,
              runSpacing: 12.h,
              children: technologies.map((tech) {
                return Container(
                  padding:
                      EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
                  decoration: BoxDecoration(
                    color: (tech['color'] as Color).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(20.r),
                    border: Border.all(
                      color: (tech['color'] as Color).withValues(alpha: 0.3),
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        tech['icon'] as IconData,
                        color: tech['color'] as Color,
                        size: 16.sp,
                      ),
                      SizedBox(width: 8.w),
                      Text(
                        tech['name'] as String,
                        style: textTheme.bodySmall?.copyWith(
                          color: appColors.textColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExperienceCard(
      BuildContext context, dynamic appColors, TextTheme textTheme) {
    return AnimatedItemWrapper(
      delay: const Duration(milliseconds: 1400),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(24.w),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              appColors.primaryColor.withValues(alpha: 0.05),
              appColors.primaryColor.withValues(alpha: 0.1),
            ],
          ),
          borderRadius: BorderRadius.circular(16.r),
          border: Border.all(
            color: appColors.primaryColor.withValues(alpha: 0.2),
          ),
        ),
        child: Column(
          children: [
            Icon(
              FontAwesomeIcons.trophy,
              color: appColors.primaryColor,
              size: 40.sp,
            ),
            SizedBox(height: 16.h),
            Text(
              'Experience & Achievements',
              style: textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: appColors.textColor,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 20.h),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                      context, appColors, textTheme, '8+', 'Years Experience'),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: _buildStatCard(context, appColors, textTheme, '100+',
                      'Projects Delivered'),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                      context, appColors, textTheme, '50+', 'Happy Clients'),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: _buildStatCard(
                      context, appColors, textTheme, '24/7', 'Support'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(BuildContext context, dynamic appColors,
      TextTheme textTheme, String number, String label) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: appColors.whiteColor,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: appColors.dividerColor.withValues(alpha: 0.2),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Text(
            number,
            style: textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: appColors.primaryColor,
            ),
          ),
          SizedBox(height: 4.h),
          Text(
            label,
            style: textTheme.bodySmall?.copyWith(
              color: appColors.subtextColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildContactSection(
      BuildContext context, dynamic appColors, TextTheme textTheme) {
    return AnimatedItemWrapper(
      delay: const Duration(milliseconds: 1600),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(24.w),
        decoration: BoxDecoration(
          color: appColors.whiteColor,
          borderRadius: BorderRadius.circular(16.r),
          boxShadow: [
            BoxShadow(
              color: appColors.dividerColor.withValues(alpha: 0.3),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            Text(
              'Get In Touch',
              style: textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: appColors.textColor,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              'Ready to transform your business with our ERP solutions?',
              style: textTheme.bodyMedium?.copyWith(
                color: appColors.subtextColor,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 24.h),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _launchWebsite(),
                    icon: Icon(
                      FontAwesomeIcons.globe,
                      size: 18.sp,
                    ),
                    label: const Text('Visit Website'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: appColors.primaryColor,
                      foregroundColor: appColors.whiteColor,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                      padding: EdgeInsets.symmetric(vertical: 16.h),
                    ),
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _launchEmail(),
                    icon: Icon(
                      FontAwesomeIcons.envelope,
                      size: 18.sp,
                    ),
                    label: const Text('Contact Us'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: appColors.primaryColor,
                      side: BorderSide(color: appColors.primaryColor),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                      padding: EdgeInsets.symmetric(vertical: 16.h),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _launchWebsite() async {
    const url = 'https://frappe.io';
    final Uri uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    }
  }

  Future<void> _launchEmail() async {
    const email = 'mailto:<EMAIL>?subject=ERP Solutions Inquiry';
    final Uri uri = Uri.parse(email);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    }
  }
}
