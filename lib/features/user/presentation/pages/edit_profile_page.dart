import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hodan_hospital/core/config/theme/colors/app_colors_extension.dart';
import 'package:hodan_hospital/core/enums/dropdown_type.dart';
import 'package:hodan_hospital/core/enums/image_type.dart';
import 'package:hodan_hospital/core/utils/extensions/app_bloc_extensions.dart';
import 'package:hodan_hospital/core/utils/extensions/build_context_extensions.dart';
import 'package:hodan_hospital/core/utils/helpers/form_validation_helper.dart';
import 'package:hodan_hospital/core/utils/helpers/somali_phone_number_formatter.dart';
import 'package:hodan_hospital/features/shared/data/models/gender.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/animations/animated_app_bar.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_button.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_drop_down.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_image_picker_card.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_textfield.dart';
import 'package:image_picker/image_picker.dart';

import '../../../shared/presentation/widgets/animations/animated_item_wrapper.dart';

class EditProfilePage extends StatefulWidget {
  const EditProfilePage({super.key});

  @override
  State<EditProfilePage> createState() => _EditProfilePageState();
}

class _EditProfilePageState extends State<EditProfilePage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _ageController = TextEditingController();
  Gender _gender = Gender.male;
  XFile? img;

  @override
  void initState() {
    super.initState();
    final currentUser = context.userBloc.currentUser;
    if (currentUser != null) {
      _nameController.text = currentUser.fullName;
      _phoneController.text = currentUser.phoneNumber;
      _ageController.text = currentUser.age.toString();
      // _district = currentUser.district;
      final genderFiled = currentUser.gender.toLowerCase();
      if (genderFiled == 'male') {
        _gender = Gender.male;
      } else {
        _gender = Gender.female;
      }
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _ageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // final appColors = context.appColors;

    return Scaffold(
      appBar: const AnimatedAppBar(
        title: 'Edit Profile',
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              // 🔹 Profile Picture
              CustomImagePickerCard(
                imageUrl: context.userBloc.currentUser?.profileImage,
                // imageUrl: '',
                imageType: ImageType.profile,
                userName: context.userBloc.currentUser?.fullName,
                showIcon: true,
                isLoading: false,
                imageFile: img,
                onGallery: (pickedImage) {
                  setState(() {
                    img = pickedImage;
                  });
                },
              ),
              SizedBox(height: 30.h),

              // 🔹 Full Name
              CustomTextField(
                isReadOnly: true,
                controller: _nameController,
                labelText: 'Full Name',

                // prefixIcon: Icon(Icons.person),
                validator: (value) =>
                    FormValidationHelper.validateName(value: value),
              ),
              SizedBox(height: 10.h),

              // 🔹 Phone
              // CustomTextField(
              //   isReadOnly: true,
              //   controller: _phoneController,
              //   labelText: "Phone",
              //   // prefixIcon: Icon(Icons.person),
              //   validator: (value) =>
              //       FormValidationHelper.validatePhoneNumber(value: value),
              // ),
              _buildNumberField(),

              SizedBox(height: 10.h),

              // 🔹 Age
              CustomTextField(
                isReadOnly: true,
                controller: _ageController,
                labelText: 'Age',
                // prefixIcon: Icon(Icons.person),
                validator: (value) =>
                    FormValidationHelper.validateAge(value: value),
              ),
              SizedBox(height: 10.h),

              // 🔹 Gender Dropdown
              CustomDropDown<Gender>(
                value: _gender,
                items: Gender.values,
                dropdownType: DropdownType.nonSelectable,
                labelText: 'Gender',
                displayItem: (gender) => gender?.genderType,
                onChanged: (val) {
                  setState(() {
                    _gender = val!;
                  });
                },
                validator: (value) =>
                    FormValidationHelper.validateRequiredField(
                  value: value?.genderType,
                ),
              ),
              SizedBox(height: 40.h),

              // 🔹 Save Button
              // CustomButton(
              //   buttonText: 'Save Changes',
              //   onTap: () {
              //     // if (_formKey.currentState!.validate()) {
              //     //   // Save logic here
              //     //   ScaffoldMessenger.of(context).showSnackBar(
              //     //     SnackBar(content: Text('Profile updated')),
              //     //   );
              //     // }
              //   },
              // ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNumberField() {
    return AnimatedItemWrapper(
      delay: const Duration(milliseconds: 200),
      child: CustomTextField(
        controller: _phoneController,
        keyboardType: TextInputType.phone,
        textInputAction: TextInputAction.done,
        hintText: '61XXXXXXX',
        inputFormatters: [
          SomaliPhoneNumberFormatter(),
        ],
        labelStyle: context.textTheme.labelMedium?.copyWith(
          fontSize: 16.sp,
          fontWeight: FontWeight.w600,
        ),
        hintStyle: context.textTheme.bodySmall?.copyWith(
          fontSize: 16.sp,
        ),
        validator: (value) =>
            FormValidationHelper.validatePhoneNumber(value: value),
        prefixIcon: Padding(
          padding: EdgeInsets.symmetric(horizontal: 10.w),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                '+252',
                style: context.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: context.appColors.primaryColor,
                ),
              ),
              const VerticalDivider(
                width: 1,
                thickness: 1,
                color: Colors.grey,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
