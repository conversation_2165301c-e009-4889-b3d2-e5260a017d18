import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:hodan_hospital/core/config/theme/colors/app_colors_extension.dart';
import 'package:hodan_hospital/core/constants/app_constants.dart';
import 'package:hodan_hospital/core/utils/extensions/build_context_extensions.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/animations/animated_item_wrapper.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_app_bar.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../core/enums/loading_type.dart';
import '../../../shared/presentation/widgets/custom_list_grid_view.dart';

class HelpSupportPage extends StatefulWidget {
  const HelpSupportPage({super.key});

  @override
  State<HelpSupportPage> createState() => _HelpSupportPageState();
}

class _HelpSupportPageState extends State<HelpSupportPage> {
  final List<FAQItem> _faqItems = [
    FAQItem(
      question: 'How do I book an appointment?',
      answer: "1. Go to Home screen and tap 'Book an Appointment'\n"
          '2. Select a doctor from the available list\n'
          '3. Choose or add a patient\n'
          '4. Select your preferred date and time\n'
          '5. Confirm your appointment details\n'
          "6. You'll receive a confirmation with appointment details",
      icon: FontAwesomeIcons.calendarCheck,
    ),
    FAQItem(
      question: 'How can I view my appointments?',
      answer: "Tap on the 'Appointments' tab at the bottom of the screen. "
          "You'll see two tabs:\n"
          '• Upcoming: Shows all future appointments\n'
          '• History: Shows all past appointments\n'
          'You can also download appointment PDFs and scan QR codes for check-in.',
      icon: FontAwesomeIcons.calendar,
    ),
    FAQItem(
      question: 'Can I book appointments for family members?',
      answer:
          'Yes! You can easily manage appointments for your entire family:\n\n'
          '📋 Adding Family Members:\n'
          "1. Go to 'Book Appointment' from the home screen\n"
          "2. On the patient selection screen, tap the '+' (Add Patient) button\n"
          '3. Fill in family member details:\n'
          '   • Full name\n'
          '   • Age\n'
          '   • Gender (Male/Female)\n'
          '   • District/Location\n'
          "4. Tap 'Add Patient' to save\n\n"
          '👨‍👩‍👧‍👦 Managing Family Appointments:\n'
          '• All family members will appear in your patient list\n'
          '• Select any family member when booking appointments\n'
          '• View appointment history for each family member\n'
          '• Download appointment confirmations for anyone\n\n'
          '💡 Note: All family members are linked to your phone number for easy management.',
      icon: FontAwesomeIcons.users,
    ),
    FAQItem(
      question: 'How can I access my lab results?',
      answer: "1. Go to Home screen and tap 'Lab Results'\n"
          "2. You'll see all your laboratory test results\n"
          '3. Tap on any result to view detailed information\n'
          '4. You can download or share results as needed\n'
          'Results are automatically updated when available.',
      icon: FontAwesomeIcons.flask,
    ),
    FAQItem(
      question: 'What are the QR codes for?',
      answer:
          'QR codes provide a fast and convenient way to print your appointment details at the hospital:\n\n'
          '📍 Where to Find QR Codes:\n'
          '• Located near cashier counters throughout the hospital\n'
          '• Available at reception desks\n'
          '• Posted in waiting areas for easy access\n\n'
          '📱 How to Use QR Codes:\n'
          "1. Open the app and go to 'Appointments' tab\n"
          "2. Find your appointment and tap the 'QR Scan' button\n"
          '3. Point your phone camera at the hospital QR code\n'
          '4. The app will automatically detect and scan the code\n'
          '5. Your appointment details will be sent for printing\n\n'
          '🖨️ What Happens Next:\n'
          '• The system will print your appointment confirmation\n'
          '• Includes appointment time, doctor, and patient details\n'
          '• Helps hospital staff quickly identify your appointment\n'
          '• Reduces waiting time at reception\n\n'
          '💡 Benefits: Skip long queues and get instant printed confirmations!',
      icon: FontAwesomeIcons.qrcode,
    ),
    FAQItem(
      question: 'How do I track my orders?',
      answer: "1. Go to Home screen and tap 'Orders'\n"
          '2. View orders in two categories:\n'
          '   • Pending: Orders being processed\n'
          '   • Completed: Finished orders\n'
          '3. Tap on any order to see detailed status and information',
      icon: FontAwesomeIcons.box,
    ),
    FAQItem(
      question: 'How can I update my profile?',
      answer: '1. Go to Profile tab\n'
          "2. Tap 'Personal Info'\n"
          '3. Update your profile picture by tapping the camera icon\n'
          '4. Edit available fields (some may be read-only for security)\n'
          'Note: Phone number and name may be locked for verification purposes.',
      icon: FontAwesomeIcons.user,
    ),
    FAQItem(
      question: 'What medical packages are available?',
      answer: 'Available packages include:\n'
          '• Antenatal Package (for pregnant women)\n'
          '• Sugar Package (diabetes screening)\n'
          '• Cardiac Package (heart health checkup)\n'
          '• Hypertension Patients Package\n'
          '• Executive Health Check Up (Male/Female)\n'
          "Tap 'Packages' on the home screen to explore all options.",
      icon: FontAwesomeIcons.boxOpen,
    ),
    FAQItem(
      question: 'How do I provide feedback?',
      answer:
          'We love hearing from our patients! Sharing your thoughts is easy:\n\n'
          '🔍 Finding the Feedback Option:\n'
          '1. Open the app and go to the main screen\n'
          '2. Look for a small round button (usually at the bottom)\n'
          '3. Tap this round button - a small menu will pop up\n'
          "4. Choose 'Send Feedback' from the menu\n\n"
          '� Telling Us Your Experience:\n'
          '5. You\'ll see two options to choose from:\n'
          '   • About the App (if something isn\'t working properly)\n'
          '   • About Hospital Service (doctors, nurses, waiting time, etc.)\n'
          '6. Give us stars (1 star = not good, 5 stars = excellent)\n'
          '7. Pick what your feedback is about from the list\n'
          '8. Write your thoughts in your own words\n'
          '9. Tell us how we can do better (if you want)\n'
          "10. Press 'Submit' to send us your message\n\n"
          '📬 What We Do With Your Feedback:\n'
          '• We read every single message you send\n'
          '• We use your suggestions to make things better\n'
          '• If something is urgent, we fix it quickly\n\n'
          '💡 Remember: Your honest opinion helps us serve you and other patients better!',
      icon: FontAwesomeIcons.star,
    ),
    FAQItem(
      question: 'What if I forgot my login details?',
      answer: 'The app uses phone number verification:\n'
          '1. Enter your registered phone number\n'
          "2. You'll receive an OTP via SMS\n"
          '3. Enter the OTP to access your account\n'
          "If you don't receive the OTP, check your network connection and try again.",
      icon: FontAwesomeIcons.lock,
    ),
    FAQItem(
      question: 'How do I cancel or reschedule an appointment?',
      answer:
          'Currently, appointment changes need to be made by contacting the hospital directly:\n'
          '• Call: ${AppConstants.phoneNumber}\n'
          '• Visit the hospital reception\n'
          '• Use the Contact Us feature in the app\n'
          'Please provide your appointment ID when requesting changes.',
      icon: FontAwesomeIcons.calendarXmark,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    final appColors = context.appColors;
    final textTheme = context.textTheme;

    return Scaffold(
      body: Column(
        children: [
          const CustomAppBar(
            title: 'Help & Support',
          ),
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.all(20.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header Section
                  AnimatedItemWrapper(
                    delay: const Duration(milliseconds: 200),
                    child: Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(20.w),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            appColors.primaryColor,
                            appColors.primaryColor.withValues(alpha: 0.8),
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(16.r),
                      ),
                      child: Column(
                        children: [
                          Icon(
                            FontAwesomeIcons.circleQuestion,
                            color: appColors.whiteColor,
                            size: 40.sp,
                          ),
                          SizedBox(height: 12.h),
                          Text(
                            'Frequently Asked Questions',
                            style: textTheme.titleLarge?.copyWith(
                              color: appColors.whiteColor,
                              fontWeight: FontWeight.bold,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          SizedBox(height: 8.h),
                          Text(
                            'Find answers to common questions about using the Hodan Hospital app',
                            style: textTheme.bodyMedium?.copyWith(
                              color:
                                  appColors.whiteColor.withValues(alpha: 0.9),
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  ),
                  SizedBox(height: 24.h),

                  // FAQ List
                  CustomListGridView<FAQItem>(
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: _faqItems.length,
                    separatorWidget: SizedBox(height: 12.h),
                    contentType: LoadingType.listView,
                    items: _faqItems,
                    isLoading: false,
                    isEmpty: false,
                    showFooter: false,
                    padding: EdgeInsets.zero,
                    itemBuilder: (context, faq) {
                      return FAQCard(faqItem: faq);
                    },
                  ),

                  SizedBox(height: 32.h),

                  // Contact Section
                  AnimatedItemWrapper(
                    delay:
                        Duration(milliseconds: 300 + (_faqItems.length * 100)),
                    child: Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(20.w),
                      decoration: BoxDecoration(
                        color: appColors.surfaceColor,
                        borderRadius: BorderRadius.circular(16.r),
                        border: Border.all(
                          color: appColors.primaryColor.withValues(alpha: 0.2),
                        ),
                      ),
                      child: Column(
                        children: [
                          Icon(
                            FontAwesomeIcons.headset,
                            color: appColors.primaryColor,
                            size: 32.sp,
                          ),
                          SizedBox(height: 12.h),
                          Text(
                            'Still Need Help?',
                            style: textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          SizedBox(height: 8.h),
                          Text(
                            'If you couldn\'t find the answer to your question, our support team is here to help.',
                            style: textTheme.bodyMedium?.copyWith(
                              color: appColors.subtextColor,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          SizedBox(height: 16.h),
                          Row(
                            children: [
                              Expanded(
                                child: _buildContactButton(
                                  context,
                                  icon: FontAwesomeIcons.phone,
                                  label: 'Call Us',
                                  subtitle: AppConstants.phoneNumber,
                                  onTap: () =>
                                      _makePhoneCall(AppConstants.phoneNumber),
                                ),
                              ),
                              SizedBox(width: 12.w),
                              Expanded(
                                child: _buildContactButton(
                                  context,
                                  icon: FontAwesomeIcons.envelope,
                                  label: 'Email Us',
                                  subtitle: AppConstants.email,
                                  onTap: () => _sendEmail(AppConstants.email),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  SizedBox(height: 20.h),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContactButton(
    BuildContext context, {
    required IconData icon,
    required String label,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    final appColors = context.appColors;
    final textTheme = context.textTheme;

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16.r),
        splashColor: appColors.primaryColor.withValues(alpha: 0.1),
        highlightColor: appColors.primaryColor.withValues(alpha: 0.05),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          padding: EdgeInsets.symmetric(vertical: 20.h, horizontal: 16.w),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                appColors.primaryColor.withValues(alpha: 0.08),
                appColors.primaryColor.withValues(alpha: 0.12),
              ],
            ),
            borderRadius: BorderRadius.circular(16.r),
            border: Border.all(
              color: appColors.primaryColor.withValues(alpha: 0.2),
              width: 1.5,
            ),
            boxShadow: [
              BoxShadow(
                color: appColors.primaryColor.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Icon with background circle
              Container(
                padding: EdgeInsets.all(12.w),
                decoration: BoxDecoration(
                  color: appColors.primaryColor.withValues(alpha: 0.15),
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: appColors.primaryColor.withValues(alpha: 0.2),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Icon(
                  icon,
                  color: appColors.primaryColor,
                  size: 28.sp,
                ),
              ),
              SizedBox(height: 12.h),

              // Label
              Text(
                label,
                style: textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w700,
                  color: appColors.textColor,
                  letterSpacing: 0.5,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 4.h),

              // Subtitle with better styling
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 2.h),
                decoration: BoxDecoration(
                  color: appColors.primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Text(
                  subtitle,
                  style: textTheme.bodySmall?.copyWith(
                    color: appColors.primaryColor,
                    fontWeight: FontWeight.w500,
                    fontSize: 11.sp,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _makePhoneCall(String phoneNumber) async {
    final Uri launchUri = Uri(
      scheme: 'tel',
      path: phoneNumber,
    );
    if (await canLaunchUrl(launchUri)) {
      await launchUrl(launchUri);
    }
  }

  Future<void> _sendEmail(String email) async {
    final Uri launchUri = Uri(
      scheme: 'mailto',
      path: email,
      query: 'subject=Help & Support - Hodan Hospital App',
    );
    if (await canLaunchUrl(launchUri)) {
      await launchUrl(launchUri);
    }
  }
}

class FAQItem {
  final String question;
  final String answer;
  final IconData icon;

  FAQItem({
    required this.question,
    required this.answer,
    required this.icon,
  });
}

class FAQCard extends StatefulWidget {
  final FAQItem faqItem;

  const FAQCard({super.key, required this.faqItem});

  @override
  State<FAQCard> createState() => _FAQCardState();
}

class _FAQCardState extends State<FAQCard> with SingleTickerProviderStateMixin {
  bool _isExpanded = false;
  late AnimationController _animationController;
  late Animation<double> _expandAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _expandAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _toggleExpansion() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final appColors = context.appColors;
    final textTheme = context.textTheme;

    return Container(
      decoration: BoxDecoration(
        color: appColors.surfaceColor,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: appColors.dividerColor.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          InkWell(
            onTap: _toggleExpansion,
            borderRadius: BorderRadius.circular(12.r),
            child: Padding(
              padding: EdgeInsets.all(16.w),
              child: Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(8.w),
                    decoration: BoxDecoration(
                      color: appColors.primaryColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Icon(
                      widget.faqItem.icon,
                      color: appColors.primaryColor,
                      size: 20.sp,
                    ),
                  ),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: Text(
                      widget.faqItem.question,
                      style: textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  AnimatedRotation(
                    turns: _isExpanded ? 0.5 : 0,
                    duration: const Duration(milliseconds: 300),
                    child: Icon(
                      Icons.keyboard_arrow_down,
                      color: appColors.primaryColor,
                    ),
                  ),
                ],
              ),
            ),
          ),
          SizeTransition(
            sizeFactor: _expandAnimation,
            child: Container(
              width: double.infinity,
              padding: EdgeInsets.fromLTRB(16.w, 0, 16.w, 16.w),
              child: Container(
                padding: EdgeInsets.all(16.w),
                decoration: BoxDecoration(
                  color: appColors.primaryColor.withValues(alpha: 0.05),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Text(
                  widget.faqItem.answer,
                  style: textTheme.bodyMedium?.copyWith(
                    color: appColors.textColor,
                    height: 1.5,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
