import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hodan_hospital/core/config/theme/colors/app_colors_extension.dart';
import 'package:hodan_hospital/core/utils/extensions/build_context_extensions.dart';

class ProfileHeader extends StatelessWidget {
  const ProfileHeader({super.key});

  @override
  Widget build(BuildContext context) {
    final textTheme = context.textTheme;
    return Padding(
      padding: EdgeInsets.only(top: 30.h),
      child: Text(
        'Profile',
        style: textTheme.titleLarge?.copyWith(
          color: context.appColors.whiteColor,
        ),
      ),
    );
  }
}
