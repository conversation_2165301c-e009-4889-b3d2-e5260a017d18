import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hodan_hospital/core/config/theme/colors/app_colors_extension.dart';
import 'package:hodan_hospital/core/utils/extensions/build_context_extensions.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/animations/animated_item_wrapper.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_container.dart';

class ProfileMenuItem extends StatelessWidget {
  final IconData icon;
  final String title;
  final VoidCallback? onTap;
  final Widget? trailing;
  final Duration duration;

  const ProfileMenuItem({
    required this.icon,
    required this.title,
    this.onTap,
    this.trailing,
    super.key,
    this.duration = const Duration(milliseconds: 300),
  });

  @override
  Widget build(BuildContext context) {
    final appColors = context.appColors;
    final textTheme = context.textTheme;
    return AnimatedItemWrapper(
      delay: duration,
      child: GestureDetector(
        onTap: onTap,
        child: CustomContainer(
          margin: EdgeInsets.symmetric(horizontal: 20.w),
          child: ListTile(
            leading: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: appColors.surfaceColor,
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Icon(icon, color: appColors.textColor),
            ),
            title: Text(
              title,
              style: textTheme.bodyLarge,
            ),
            trailing: trailing ?? Icon(Icons.arrow_forward_ios, size: 16),
            // tileColor:appColors.whiteColor,
            onTap: onTap,
            contentPadding:
                EdgeInsets.symmetric(horizontal: 16.w, vertical: 4.h),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12.r),
            ),
          ),
        ),
      ),
    );
  }
}
