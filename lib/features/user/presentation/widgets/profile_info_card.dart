import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hodan_hospital/core/enums/image_type.dart';
import 'package:hodan_hospital/core/utils/extensions/app_bloc_extensions.dart';
import 'package:hodan_hospital/core/utils/extensions/build_context_extensions.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_container.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_image_picker_card.dart';

class ProfileInfoCard extends StatelessWidget {
  const ProfileInfoCard({super.key});

  @override
  Widget build(BuildContext context) {
    final currentUser = context.userBloc.currentUser;
    final textTheme = context.textTheme;
    // padding: EdgeInsets.symmetric(vertical: 10.h),
    return CustomContainer(
      padding: EdgeInsets.symmetric(vertical: 10.h),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CustomImagePickerCard(
            imageUrl: currentUser?.profileImage,
            imageType: ImageType.profile,
            radius: 40,
            userName: currentUser?.fullName,
          ),
          SizedBox(height: 10.h),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 30.w),
            child: Text(
              currentUser?.fullName ?? 'Full Name',
              style: textTheme.titleLarge,
              maxLines: 2,
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }
}
