import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/core/use_cases/use_case.dart';
import 'package:hodan_hospital/features/user/domain/params/register_new_patient_params.dart';
import 'package:hodan_hospital/features/user/domain/repositories/user_reposiory.dart';

class RegisterNewPatientUseCase
    implements UseCase<String, RegisterNewPatientParams> {
  final UserRepository repository;

  RegisterNewPatientUseCase({required this.repository});

  @override
  FutureEitherFailOr<String> call({required RegisterNewPatientParams params}) {
    return repository.registerNewPatient(
      patFullName: params.patFullName,
      patGender: params.patGender,
      patAge: params.patAge,
      patAgeType: params.patAgeType,
      patMobileNumber: params.patMobileNumber,
      patDistrict: params.patDistrict,
    );
  }
}
