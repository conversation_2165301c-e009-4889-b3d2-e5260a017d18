import '../../../../core/errors/app_failure.dart';
import '../../../../core/use_cases/use_case.dart';
import '../params/send_feedback_params.dart';
import '../repositories/user_reposiory.dart';

// ✅ Concrete Use Case for sending feedback
class SendFeedbackUseCase implements UseCase<String, SendFeedbackParams> {
  final UserRepository repository;

  SendFeedbackUseCase({required this.repository});

  @override
  FutureEitherFailOr<String> call({required SendFeedbackParams params}) {
    return repository.sendFeedback(
      patientId: params.patientId,
      feedbackType: params.feedbackType,
      rating: params.rating,
      appFeedbackCategory: params.appFeedbackCategory,
      comments: params.comments,
      appVersion: params.appVersion,
      deviceInfo: params.deviceInfo,
    );
  }
}
