import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/core/use_cases/use_case.dart';
import 'package:hodan_hospital/features/user/domain/entities/district_entity.dart';
import 'package:hodan_hospital/features/user/domain/params/get_district_params.dart';
import 'package:hodan_hospital/features/user/domain/repositories/user_reposiory.dart';

// ✅ Concrete Use Case for fetching districts
class GetDistrictsUseCase
    extends UseCase<List<DistrictEntity>, GetDistrictParams> {
  final UserRepository userRepository;

  GetDistrictsUseCase({required this.userRepository});

  @override
  FutureEitherFailOr<List<DistrictEntity>> call({
    required GetDistrictParams params,
  }) async {
    return userRepository.getDistricts(forceFetch: params.forceFetch);
  }
}
