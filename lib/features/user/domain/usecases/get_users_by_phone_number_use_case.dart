import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/core/use_cases/use_case.dart';
import 'package:hodan_hospital/features/user/domain/entities/users_by_phone_entity.dart';
import 'package:hodan_hospital/features/user/domain/params/get_users_by_phone_number_params.dart';
import 'package:hodan_hospital/features/user/domain/repositories/user_reposiory.dart';

// ✅ Concrete Use Case for fetching users who is same mobile number for current user
class GetUsersByPhoneNumberUseCase
    implements UseCase<List<UsersByPhoneEntity>, GetUsersByPhoneNumberParams> {
  final UserRepository repository;

  GetUsersByPhoneNumberUseCase({required this.repository});

  @override
  FutureEitherFailOr<List<UsersByPhoneEntity>> call(
      {required GetUsersByPhoneNumberParams params}) {
    return repository.getUsersByPhone(
      forceFetch: params.forceFetch,
      doctorName: params.doctorName,
      mobileNumber: params.mobileNumber,
    );
  }
}
