import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/core/use_cases/use_case.dart';
import 'package:hodan_hospital/features/user/domain/repositories/user_reposiory.dart';
import 'package:hodan_hospital/features/shared/domain/entities/user_entity.dart';

import '../params/get_current_patient_params.dart';

// ✅ Concrete Use Case for fetching current user data
class GetCurrentUserUseCase
    implements UseCase<UserEntity, GetCurrentPatientParams> {
  final UserRepository repository;

  GetCurrentUserUseCase({required this.repository});

  @override
  FutureEitherFailOr<UserEntity> call({
    required GetCurrentPatientParams params,
  }) {
    return repository.getCurrentUser(
      forceFetch: params.forceFetch,
    );
  }
}
