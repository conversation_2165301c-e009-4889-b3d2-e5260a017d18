import 'package:hodan_hospital/core/utils/extensions/app_date_extensions.dart';

class UsersByPhoneEntity {
  final String pID;
  final String firstName;
  final int patientAge;
  final String? patientImage;
  final String customerGroup;

  //
  final String? followupId;
  final DateTime? followupExpirationDate;
  final DateTime? followupStartDate;
  final String? followupStatus;

  UsersByPhoneEntity({
    required this.pID,
    required this.firstName,
    required this.patientAge,
    required this.patientImage,
    required this.customerGroup,
    required this.followupId,
    required this.followupStartDate,
    required this.followupExpirationDate,
    required this.followupStatus,
  });

  //
  bool get hasMembership {
    return customerGroup == "Membership";
  }

  //
  bool hasFollowUpOn(DateTime selectedDay) {
    if (followupStartDate == null || followupExpirationDate == null)
      return false;

    final selected =
        DateTime(selectedDay.year, selectedDay.month, selectedDay.day);
    final start = DateTime(followupStartDate!.year, followupStartDate!.month,
        followupStartDate!.day);
    final end = DateTime(followupExpirationDate!.year,
        followupExpirationDate!.month, followupExpirationDate!.day);

    return selected.isAtSameMomentAs(start) ||
        selected.isAtSameMomentAs(end) ||
        (selected.isAfter(start) && selected.isBefore(end));
  }

  List<DateTime> get followupRemaingDays {
    if (followupStartDate == null || followupExpirationDate == null) return [];

    final today = DateTime.now();
    final start = followupStartDate!.isAfter(today)
        ? DateTime(followupStartDate!.year, followupStartDate!.month,
            followupStartDate!.day)
        : DateTime(today.year, today.month, today.day);
    final end = DateTime(
      followupExpirationDate!.year,
      followupExpirationDate!.month,
      followupExpirationDate!.day,
    );

    if (start.isAfter(end)) return [];

    List<DateTime> days = [];
    DateTime current = start;

    while (!current.isAfter(end)) {
      days.add(current);
      current = current.add(Duration(days: 1));
    }

    return days;
  }

  // get formatted followup expiration date
  String get formattedFollowupExpirationDate {
    return followupExpirationDate?.toFormattedString() ?? '';
  }

  @override
  String toString() {
    return 'UsersByPhoneEntity('
        'pID: $pID, '
        'firstName: $firstName, '
        'patientAge: $patientAge, '
        'patientImage: $patientImage, '
        'customerGroup: $customerGroup, '
        'hasMembership: $hasMembership, '
        'followupId: $followupId, '
        'followupStartDate: $followupStartDate, '
        'followupExpirationDate: $followupExpirationDate, '
        'followupStatus: $followupStatus, '
        'followupRemainingDays: ${followupRemaingDays.length}'
        ')';
  }
}
