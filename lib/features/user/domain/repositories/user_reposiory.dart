import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/features/user/domain/entities/district_entity.dart';
import 'package:hodan_hospital/features/user/domain/entities/users_by_phone_entity.dart';
import 'package:hodan_hospital/features/shared/domain/entities/user_entity.dart';

abstract class UserRepository {
  FutureEitherFailOr<UserEntity> getCurrentUser({
    required bool forceFetch,
  });
  FutureEitherFailOr<List<UsersByPhoneEntity>> getUsersByPhone({
    required String mobileNumber,
    required String doctorName,
    required bool forceFetch,
  });

  FutureEitherFailOr<String> registerNewPatient({
    required String patFullName,
    required String patGender,
    required double patAge,
    required String patAgeType,
    required String patMobileNumber,
    required String patDistrict,
  });

  FutureEitherFailOr<List<DistrictEntity>> getDistricts({
    required bool forceFetch,
  });

  FutureEitherFailOr<String> sendFeedback({
    required String patientId,
    required String feedbackType,
    required double rating,
    required String? appFeedbackCategory,
    required String comments,
    required String appVersion,
    required String deviceInfo,
  });
}
