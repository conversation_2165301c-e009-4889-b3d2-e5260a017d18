import 'package:fpdart/fpdart.dart';
import 'package:hodan_hospital/core/config/logger/app_logger.dart';
import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/features/authentication/data/mappers/auth_mappers.dart';
import 'package:hodan_hospital/features/user/data/datasources/local/user_local_data_source.dart';
import 'package:hodan_hospital/features/user/data/datasources/remote/user_remote_data_source.dart';
import 'package:hodan_hospital/features/user/data/mappers/users_by_phone_mapper.dart';
import 'package:hodan_hospital/features/user/data/mappers/district_mapper.dart';
import 'package:hodan_hospital/features/user/data/models/district_model.dart';
import 'package:hodan_hospital/features/user/data/models/users_by_phone_model.dart';
import 'package:hodan_hospital/features/user/domain/entities/district_entity.dart';
import 'package:hodan_hospital/features/user/domain/entities/users_by_phone_entity.dart';
import 'package:hodan_hospital/features/user/domain/repositories/user_reposiory.dart';
import 'package:hodan_hospital/features/shared/domain/entities/user_entity.dart';

class UserRepositoryImpl implements UserRepository {
  final UserRemoteDataSource userRemoteDataSource;
  final UserLocalDataSource userLocalDataSource;

  UserRepositoryImpl({
    required this.userRemoteDataSource,
    required this.userLocalDataSource,
  });

  @override
  FutureEitherFailOr<UserEntity> getCurrentUser({
    required bool forceFetch,
  }) async {
    // AppLogger().info('getCurrentUser and forceFetch: $forceFetch');
    if (!forceFetch) {
      final localResponse = await userLocalDataSource.readUserData();
      return localResponse.fold((failure) => left(failure), (userModel) {
        final userEntity = AuthMapper.toUserEntity(userModel);
        // AppLogger().info('getCurrentUser from cache: $userEntity');
        return right(userEntity);
      });
    }

    final remoteResponse = await userRemoteDataSource.getCurrentUser();
    return remoteResponse.fold((failure) async {
      final localResponse = await userLocalDataSource.readUserData();
      if (localResponse.isRight()) {
        // AppLogger().info('getCurrentUser from cache: $localResponse');
        return localResponse.map(
          (user) {
            final userEntity = AuthMapper.toUserEntity(user);
            return userEntity;
          },
        );
      }
      // AppLogger().info('getCurrentUser from server: $failure');
      return left(failure);
    }, (userModel) async {
      final userEntity = AuthMapper.toUserEntity(userModel);
      await userLocalDataSource.saveUserData(userModel: userModel);
      // AppLogger().info('getCurrentUser from server: $userEntity');
      return right(userEntity);
    });
  }

  @override
  FutureEitherFailOr<List<UsersByPhoneEntity>> getUsersByPhone({
    required String mobileNumber,
    required String doctorName,
    required bool forceFetch,
  }) async {
    try {
      // if (!forceFetch) {
      //   final cachedResponse = await _getCachedUsesByPhone();
      //   if (cachedResponse.isRight()) {
      //     return cachedResponse;
      //   }
      // }
      final remoteResponse = await _fetchUsersByPhoneFromServer(
        mobileNumber: mobileNumber,
        doctorName: doctorName,
      );
      return remoteResponse.fold(
        (failure) async {
          // final cachedResponse = await _getCachedUsesByPhone();
          // if (cachedResponse.isRight()) {
          //   return cachedResponse;
          // }
          return left(failure);
        },
        (bannerModels) async {
          // await userLocalDataSource.saveUsersByPhone(users: bannerModels);
          final bannersEntity =
              UsersByPhoneMapper.modelListToEntityList(bannerModels);
          return right(bannersEntity);
        },
      );
    } catch (error, stackTrace) {
      AppLogger().error(
        "Error fetching usersByPhone from ${forceFetch ? 'Server' : 'Cache'}  error: ${error.toString()}",
        error: error,
        stackTrace: stackTrace,
      );
      return left(
        UnexpectedFailure(
          message:
              'Failed to load usersByPhone from ${forceFetch ? 'server' : 'cache'} error: ${error.toString()}',
          stackTrace: stackTrace,
        ),
      );
    }
  }

  // FutureEitherFailOr<List<UsersByPhoneEntity>> _getCachedUsesByPhone() async {
  //   final cachedResponse = await userLocalDataSource.readUsersByPhone();
  //   return cachedResponse.map(
  //     (bannerModels) {
  //       final bannersEntity =
  //           UsersByPhoneMapper.modelListToEntityList(bannerModels);
  //       return bannersEntity;
  //     },
  //   );
  // }

  FutureEitherFailOr<List<UsersByPhoneModel>> _fetchUsersByPhoneFromServer({
    required String mobileNumber,
    required String doctorName,
  }) async {
    return await userRemoteDataSource.getUsersByPhone(
      mobileNumber: mobileNumber,
      doctorName: doctorName,
    );
  }

  @override
  FutureEitherFailOr<String> registerNewPatient({
    required String patFullName,
    required String patGender,
    required double patAge,
    required String patAgeType,
    required String patMobileNumber,
    required String patDistrict,
  }) async {
    return await userRemoteDataSource.registerNewPatient(
      patFullName: patFullName,
      patGender: patGender,
      patAge: patAge,
      patAgeType: patAgeType,
      patMobileNumber: patMobileNumber,
      patDistrict: patDistrict,
    );
  }

  @override
  FutureEitherFailOr<List<DistrictEntity>> getDistricts({
    required bool forceFetch,
  }) async {
    try {
      // if (!forceFetch) {
      //   final cachedResponse = await _getCachedDistricts();
      //   if (cachedResponse.isRight()) {
      //     return cachedResponse;
      //   }
      // }
      final remoteResponse = await _fetchDistrictsFromServer();
      return remoteResponse.fold(
        (failure) async {
          // final cachedResponse = await _getCachedDistricts();
          // if (cachedResponse.isRight()) {
          //   return cachedResponse;
          // }
          return left(failure);
        },
        (districtModels) async {
          // await userLocalDataSource.saveDistricts(districts: districtModels);
          final districtsEntity =
              DistrictMapper.modelListToEntityList(districtModels);
          return right(districtsEntity);
        },
      );
    } catch (error, stackTrace) {
      AppLogger().error(
        "Error fetching districts from ${forceFetch ? 'Server' : 'Cache'}  error: ${error.toString()}",
        error: error,
        stackTrace: stackTrace,
      );
      return left(
        UnexpectedFailure(
          message:
              'Failed to load districts from ${forceFetch ? 'server' : 'cache'} error: ${error.toString()}',
          stackTrace: stackTrace,
        ),
      );
    }
  }

  // FutureEitherFailOr<List<DistrictEntity>> _getCachedDistricts() async {
  //   final cachedResponse = await userLocalDataSource.readDistricts();
  //   return cachedResponse.map(
  //     (districtModels) {
  //       final districtsEntity =
  //           DistrictMapper.modelListToEntityList(districtModels);
  //       return districtsEntity;
  //     },
  //   );
  // }

  FutureEitherFailOr<List<DistrictModel>> _fetchDistrictsFromServer() async {
    return await userRemoteDataSource.getDistricts();
  }

  @override
  FutureEitherFailOr<String> sendFeedback({
    required String patientId,
    required String feedbackType,
    required double rating,
    required String? appFeedbackCategory,
    required String comments,
    required String appVersion,
    required String deviceInfo,
  }) async {
    final patientIdResponse = await userLocalDataSource.getPatientId();
    return await patientIdResponse.fold(
      (failure) async {
        return await userRemoteDataSource.sendFeedback(
          patientId: patientId,
          feedbackType: feedbackType,
          rating: rating,
          appFeedbackCategory: appFeedbackCategory,
          comments: comments,
          appVersion: appVersion,
          deviceInfo: deviceInfo,
        );
      },
      (pID) async {
        return await userRemoteDataSource.sendFeedback(
          patientId: pID,
          feedbackType: feedbackType,
          rating: rating,
          appFeedbackCategory: appFeedbackCategory,
          comments: comments,
          appVersion: appVersion,
          deviceInfo: deviceInfo,
        );
      },
    );
  }
}
