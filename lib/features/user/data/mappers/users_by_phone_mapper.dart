import 'package:hodan_hospital/features/user/data/models/users_by_phone_model.dart';
import 'package:hodan_hospital/features/user/domain/entities/users_by_phone_entity.dart';

class UsersByPhoneMapper {
  /// Converts a UsersByPhoneEntity to a UsersByPhoneModel
  static UsersByPhoneModel entityToModel(UsersByPhoneEntity entity) {
    return UsersByPhoneModel(
      firstName: entity.firstName,
      pID: entity.pID,
      patientAge: entity.patientAge,
      patientImage: entity.patientImage,
      customerGroup: entity.customerGroup,
      followupId: entity.followupId,
      followupExpirationDate: entity.followupExpirationDate,
      followupStartDate: entity.followupStartDate,
      followupStatus: entity.followupStatus,
    );
  }

  /// Converts a UsersByPhoneModel to a UsersByPhoneEntity
  static UsersByPhoneEntity modelToEntity(UsersByPhoneModel model) {
    return UsersByPhoneEntity(
      pID: model.pID,
      firstName: model.firstName,
      patientAge: model.patientAge,
      patientImage: model.patientImage,
      customerGroup: model.customerGroup,
      followupId: model.followupId,
      followupExpirationDate: model.followupExpirationDate,
      followupStartDate: model.followupStartDate,
      followupStatus: model.followupStatus,
    );
  }

  /// Converts a list of UsersByPhoneModel to a list of UsersByPhoneEntity
  static List<UsersByPhoneEntity> modelListToEntityList(
      List<UsersByPhoneModel> models) {
    return models.map((model) => modelToEntity(model)).toList();
  }

  /// Converts a list of UsersByPhoneEntity to a list of UsersByPhoneModel
  static List<UsersByPhoneModel> entityListToModelList(
      List<UsersByPhoneEntity> entities) {
    return entities.map((entity) => entityToModel(entity)).toList();
  }
}
