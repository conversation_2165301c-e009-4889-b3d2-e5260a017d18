import 'package:hodan_hospital/features/user/data/models/district_model.dart';
import 'package:hodan_hospital/features/user/domain/entities/district_entity.dart';

class DistrictMapper {
  static DistrictEntity modelToEntity(DistrictModel model) {
    return DistrictEntity(name: model.name);
  }

  static DistrictModel entityToModel(DistrictEntity entity) {
    return DistrictModel(name: entity.name);
  }

  static List<DistrictEntity> modelListToEntityList(
      List<DistrictModel> models) {
    return models.map((model) => modelToEntity(model)).toList();
  }

  static List<DistrictModel> entityListToModelList(
      List<DistrictEntity> entities) {
    return entities.map((entity) => entityToModel(entity)).toList();
  }
}
