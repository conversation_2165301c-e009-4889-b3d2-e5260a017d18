import 'dart:convert';

import 'package:fpdart/fpdart.dart';
import 'package:hodan_hospital/core/constants/local_storage_key_constants.dart';
import 'package:hodan_hospital/core/enums/cache_failure_type.dart';
import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/features/user/data/datasources/local/user_database_manager.dart';
import 'package:hodan_hospital/features/user/data/models/district_model.dart';
import 'package:hodan_hospital/features/user/data/models/users_by_phone_model.dart';
import 'package:hodan_hospital/features/shared/data/models/user_model.dart';
import 'package:hodan_hospital/core/services/flutter_secure_storage_services.dart';

// import '../../../../../core/config/logger/app_logger.dart';

abstract class UserLocalDataSource {
  FutureEitherFailOr<int> saveUserData({required UserModel userModel});
  FutureEitherFailOr<UserModel> readUserData();
  FutureEitherFailOr<List<int>> saveUsersByPhone(
      {required List<UsersByPhoneModel> users});
  FutureEitherFailOr<List<UsersByPhoneModel>> readUsersByPhone();
  FutureEitherFailOr<List<DistrictModel>> readDistricts();
  FutureEitherFailOr<List<int>> saveDistricts(
      {required List<DistrictModel> districts});

  FutureEitherFailOr<String> getPatientId();
}

class UserLocalDataSourceImpl implements UserLocalDataSource {
  final FlutterSecureStorageServices flutterSecureStorageServices;
  final UserDatabaseManager userDatabaseManager;

  UserLocalDataSourceImpl({
    required this.flutterSecureStorageServices,
    required this.userDatabaseManager,
  });

  /// 🟢 **Save User Data**
  @override
  FutureEitherFailOr<int> saveUserData({required UserModel userModel}) async {
    // Save user in **ObjectBox Database**
    // final dbResponse =
    //     await userDatabaseManager.saveUserData(userModel: userModel);

    // return dbResponse.fold(
    //   (failure) => left(failure),
    //   (userId) async {
    try {
      // Serialize and store user data in **FlutterSecureStorage**
      await flutterSecureStorageServices.storeData(
        key: LocalStorageKeyConstants.userModelKey,
        value: jsonEncode(userModel.toJson()),
      );
      // return right(userId);
      return right(1);
    } catch (e, stackTrace) {
      return left(
        CacheFailure(
          message: 'Failed to save user in Cache: ${e.toString()}',
          failureType: CacheFailureType.writeError,
          stackTrace: stackTrace,
        ),
      );
    }
    // },
    // );
  }

  /// 🟡 **Read User Data**
  @override
  FutureEitherFailOr<UserModel> readUserData() async {
    try {
      // AppLogger().info('readUserData');
      // Read user JSON from **FlutterSecureStorage**
      final response = await flutterSecureStorageServices.readData(
          key: LocalStorageKeyConstants.userModelKey);

      // AppLogger().info('readUserData: $response');
      if (response == null) {
        return left(
          CacheFailure(
            message: 'No user found in Cache',
            failureType: CacheFailureType.notFound,
          ),
        );
      }

      // AppLogger().info('readUserData: $response');

      final jsonMap = jsonDecode(response);
      final userModel = UserModel.fromJson(jsonMap);
      return right(userModel);

      // Verify with **ObjectBox Database**
      // final dbResponse = await userDatabaseManager.readUserData(patientId: pID);

      // return dbResponse.fold(
      //   (failure) => left(failure),
      //   (user) => right(user),
      // );
    } catch (e, stackTrace) {
      return left(
        CacheFailure(
          message: 'Failed to read user from Cache: ${e.toString()}',
          failureType: CacheFailureType.readError,
          stackTrace: stackTrace,
        ),
      );
    }
  }

  @override
  FutureEitherFailOr<List<UsersByPhoneModel>> readUsersByPhone() async {
    // Save user in **ObjectBox Database**
    return await userDatabaseManager.readUsersByPhone();
  }

  @override
  FutureEitherFailOr<List<int>> saveUsersByPhone(
      {required List<UsersByPhoneModel> users}) async {
    // Save user in **ObjectBox Database**
    return await userDatabaseManager.saveUsersByPhone(users: users);
  }

  @override
  FutureEitherFailOr<List<DistrictModel>> readDistricts() async {
    return await userDatabaseManager.readDistricts();
  }

  @override
  FutureEitherFailOr<List<int>> saveDistricts(
      {required List<DistrictModel> districts}) async {
    return await userDatabaseManager.saveDistricts(districts: districts);
  }

  @override
  FutureEitherFailOr<String> getPatientId() async {
    try {
      final pID = await flutterSecureStorageServices.readData(
          key: LocalStorageKeyConstants.userDataKey);
      if (pID == null) {
        return left(
          CacheFailure(
            message: 'No pID   found in Cache',
            failureType: CacheFailureType.notFound,
          ),
        );
      }
      return right(pID);
    } catch (e, stackTrace) {
      return left(
        CacheFailure(
          message: 'Failed to read user from Cache: ${e.toString()}',
          failureType: CacheFailureType.readError,
          stackTrace: stackTrace,
        ),
      );
    }
  }
}
