import 'package:hodan_hospital/core/enums/database_failure_type.dart';
import 'package:hodan_hospital/core/enums/database_operation_type.dart';
import 'package:hodan_hospital/core/errors/database_error_handler.dart';
import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/features/user/data/models/district_model.dart';
import 'package:hodan_hospital/features/user/data/models/users_by_phone_model.dart';
import 'package:hodan_hospital/features/shared/data/models/user_model.dart';
// import 'package:hodan_hospital/objectbox.g.dart';

abstract class UserDatabaseManager {
  FutureEitherFailOr<int> saveUserData({required UserModel userModel});
  FutureEitherFailOr<UserModel> readUserData({required String patientId});
  FutureEitherFailOr<List<int>> saveUsersByPhone(
      {required List<UsersByPhoneModel> users});
  FutureEitherFailOr<List<UsersByPhoneModel>> readUsersByPhone();
  FutureEitherFailOr<List<DistrictModel>> readDistricts();
  FutureEitherFailOr<List<int>> saveDistricts(
      {required List<DistrictModel> districts});
}

class UserDatabaseManagerImpl implements UserDatabaseManager {
  final DatabaseErrorHandler databaseErrorHandler;

  UserDatabaseManagerImpl({required this.databaseErrorHandler});

  /// 🟢 **Save User Data**
  @override
  FutureEitherFailOr<int> saveUserData({required UserModel userModel}) async {
    return await databaseErrorHandler.handleDatabaseOperation(
      operationType: DatabaseOperationType.write,
      operationFunction: () async {
        // await _deleteUserData(patientId: userModel.pID);
        // final userBox =
        //     await databaseErrorHandler.databaseManager.getBox<UserModel>();

        // // Insert or Update user
        // return userBox.put(userModel);

        return 1; // Simulating successful save with a dummy ID
      },
    );
  }

  /// 🔴 **Delete User Data**
  FutureEitherFailOr<bool> _deleteUserData({required String patientId}) async {
    return await databaseErrorHandler.handleDatabaseOperation(
      operationType: DatabaseOperationType.delete,
      operationFunction: () async {
        // final userBox =
        //     await databaseErrorHandler.databaseManager.getBox<UserModel>();

        // // Find user by patientId
        // final query = userBox.query(UserModel_.pID.equals(patientId)).build();
        // final user = query.findFirst();

        // if (user == null) {
        //   return false; // User not found
        // }

        // userBox.remove(user.id);
        return true;
      },
    );
  }

  /// 🟡 **Read User Data**
  @override
  FutureEitherFailOr<UserModel> readUserData(
      {required String patientId}) async {
    return await databaseErrorHandler.handleDatabaseOperation(
      operationType: DatabaseOperationType.read,
      operationFunction: () async {
        // final userBox =
        //     await databaseErrorHandler.databaseManager.getBox<UserModel>();

        // // Find user by patientId
        // final query = userBox.query(UserModel_.pID.equals(patientId)).build();
        // return query.findFirst();
        throw DatabaseFailure(
          message: 'No User found with patientId: $patientId',
          failureType: DatabaseFailureType.noDataFound,
          stackTrace: StackTrace.current,
        );
      },
    );
  }

  @override
  FutureEitherFailOr<List<UsersByPhoneModel>> readUsersByPhone() async {
    return await databaseErrorHandler.handleDatabaseOperation(
      operationType: DatabaseOperationType.read,
      operationFunction: () async {
        // final userByPhoneBox = await databaseErrorHandler.databaseManager
        //     .getBox<UsersByPhoneModel>();

        // // Find user by patientId
        // final usersByPhone = userByPhoneBox.query().build().find();
        // if (usersByPhone.isNotEmpty) {
        //   return usersByPhone;
        // }

        throw DatabaseFailure(
          message: 'No Users by phone  found in the database',
          failureType: DatabaseFailureType.noDataFound,
          stackTrace: StackTrace.current,
        );
      },
    );
  }

  @override
  FutureEitherFailOr<List<int>> saveUsersByPhone(
      {required List<UsersByPhoneModel> users}) async {
    return await databaseErrorHandler.handleDatabaseOperation(
      operationType: DatabaseOperationType.write,
      operationFunction: () async {
        // await _deleteUsersByPhone();
        // final usersByPhoneBox = await databaseErrorHandler.databaseManager
        //     .getBox<UsersByPhoneModel>();

        // // Insert or Update user
        // return usersByPhoneBox.putMany(users);
        return []; // Simulating successful save with an empty list
      },
    );
  }

  /// 🔴 **Delete Users by phone data**
  FutureEitherFailOr<bool> _deleteUsersByPhone() async {
    return await databaseErrorHandler.handleDatabaseOperation(
      operationType: DatabaseOperationType.delete,
      operationFunction: () async {
        // final usersByPhoneBox = await databaseErrorHandler.databaseManager
        //     .getBox<UsersByPhoneModel>();

        // // Find user by patientId
        // usersByPhoneBox.removeAll();

        return true;
      },
    );
  }

  @override
  FutureEitherFailOr<List<DistrictModel>> readDistricts() async {
    return await databaseErrorHandler.handleDatabaseOperation(
      operationType: DatabaseOperationType.read,
      operationFunction: () async {
        // final districtBox = await databaseErrorHandler.databaseManager
        //     .getBox<DistrictModel>();
        // return districtBox.query().build().find();
        throw DatabaseFailure(
          message: 'No Districts found in the database',
          failureType: DatabaseFailureType.noDataFound,
          stackTrace: StackTrace.current,
        );
      },
    );
  }

  @override
  FutureEitherFailOr<List<int>> saveDistricts(
      {required List<DistrictModel> districts}) async {
    return await databaseErrorHandler.handleDatabaseOperation(
      operationType: DatabaseOperationType.write,
      operationFunction: () async {
        // final districtBox = await databaseErrorHandler.databaseManager
        //     .getBox<DistrictModel>();
        // return districtBox.putMany(districts);
        return []; // Simulating successful save with an empty list
      },
    );
  }
}
