// import 'package:objectbox/objectbox.dart';
import 'package:hodan_hospital/core/enums/parse_failure_type.dart';
import 'package:hodan_hospital/core/errors/app_failure.dart';

// @Entity()
class DistrictModel {
  // @Id()
  int id = 0;

  // @Index()
  final String name;

  DistrictModel({required this.name});

  factory DistrictModel.fromJson(Map<String, dynamic> json) {
    try {
      return DistrictModel(name: json['territory_name']);
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: " Failed to parse DistrictModel : ${error.toString()} ",
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: DistrictModel,
        stackTrace: stackTrace,
      );
    }
  }
  static List<DistrictModel> fromJsonList(List<dynamic> jsonList) {
    return jsonList.map((json) => DistrictModel.fromJson(json)).toList();
  }
}
