import 'package:hodan_hospital/core/enums/parse_failure_type.dart';
import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/core/utils/extensions/app_date_extensions.dart';
// import 'package:objectbox/objectbox.dart';

// @Entity()
class UsersByPhoneModel {
  // @Id()
  int id = 0;

  // Core patient fields
  final String pID;
  final String firstName;
  final int patientAge;
  final String? patientImage;
  final String customerGroup;

  // Flattened FeeValidity fields
  final String? followupId;
  final DateTime? followupExpirationDate;
  final DateTime? followupStartDate;
  final String? followupStatus;

  UsersByPhoneModel({
    required this.pID,
    required this.firstName,
    required this.patientAge,
    this.patientImage,
    required this.customerGroup,
    this.followupId,
    this.followupExpirationDate,
    this.followupStartDate,
    this.followupStatus,
  });

  factory UsersByPhoneModel.fromJson(Map<String, dynamic> map) {
    try {
      // Validate required fields
      if (map['name'] == null || map['first_name'] == null) {
        throw const FormatException('Missing required patient fields');
      }

      // Parse fee validity if exists
      // final feeValidity = map['fee_validity'] as Map<String, dynamic>?;

      return UsersByPhoneModel(
        pID: map['name'] as String,
        firstName: map['first_name'] as String,
        patientAge: _parseAge(map['p_age']),
        patientImage: map['image'] as String?,
        customerGroup:
            (map['customer_group'] as String?) ?? 'All Customer Groups',
        // followupId: feeValidity?['name'] as String?,
        // followupExpirationDate:
        //     (feeValidity?['valid_till'] as String?)?.toDateTime(),
        // followupStartDate:
        //     (feeValidity?['start_date'] as String?)?.toDateTime(),
        // followupStatus: feeValidity?['status'] as String?,
        followupId: map['followupId'] as String?,
        followupExpirationDate:
            (map['followupExpirationDate'] as String?)?.toDateTime(),
        followupStartDate: (map['followupStartDate'] as String?)?.toDateTime(),
        followupStatus: map['followupStatus'] as String?,
      );
    } on FormatException catch (e, stackTrace) {
      throw ParsingFailure(
        message: 'Invalid patient data format: ${e.message}',
        failureType: ParsingFailureType.invalidFormat,
        expectedType: UsersByPhoneModel,
        stackTrace: stackTrace,
      );
    } on TypeError catch (e, stackTrace) {
      throw ParsingFailure(
        message: 'Type mismatch in patient data: ${e.toString()}',
        failureType: ParsingFailureType.typeMismatch,
        expectedType: UsersByPhoneModel,
        stackTrace: stackTrace,
      );
    } catch (e, stackTrace) {
      throw ParsingFailure(
        message: 'Unexpected error parsing patient: ${e.toString()}',
        failureType: ParsingFailureType.unknown,
        expectedType: UsersByPhoneModel,
        stackTrace: stackTrace,
      );
    }
  }

  static int _parseAge(dynamic age) {
    try {
      if (age == null) return 0;
      if (age is int) return age;
      if (age is double) return age.toInt();
      if (age is String) return int.tryParse(age) ?? 0;
      return 0;
    } catch (_) {
      return 0;
    }
  }

  static List<UsersByPhoneModel> fromJsonList(List<dynamic> list) {
    try {
      return list.map<UsersByPhoneModel>((e) {
        if (e is! Map<String, dynamic>) {
          throw ParsingFailure(
            message: 'Expected Map<String,dynamic> but got ${e.runtimeType}',
            failureType: ParsingFailureType.typeMismatch,
            expectedType: UsersByPhoneModel,
          );
        }
        return UsersByPhoneModel.fromJson(e);
      }).toList();
    } on ParsingFailure {
      rethrow;
    } catch (e, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse patient list: ${e.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: List<UsersByPhoneModel>,
        stackTrace: stackTrace,
      );
    }
  }

  @override
  String toString() {
    return 'UsersByPhoneModel(pID: $pID, name: $firstName, age: $patientAge)';
  }
}
