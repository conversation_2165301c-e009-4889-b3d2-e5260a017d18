// import 'package:hodan_hospital/core/config/enviroment/enviroment_config.dart';
// import 'package:hodan_hospital/core/config/logger/app_logger.dart';
// import 'package:mailer/mailer.dart';
// import 'package:mailer/smtp_server.dart';

// class EmailServices {
//   static Future<void> sendConfirmationEmail({
//     required String patientEmail,
//     required String patientName,
//     required String doctorName,
//     required String appointmentTime,
//   }) async {
//     try {
//       final String username = EnvironmentConfig.emailUsername;
//       final String password = EnvironmentConfig.emailPassword;

//       if (!_isValidEmail(patientEmail)) {
//         AppLogger().error("❌ Invalid email format: $patientEmail");
//         return;
//       }

//       final smtpServer = SmtpServer(
//         'smtp.gmail.com',
//         username: username,
//         password: password,
//         port: 587, // Use 465 for SSL, 587 for TLS
//         ssl: false, // Set true if using SSL
//         allowInsecure: false,
//       );

//       final message = Message()
//         ..from = Address(username, "Hodan Medical Center")
//         ..recipients.add(patientEmail)
//         ..subject = "📅 Appointment Confirmation - $appointmentTime"
//         ..html = """
//         <html>
//         <body style="font-family: Arial, sans-serif; background-color: #f4f4f4; padding: 20px;">
//           <div style="max-width: 600px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);">
//             <h2 style="color: #007bff; text-align: center;">✅ Appointment Confirmed</h2>
//             <p>Dear <strong>$patientName</strong>,</p>
//             <p>We are pleased to confirm your appointment with:</p>
//             <div style="background: #f1f1f1; padding: 15px; border-radius: 8px;">
//               <p><strong>👨‍⚕ Doctor:</strong> Dr. $doctorName</p>
//               <p><strong>📅 Date & Time:</strong> $appointmentTime</p>
//             </div>
//             <p>Thank you for choosing <strong>Hodan Medical Center</strong>.</p>
//             <p>If you have any questions, feel free to contact us at any time.</p>
//             <p style="font-size: 12px; text-align: center; color: #888;">This is an automated message. Please do not reply.</p>
//           </div>
//         </body>
//         </html>
//       """;

//       await send(message, smtpServer);

//       AppLogger().info("✅ Email sent successfully to $patientEmail");
//     } on MailerException catch (e, stacktrace) {
//       AppLogger().warning('Message not sent.');
//       for (var p in e.problems) {
//         AppLogger().error(
//           "Failed to send email: ${p.code}: ${p.msg}",
//           error: e,
//           stackTrace: stacktrace,
//         );
//       }
//     } catch (e, stacktrace) {
//       AppLogger()
//           .error("❌ Failed to send email", error: e, stackTrace: stacktrace);
//       throw Exception("Email sending failed. Reason: ${e.toString()}");
//     }
//   }

//   static bool _isValidEmail(String email) {
//     final emailRegExp = RegExp(
//       r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$",
//     );
//     return emailRegExp.hasMatch(email);
//   }
// }
