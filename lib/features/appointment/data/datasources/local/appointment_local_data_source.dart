// import 'package:fpdart/fpdart.dart';
// import 'package:hodan_hospital/core/constants/local_storage_key_constants.dart';
// import 'package:hodan_hospital/core/enums/cache_failure_type.dart';
// import 'package:hodan_hospital/core/errors/app_failure.dart';
// import 'package:hodan_hospital/core/services/flutter_secure_storage_services.dart';
// import 'package:hodan_hospital/features/appointment/data/datasources/local/appointment_database_manager.dart';
// import 'package:hodan_hospital/features/appointment/data/models/appointment_model.dart';

// abstract class AppointmentLocalDataSource {
//   FutureEitherFailOr<List<int>> saveAppointments(
//       {required List<AppointmentModel> appointments});
//   FutureEitherFailOr<List<AppointmentModel>> readAppointments();
// }

// class AppointmentLocalDataSourceImpl implements AppointmentLocalDataSource {
//   // final AppointmentDatabaseManager appointmentDatabaseManager;
//   final FlutterSecureStorageServices flutterSecureStorageServices;

//   AppointmentLocalDataSourceImpl({
//     // required this.appointmentDatabaseManager,
//     required this.flutterSecureStorageServices,
//   });

//   @override
//   FutureEitherFailOr<List<AppointmentModel>> readAppointments() async {
//     try {
//       final pID = await flutterSecureStorageServices.readData(
//         key: LocalStorageKeyConstants.userDataKey,
//       );
//       return await appointmentDatabaseManager.readAppointments(
//         patientId: pID ?? '',
//       );
//     } catch (e, stackTrace) {
//       return left(
//         CacheFailure(
//           message: 'Failed to read appointments from Cache: ${e.toString()}',
//           failureType: CacheFailureType.readError,
//           stackTrace: stackTrace,
//         ),
//       );
//     }
//   }

//   @override
//   FutureEitherFailOr<List<int>> saveAppointments(
//       {required List<AppointmentModel> appointments}) async {
//     return await appointmentDatabaseManager.saveAppointments(
//       appointments: appointments,
//     );
//   }
// }
