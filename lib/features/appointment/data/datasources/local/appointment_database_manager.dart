// import 'package:hodan_hospital/core/enums/database_failure_type.dart';
// import 'package:hodan_hospital/core/enums/database_operation_type.dart';
// import 'package:hodan_hospital/core/errors/app_failure.dart';
// import 'package:hodan_hospital/core/errors/database_error_handler.dart';
// import 'package:hodan_hospital/features/appointment/data/models/appointment_model.dart';

// abstract class AppointmentDatabaseManager {
//   FutureEitherFailOr<List<int>> saveAppointments(
//       {required List<AppointmentModel> appointments});
//   // FutureEitherFailOr<bool> deleteAppointments();
//   FutureEitherFailOr<List<AppointmentModel>> readAppointments(
//       {required String patientId});
// }

// class AppointmentDatabaseManagerImpl implements AppointmentDatabaseManager {
//   final DatabaseErrorHandler databaseErrorHandler;

//   AppointmentDatabaseManagerImpl({required this.databaseErrorHandler});

//   /// 🟢 **Save Appointments**
//   @override
//   FutureEitherFailOr<List<int>> saveAppointments(
//       {required List<AppointmentModel> appointments}) async {
//     return await databaseErrorHandler.handleDatabaseOperation(
//       operationType: DatabaseOperationType.write,
//       operationFunction: () async {
//         await _deleteAppointments();
//         // final appointmentBox = await databaseErrorHandler.databaseManager
//         // .getBox<AppointmentModel>();

//         // Insert or Update appointment
//         // return appointmentBox.putMany(appointments);
//         return [];
//       },
//     );
//   }

//   /// 🔴 **Delete All Appointments**
//   FutureEitherFailOr<void> _deleteAppointments() async {
//     return await databaseErrorHandler.handleDatabaseOperation(
//       operationType: DatabaseOperationType.delete,
//       operationFunction: () async {
//         // final appointmentBox = await databaseErrorHandler.databaseManager
//         // .getBox<AppointmentModel>();

//         // appointmentBox.removeAll();

//         return;
//       },
//     );
//   }

//   /// 🟡 **Read Appointments**
//   @override
//   FutureEitherFailOr<List<AppointmentModel>> readAppointments({
//     required String patientId,
//   }) async {
//     return await databaseErrorHandler.handleDatabaseOperation(
//       operationType: DatabaseOperationType.read,
//       operationFunction: () async {
//         // final appointmentBox = await databaseErrorHandler.databaseManager
//         // .getBox<AppointmentModel>();

//         // Find Appointments and return
//         // final appointments = appointmentBox
//         // .query(AppointmentModel_.patientId.equals(patientId))
//         //     .query()
//         //     .build()
//         //     .find();
//         // if (appointments.isNotEmpty) {
//         //   return appointments;
//         // }

//         throw DatabaseFailure(
//           message: 'No Appointments found in the database',
//           failureType: DatabaseFailureType.noDataFound,
//           stackTrace: StackTrace.current,
//         );
//       },
//     );
//   }
// }
