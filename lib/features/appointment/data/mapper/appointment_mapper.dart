import 'package:hodan_hospital/features/appointment/data/models/appointment_model.dart';
import 'package:hodan_hospital/features/appointment/domain/entities/appointment_entity.dart';

class AppointmentMapper {
  /// Converts a AppointmentEntity to a AppointmentModel
  static AppointmentModel entityToModel(AppointmentEntity entity) {
    return AppointmentModel(
      appointmentID: entity.appointmentID,
      patientId: entity.patientId,
      patientName: entity.patientName,
      patientImage: entity.patientImage,
      practitioner: entity.practitioner,
      paidAmount: entity.paidAmount,
      appointmentSource: entity.appointmentSource,
      date: entity.date,
    );
  }

  /// Converts a AppointmentModel to a AppointmentEntity
  static AppointmentEntity modelToEntity(AppointmentModel model) {
    return AppointmentEntity(
      appointmentID: model.appointmentID,
      patientId: model.patientId,
      patientName: model.patientName,
      patientImage: model.patientImage,
      practitioner: model.practitioner,
      paidAmount: model.paidAmount,
      appointmentSource: model.appointmentSource,
      date: model.date,
    );
  }

  /// Converts a list of AppointmentModel to a list of AppointmentEntity
  static List<AppointmentEntity> modelListToEntityList(
      List<AppointmentModel> models) {
    return models.map((model) => modelToEntity(model)).toList();
  }

  /// Converts a list of AppointmentEntity to a list of AppointmentModel
  static List<AppointmentModel> entityListToModelList(
      List<AppointmentEntity> entities) {
    return entities.map((entity) => entityToModel(entity)).toList();
  }
}
