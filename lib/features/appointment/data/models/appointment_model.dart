import 'package:hodan_hospital/core/enums/parse_failure_type.dart';
import 'package:hodan_hospital/core/errors/app_failure.dart';
// import 'package:objectbox/objectbox.dart';
import 'package:hodan_hospital/core/utils/extensions/app_date_extensions.dart';

// @Entity()
class AppointmentModel {
  // @Id()
  int id = 0;

  // @Index()
  final String appointmentID;
  final String patientId;
  final String patientName;
  final String patientImage;
  final String practitioner;
  final String appointmentSource;
  final double paidAmount;

  // @Property(type: PropertyType.date)
  final DateTime? date;

  AppointmentModel({
    required this.appointmentID,
    required this.patientId,
    required this.patientName,
    required this.patientImage,
    required this.appointmentSource,
    required this.practitioner,
    required this.paidAmount,
    required this.date,
  });

  factory AppointmentModel.fromJson(Map<String, dynamic> json) {
    try {
      return AppointmentModel(
        appointmentID: json['name'] ?? '',
        patientId: json['patient'] ?? '',
        patientName: json['patient_name'] ?? '',
        patientImage: json['image'] ?? '',
        appointmentSource: json['appointment_source'] ?? '',
        practitioner: json['practitioner'] ?? '',
        // paidAmount: (json['paid_amount'] as num?)?.toDouble() ?? 0.0,
        paidAmount: (json['payable_amount'] as num?)?.toDouble() ?? 0.0,
        // date: (json['date'] as String?)?.toDateTime(),
        date: (json['creation'] as String?)?.toDateTime(),
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse AppointmentModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: AppointmentModel,
        stackTrace: stackTrace,
      );
    }
  }

  static List<AppointmentModel> fromJsonList(List<dynamic> jsonList) {
    return jsonList
        .map((json) => AppointmentModel.fromJson(json as Map<String, dynamic>))
        .toList();
  }

  @override
  String toString() {
    return 'AppointmentModel(id: $id, name: $appointmentID, patient: $patientId, practitioner: $practitioner, paidAmount: $paidAmount, date: $date)';
  }
}
