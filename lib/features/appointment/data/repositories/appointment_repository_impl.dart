import 'package:flutter/services.dart';
import 'package:fpdart/fpdart.dart';
import 'package:hodan_hospital/core/config/logger/app_logger.dart';
import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/features/appointment/data/datasources/remote/appointment_remote_data_source.dart';
import 'package:hodan_hospital/features/appointment/data/mapper/appointment_mapper.dart';
import 'package:hodan_hospital/features/appointment/data/models/appointment_model.dart';
import 'package:hodan_hospital/features/appointment/domain/entities/appointment_entity.dart';
import 'package:hodan_hospital/features/appointment/domain/repositories/appointment_repository.dart';

class AppointmentRepositoryImpl implements AppointmentRepository {
  final AppointmentRemoteDataSource appointmentRemoteDataSource;
  // final AppointmentLocalDataSource appointmentLocalDataSource;

  AppointmentRepositoryImpl({
    required this.appointmentRemoteDataSource,
    // required this.appointmentLocalDataSource,
  });

  @override
  FutureEitherFailOr<List<AppointmentEntity>> getAppointments({
    required bool forceFetch,
    required String mobileNo,
  }) async {
    try {
      // if (!forceFetch) {
      //   final cachedResponse = await _getCachedAppointments();
      //   if (cachedResponse.isRight()) {
      //     return cachedResponse;
      //   }
      // }

      final remoteResponse =
          await _fetchAppointmentsFromServer(mobileNo: mobileNo);
      return remoteResponse.fold(
        (failure) async {
          // final cachedResponse = await _getCachedAppointments();
          // if (cachedResponse.isRight()) {
          //   return cachedResponse;
          // }
          return left(failure);
        },
        (appointments) async {
          // await appointmentLocalDataSource.saveAppointments(
          //     appointments: appointments);
          final appointmentEntities =
              AppointmentMapper.modelListToEntityList(appointments);
          return right(appointmentEntities);
        },
      );
    } catch (error, stackTrace) {
      AppLogger().error(
        'Unexpected error in getAppointments',
        error: error,
        stackTrace: stackTrace,
      );
      return left(UnexpectedFailure(
        message: 'Unexpected error in getAppointments',
        stackTrace: stackTrace,
      ));
    }
  }

  // FutureEitherFailOr<List<AppointmentEntity>> _getCachedAppointments() async {
  //   final cachedResponse = await appointmentLocalDataSource.readAppointments();
  //   return cachedResponse.map(
  //     (appointmentModels) {
  //       final appointmentEntities =
  //           AppointmentMapper.modelListToEntityList(appointmentModels);
  //       return appointmentEntities;
  //     },
  //   );
  // }

  FutureEitherFailOr<List<AppointmentModel>> _fetchAppointmentsFromServer({
    required String mobileNo,
  }) async {
    return await appointmentRemoteDataSource.getAppointments(
        mobileNo: mobileNo);
  }

  @override
  FutureEitherFailOr<String> makeAppointment({
    required String pID,
    required String doctorPractitioner,
    required String patientMobile,
    required double doctAmount,
    required bool hasFollowUp,
    required String appointmentDate,
    required bool hasMembership,
  }) async {
    return await appointmentRemoteDataSource.makeAppointments(
      pID: pID,
      doctorPractitioner: doctorPractitioner,
      patientMobile: patientMobile,
      doctAmount: doctAmount,
      hasFollowUp: hasFollowUp,
      appointmentDate: appointmentDate,
      hasMembership: hasMembership,
    );
  }

  @override
  FutureEitherFailOr<String> processAppointmentBarCode({
    required String queName,
  }) async {
    return await appointmentRemoteDataSource.processAppointmentBarCode(
        queName: queName);
  }

  @override
  FutureEitherFailOr<String> downloadAppointmentPDF({
    required Uint8List pdfBytes,
  }) async {
    return await appointmentRemoteDataSource.downloadAppointmentPDF(
      pdfBytes: pdfBytes,
    );
  }

  @override
  FutureEitherFailOr<Uint8List> getAppointmentPDF({
    required String appointmentId,
  }) async {
    return await appointmentRemoteDataSource.getAppointmentPDF(
      appointmentId: appointmentId,
    );
  }
}
