import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:hodan_hospital/core/config/theme/colors/app_colors_extension.dart';
import 'package:hodan_hospital/core/enums/button_state.dart';
import 'package:hodan_hospital/core/enums/image_type.dart';
import 'package:hodan_hospital/core/utils/extensions/build_context_extensions.dart';
import 'package:hodan_hospital/features/appointment/domain/entities/appointment_entity.dart';
import 'package:hodan_hospital/features/appointment/presentation/bloc/appointment_bloc.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_button.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_container.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_image_picker_card.dart';

class ApointmentWidget extends StatelessWidget {
  const ApointmentWidget({
    super.key,
    required this.appointment,
    required this.onDownload,
  });

  final AppointmentEntity appointment;
  final VoidCallback? onDownload;

  @override
  Widget build(BuildContext context) {
    final textTheme = context.textTheme;
    final appColors = context.appColors;

    return GestureDetector(
      onTap: () {
        showAppointmentBottomSheet(context);
      },
      child: CustomContainer(
        padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 15.h),
        child: Row(
          children: [
            // Profile Image
            CustomImagePickerCard(
              imageUrl: appointment.patientImage,
              userName: appointment.patientName,
              imageType: ImageType.profile,
              radius: 20,
            ),
            SizedBox(width: 20.w),
            // Appointment Info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  buildTextSpan(
                    textTheme: textTheme,
                    label: 'Patient',
                    value: appointment.patientName,
                  ),
                  SizedBox(height: 4.h),
                  buildTextSpan(
                    textTheme: textTheme,
                    label: 'Doctor',
                    value: appointment.practitioner,
                  ),
                  SizedBox(height: 4.h),
                  buildTextSpan(
                    textTheme: textTheme,
                    label: 'Date',
                    value: appointment.formattedDate,
                  ),
                  buildTextSpan(
                    textTheme: textTheme,
                    label: 'Fee',
                    value: appointment.formattedPaidAmount,
                  ),
                ],
              ),
            ),
            SizedBox(width: 10.w),
            // Chevron Icon
            FaIcon(
              FontAwesomeIcons.chevronRight,
              size: 16,
              color: appColors.subtextColor,
            ),
          ],
        ),
      ),
    );
  }

  /// 📌 Helper Function to Create Rich Text UI
  Widget buildTextSpan({
    required TextTheme textTheme,
    required String label,
    required String value,
    double? titleFontSize,
    double? valueFontSize,
  }) {
    return Text.rich(
      TextSpan(
        children: [
          TextSpan(
            text: '$label: ',
            style: textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.bold,
              fontSize: titleFontSize,
            ),
          ),
          TextSpan(
            text: value,
            style: textTheme.bodySmall?.copyWith(
              fontSize: valueFontSize,
            ),
          ),
        ],
      ),
      overflow: TextOverflow.ellipsis,
    );
  }

  /// 📌 Helper Function to Create Bottom Sheet
  void showAppointmentBottomSheet(BuildContext context) {
    final textTheme = context.textTheme;
    final appColors = context.appColors;

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (BuildContext context) {
        return BlocBuilder<AppointmentBloc, AppointmentState>(
          builder: (context, state) {
            final isPDFLoading = state is GetAppointmentPDFLoading;
            return Padding(
              padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 20.h),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Profile Image
                  // const CustomImagePickerCard(
                  //   imageType: ImageType.profile,
                  //   radius: 20,
                  // ),
                  Center(
                    child: Text(
                      'Appointment Details',
                      style: textTheme.titleLarge,
                    ),
                  ),
                  SizedBox(height: 25.h),
                  // Appointment Info
                  buildTextSpan(
                    textTheme: textTheme,
                    label: 'Patient',
                    value: appointment.patientName,
                    titleFontSize: 18,
                    valueFontSize: 16,
                  ),
                  SizedBox(height: 4.h),
                  buildTextSpan(
                    textTheme: textTheme,
                    label: 'Doctor',
                    value: appointment.practitioner,
                    titleFontSize: 18,
                    valueFontSize: 16,
                  ),
                  SizedBox(height: 4.h),
                  buildTextSpan(
                    textTheme: textTheme,
                    label: 'Date',
                    value: appointment.formattedDate,
                    titleFontSize: 18,
                    valueFontSize: 16,
                  ),
                  SizedBox(height: 4.h),
                  buildTextSpan(
                    textTheme: textTheme,
                    label: 'Fee',
                    value: appointment.formattedPaidAmount,
                    titleFontSize: 18,
                    valueFontSize: 16,
                  ),

                  SizedBox(height: 50.h),

                  // Action Buttons
                  if (!isPDFLoading)
                    CustomButton(
                      width: context.screenWidth,
                      onTap: onDownload,
                      buttonText: 'Generate PDF',
                      leadingIcon: FaIcon(
                        FontAwesomeIcons.solidFilePdf,
                        color: appColors.whiteColor,
                      ),
                      buttonState: isPDFLoading
                          ? ButtonState.loading
                          : ButtonState.normal,
                    )
                  else if (isPDFLoading)
                    const Center(
                      child: CircularProgressIndicator(),
                    ),
                ],
              ),
            );
          },
        );
      },
    );
  }
}
