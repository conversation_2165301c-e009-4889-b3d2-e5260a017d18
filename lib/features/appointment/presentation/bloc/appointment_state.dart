part of 'appointment_bloc.dart';

abstract class AppointmentState extends Equatable {
  const AppointmentState();

  @override
  List<Object> get props => [];
}

class AppointmentInitial extends AppointmentState {}

class AppointmentLoading extends AppointmentState {}

class MakeAppointmentLoading extends AppointmentState {}

class AppointmentLoaded extends AppointmentState {
  final List<AppointmentEntity> appointments;

  const AppointmentLoaded({required this.appointments});

  @override
  List<Object> get props => [appointments];
}

class AppointmentEmpty extends AppointmentState {}

class AppointmentFailure extends AppointmentState {
  final AppFailure failure;

  const AppointmentFailure({required this.failure});

  @override
  List<Object> get props => [failure];
}

class AppointmentSuccess extends AppointmentState {
  final String message;

  const AppointmentSuccess({required this.message});

  @override
  List<Object> get props => [message];
}

class ScannerPermissionGranted extends AppointmentState {}

class ScannerPermissionDenied extends AppointmentState {}

class Scanner<PERSON>lashState extends AppointmentState {
  final bool isOn;
  const ScannerFlashState({required this.isOn});

  @override
  List<Object> get props => [isOn];
}

class CodeScannedSuccess extends AppointmentState {
  final String code;
  const CodeScannedSuccess({required this.code});

  @override
  List<Object> get props => [code];
}

class ScannerError extends AppointmentState {
  final String error;
  const ScannerError({required this.error});

  @override
  List<Object> get props => [error];
}

class ProcessAppointmentBarCodeSuccess extends AppointmentState {
  final String message;
  const ProcessAppointmentBarCodeSuccess({required this.message});

  @override
  List<Object> get props => [message];
}

class ProcessAppointmentBarCodeFailure extends AppointmentState {
  final AppFailure failure;
  const ProcessAppointmentBarCodeFailure({required this.failure});

  @override
  List<Object> get props => [failure];
}

class ProcessAppointmentBarCodeLoading extends AppointmentState {}

class DownloadAppointmentPDFLoading extends AppointmentState {}

class DownloadAppointmentPDFSuccess extends AppointmentState {
  final String path;
  const DownloadAppointmentPDFSuccess({required this.path});

  @override
  List<Object> get props => [path];
}

class DownloadAppointmentPDFFailure extends AppointmentState {
  final AppFailure failure;
  const DownloadAppointmentPDFFailure({required this.failure});

  @override
  List<Object> get props => [failure];
}

class GetAppointmentPDFLoading extends AppointmentState {}

class GetAppointmentPDFSuccess extends AppointmentState {
  final Uint8List pdfBytes;
  const GetAppointmentPDFSuccess({required this.pdfBytes});

  @override
  List<Object> get props => [pdfBytes];
}

class GetAppointmentPDFFailure extends AppointmentState {
  final AppFailure failure;
  const GetAppointmentPDFFailure({required this.failure});

  @override
  List<Object> get props => [failure];
}
