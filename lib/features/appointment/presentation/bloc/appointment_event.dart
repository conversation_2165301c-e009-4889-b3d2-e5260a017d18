part of 'appointment_bloc.dart';

abstract class AppointmentEvent extends Equatable {
  const AppointmentEvent();

  @override
  List<Object> get props => [];
}

class FetchAppointmentEvent extends AppointmentEvent {
  final bool forceFetch;
  final String mobileNo;

  const FetchAppointmentEvent({
    this.forceFetch = false,
    required this.mobileNo,
  });

  @override
  List<Object> get props => [forceFetch, mobileNo];
}

class MakeAppointmentEvent extends AppointmentEvent {
  final String pID;
  final String doctorPractitioner;
  final String patientMobile;
  final double doctAmount;
  final bool hasFollowUp;
  final String appointmentDate;
  final bool hasMembership;
  const MakeAppointmentEvent({
    required this.pID,
    required this.doctorPractitioner,
    required this.patientMobile,
    required this.doctAmount,
    required this.hasFollowUp,
    required this.appointmentDate,
    required this.hasMembership,
  });

  @override
  List<Object> get props => [
        pID,
        doctorPractitioner,
        patientMobile,
        doctAmount,
        hasFollowUp,
        appointmentDate,
        hasMembership,
      ];
}

class ToggleScannerFlashEvent extends AppointmentEvent {
  const ToggleScannerFlashEvent();

  @override
  List<Object> get props => [];
}

class SwitchScannerCameraEvent extends AppointmentEvent {
  const SwitchScannerCameraEvent();

  @override
  List<Object> get props => [];
}

class CheckScannerPermissionEvent extends AppointmentEvent {
  final BuildContext context;

  const CheckScannerPermissionEvent({required this.context});

  @override
  List<Object> get props => [context];
}

class CodeScannedEvent extends AppointmentEvent {
  final String code;
  const CodeScannedEvent({required this.code});

  @override
  List<Object> get props => [code];
}

class ProcessAppointmentBarCodeEvent extends AppointmentEvent {
  final String queName;
  const ProcessAppointmentBarCodeEvent({required this.queName});

  @override
  List<Object> get props => [queName];
}

class DowloadAppointmentPDFEvent extends AppointmentEvent {
  final Uint8List pdfBytes;

  const DowloadAppointmentPDFEvent({
    required this.pdfBytes,
  });

  @override
  List<Object> get props => [pdfBytes];
}

class GetAppointmentPDFEvent extends AppointmentEvent {
  final String appointmentId;
  const GetAppointmentPDFEvent({required this.appointmentId});

  @override
  List<Object> get props => [appointmentId];
}
