import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hodan_hospital/core/config/logger/app_logger.dart';
import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/core/utils/helpers/bloc_helper.dart';
import 'package:hodan_hospital/features/appointment/domain/entities/appointment_entity.dart';
import 'package:hodan_hospital/features/appointment/domain/params/dowload_appointment_pdf_params.dart';
import 'package:hodan_hospital/features/appointment/domain/params/get_appointment_pdf.dart';
import 'package:hodan_hospital/features/appointment/domain/params/get_appointments_params.dart';
import 'package:hodan_hospital/features/appointment/domain/params/make_appointment_params.dart';
import 'package:hodan_hospital/features/appointment/domain/usecases/dowload_appointment_pdf_use_case.dart';
import 'package:hodan_hospital/features/appointment/domain/usecases/get_appointment_pdf_use_case.dart';
import 'package:hodan_hospital/features/appointment/domain/usecases/get_appointments_use_case.dart';
import 'package:hodan_hospital/features/appointment/domain/usecases/make_appointment_use_case.dart';
import 'package:hodan_hospital/features/appointment/domain/usecases/proccess_appointment_use_case.dart';
import 'package:hodan_hospital/core/services/scanner_permission_service.dart';
import 'package:mobile_scanner/mobile_scanner.dart';

import '../../domain/params/proccess_appointment_params.dart';

part 'appointment_event.dart';
part 'appointment_state.dart';

class AppointmentBloc extends Bloc<AppointmentEvent, AppointmentState> {
  final MakeAppointmentUseCase makeAppointmentUseCase;
  final GetAppointmentsUseCase getAppointmentsUseCase;
  final ProcessAppointmentUseCase processAppointmentUseCase;
  final DowloadAppointmentPDFUseCase dowloadAppointmentPDFUseCase;
  final GetAppointmentPDFUseCase getAppointmentPDFUseCase;
  late final MobileScannerController _scannerController;
  final ScannerPermissionService _scannerPermissionService =
      ScannerPermissionService();
  bool isFlashOn = false;

  AppointmentBloc({
    required this.makeAppointmentUseCase,
    required this.getAppointmentsUseCase,
    required this.processAppointmentUseCase,
    required this.dowloadAppointmentPDFUseCase,
    required this.getAppointmentPDFUseCase,
  }) : super(AppointmentInitial()) {
    _scannerController = MobileScannerController();

    on<MakeAppointmentEvent>(_onMakeAppointmentEvent,
        transformer: BlocHelper.debounceHelper());
    on<FetchAppointmentEvent>(_onFetchAppointmentEvent,
        transformer: BlocHelper.debounceHelper());
    on<CheckScannerPermissionEvent>(_onCheckScannerPermission);
    on<ToggleScannerFlashEvent>(_onToggleScannerFlash);
    on<SwitchScannerCameraEvent>(_onSwitchScannerCamera);
    on<CodeScannedEvent>(_onCodeScanned);
    on<ProcessAppointmentBarCodeEvent>(_onProcessAppointmentBarCode,
        transformer: BlocHelper.debounceHelper());
    on<DowloadAppointmentPDFEvent>(_onDownloadAppointmentPDF,
        transformer: BlocHelper.debounceHelper());
    on<GetAppointmentPDFEvent>(_onGetAppointmentPDF,
        transformer: BlocHelper.debounceHelper());
  }

  List<AppointmentEntity> _upcomingAppointments = [];
  List<AppointmentEntity> get upcomingAppointments => _upcomingAppointments;
  List<AppointmentEntity> _previousAppointments = [];
  List<AppointmentEntity> get previousAppointments => _previousAppointments;

  MobileScannerController get scannerController => _scannerController;

  // 🟢 Handle get appointments
  Future<void> _onFetchAppointmentEvent(
      FetchAppointmentEvent event, Emitter<AppointmentState> emit) async {
    await BlocHelper.handleEventAndEmit<List<AppointmentEntity>,
        AppointmentState>(
      emit: emit,
      loadingState: AppointmentLoading(),
      callUseCase: getAppointmentsUseCase(
          params: GetAppointmentsParams(
        forceFetch: event.forceFetch,
        mobileNo: event.mobileNo,
      )),
      onSuccess: (appointments) {
        if (appointments.isEmpty) {
          return AppointmentEmpty();
        }
        final now = DateTime.now();
        final todayStart =
            DateTime(now.year, now.month, now.day); // Start of today (00:00:00)
        // final todayEnd = todayStart
        // .add(const Duration(days: 1)); // Start of next day (00:00:00)

        _upcomingAppointments = appointments.where((appntmnt) {
          if (appntmnt.date == null) return false;
          // return !appntmnt.date!.isBefore(todayStart) &&
          // appntmnt.date!.isBefore(todayEnd);
          // Include today's appointments AND future appointments
          return !appntmnt.date!.isBefore(todayStart);
        }).toList();

        _previousAppointments = appointments.where((appntmnt) {
          if (appntmnt.date == null) return false;
          // Only appointments before today
          return appntmnt.date!.isBefore(todayStart);
        }).toList();
        return AppointmentLoaded(appointments: appointments);
      },
      onFailure: (failure) => AppointmentFailure(failure: failure),
    );
  }

  // 🟢 Handle get appointments
  Future<void> _onMakeAppointmentEvent(
      MakeAppointmentEvent event, Emitter<AppointmentState> emit) async {
    await BlocHelper.handleEventAndEmit<String, AppointmentState>(
      emit: emit,
      loadingState: MakeAppointmentLoading(),
      callUseCase: makeAppointmentUseCase(
          params: MakeAppointmentParams(
        pID: event.pID,
        doctorPractitioner: event.doctorPractitioner,
        patientMobile: event.patientMobile,
        doctAmount: event.doctAmount,
        hasFollowUp: event.hasFollowUp,
        appointmentDate: event.appointmentDate,
        hasMembership: event.hasMembership,
      )),
      onSuccess: (message) => AppointmentSuccess(message: message),
      onFailure: (failure) => AppointmentFailure(failure: failure),
    );
  }

  ///-------------------- Scan Section ---------------------

  /// Check Scanner Permission
  Future<void> _onCheckScannerPermission(
    CheckScannerPermissionEvent event,
    Emitter<AppointmentState> emit,
  ) async {
    try {
      // Use the new scanner permission service
      final hasPermission =
          await _scannerPermissionService.checkAndRequestCameraPermission(
        context: event.context,
      );

      if (hasPermission) {
        // Initialize scanner after permission granted
        await _initializeScanner();
        emit(ScannerPermissionGranted());
      } else {
        emit(ScannerPermissionDenied());
      }
    } catch (e) {
      AppLogger().error('Permission check error', error: e);
      emit(AppointmentFailure(
        failure: UnexpectedFailure(message: e.toString()),
      ));
    }
  }

  // Add this new method
  Future<void> _initializeScanner() async {
    try {
      await _scannerController.stop();
      await _scannerController.start();
    } catch (e) {
      AppLogger().error('Scanner initialization error', error: e);
    }
  }

  /// Toggle Scanner Flash
  Future<void> _onToggleScannerFlash(
    ToggleScannerFlashEvent event,
    Emitter<AppointmentState> emit,
  ) async {
    try {
      await _scannerController.toggleTorch();
      isFlashOn = !isFlashOn;

      // Make sure scanner is running
      // if (!_scannerController.isRunning) {
      //   // Re-initialize scanner if it was stopped
      //   AppLogger().info('Re-initializing scanner after toggling flash');
      //   await _initializeScanner();
      // }

      emit(ScannerFlashState(isOn: isFlashOn));
    } catch (e) {
      emit(AppointmentFailure(
        failure: UnexpectedFailure(message: e.toString()),
      ));
    }
  }

  Future<void> _onSwitchScannerCamera(
    SwitchScannerCameraEvent event,
    Emitter<AppointmentState> emit,
  ) async {
    try {
      await _scannerController.switchCamera();
    } catch (e) {
      emit(AppointmentFailure(
          failure: UnexpectedFailure(message: e.toString())));
    }
  }

  /// Code Scanned
  void _onCodeScanned(
    CodeScannedEvent event,
    Emitter<AppointmentState> emit,
  ) {
    try {
      final correctCode = 'https://hodanhospital.com';
      final scannedCode = event.code.trim();

      // Validate if code is empty
      if (scannedCode.isEmpty) {
        emit(const ScannerError(error: 'No code detected'));
        return;
      }

      if (scannedCode != correctCode) {
        emit(const ScannerError(error: 'Invalid code scanned'));
        return;
      }

      // success
      emit(CodeScannedSuccess(code: scannedCode));
    } catch (e) {
      emit(const ScannerError(error: 'Invalid code scanned'));
    }
  }

  /// Process Appointment Bar Code
  Future<void> _onProcessAppointmentBarCode(
    ProcessAppointmentBarCodeEvent event,
    Emitter<AppointmentState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<String, AppointmentState>(
      emit: emit,
      loadingState: ProcessAppointmentBarCodeLoading(),
      callUseCase: processAppointmentUseCase(
          params: ProcessAppointmentParams(queName: event.queName)),
      onSuccess: (message) =>
          ProcessAppointmentBarCodeSuccess(message: message),
      onFailure: (failure) =>
          ProcessAppointmentBarCodeFailure(failure: failure),
    );
  }

  /// Download Appointment PDF
  Future<void> _onDownloadAppointmentPDF(
    DowloadAppointmentPDFEvent event,
    Emitter<AppointmentState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<String, AppointmentState>(
      emit: emit,
      loadingState: DownloadAppointmentPDFLoading(),
      callUseCase: dowloadAppointmentPDFUseCase(
          params: DowloadAppointmentPDFParams(pdfBytes: event.pdfBytes)),
      onSuccess: (path) => DownloadAppointmentPDFSuccess(path: path),
      onFailure: (failure) => DownloadAppointmentPDFFailure(failure: failure),
    );
  }

  /// Get Appointment PDF
  Future<void> _onGetAppointmentPDF(
    GetAppointmentPDFEvent event,
    Emitter<AppointmentState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<Uint8List, AppointmentState>(
      emit: emit,
      loadingState: GetAppointmentPDFLoading(),
      callUseCase: getAppointmentPDFUseCase(
          params: GetAppointmentPDFParams(appointmentId: event.appointmentId)),
      onSuccess: (pdfBytes) => GetAppointmentPDFSuccess(pdfBytes: pdfBytes),
      onFailure: (failure) => GetAppointmentPDFFailure(failure: failure),
    );
  }

  /// Close Scanner
  @override
  Future<void> close() {
    _scannerController.dispose();
    return super.close();
  }
}
