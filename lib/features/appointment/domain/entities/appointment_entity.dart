import 'package:hodan_hospital/core/utils/extensions/app_date_extensions.dart';
import 'package:hodan_hospital/core/utils/extensions/app_number_extensions.dart';

class AppointmentEntity {
  final String appointmentID;
  final String patientId;
  final String patientName;
  final String practitioner;
  final String patientImage;
  final double paidAmount;
  final String appointmentSource;
  final DateTime? date;

  const AppointmentEntity({
    required this.appointmentID,
    required this.patientId,
    required this.patientName,
    required this.patientImage,
    required this.practitioner,
    required this.paidAmount,
    required this.appointmentSource,
    required this.date,
  });

  /// Formatted appointment date
  String get formattedDate => date?.toFormattedString() ?? '';

  /// formated appointment paid amount
  String get formattedPaidAmount => paidAmount.toMoneyString();
}
