import 'package:fpdart/fpdart.dart';
import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/core/use_cases/use_case.dart';
import 'package:hodan_hospital/features/appointment/domain/params/dowload_appointment_pdf_params.dart';
import 'package:hodan_hospital/features/appointment/domain/repositories/appointment_repository.dart';

class DowloadAppointmentPDFUseCase
    extends UseCase<String, DowloadAppointmentPDFParams> {
  final AppointmentRepository appointmentRepository;

  DowloadAppointmentPDFUseCase({required this.appointmentRepository});

  @override
  Future<Either<AppFailure, String>> call(
      {required DowloadAppointmentPDFParams params}) async {
    return await appointmentRepository.downloadAppointmentPDF(
      pdfBytes: params.pdfBytes,
    );
  }
}
