import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/core/use_cases/use_case.dart';
import 'package:hodan_hospital/features/appointment/domain/entities/appointment_entity.dart';
import 'package:hodan_hospital/features/appointment/domain/params/get_appointments_params.dart';
import 'package:hodan_hospital/features/appointment/domain/repositories/appointment_repository.dart';

class GetAppointmentsUseCase
    extends UseCase<List<AppointmentEntity>, GetAppointmentsParams> {
  final AppointmentRepository repository;
  GetAppointmentsUseCase({required this.repository});

  @override
  FutureEitherFailOr<List<AppointmentEntity>> call(
      {required GetAppointmentsParams params}) async {
    return await repository.getAppointments(
      forceFetch: params.forceFetch,
      mobileNo: params.mobileNo,
    );
  }
}
