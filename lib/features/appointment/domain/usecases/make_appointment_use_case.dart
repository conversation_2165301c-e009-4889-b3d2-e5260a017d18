import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/core/use_cases/use_case.dart';
import 'package:hodan_hospital/features/appointment/domain/params/make_appointment_params.dart';
import 'package:hodan_hospital/features/appointment/domain/repositories/appointment_repository.dart';

class MakeAppointmentUseCase extends UseCase<String, MakeAppointmentParams> {
  final AppointmentRepository repository;
  MakeAppointmentUseCase({required this.repository});

  @override
  FutureEitherFailOr<String> call(
      {required MakeAppointmentParams params}) async {
    return await repository.makeAppointment(
      pID: params.pID,
      doctorPractitioner: params.doctorPractitioner,
      patientMobile: params.patientMobile,
      doctAmount: params.doctAmount,
      hasFollowUp: params.hasFollowUp,
      appointmentDate: params.appointmentDate,
      hasMembership: params.hasMembership,
    );
  }
}
