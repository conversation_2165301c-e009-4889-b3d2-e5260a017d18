import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/core/use_cases/use_case.dart';
import 'package:hodan_hospital/features/appointment/domain/params/proccess_appointment_params.dart';
import 'package:hodan_hospital/features/appointment/domain/repositories/appointment_repository.dart';

class ProcessAppointmentUseCase
    extends UseCase<String, ProcessAppointmentParams> {
  final AppointmentRepository repository;
  ProcessAppointmentUseCase({required this.repository});

  @override
  FutureEitherFailOr<String> call(
      {required ProcessAppointmentParams params}) async {
    return await repository.processAppointmentBarCode(queName: params.queName);
  }
}
