import 'package:flutter/services.dart';
import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/core/use_cases/use_case.dart';
import 'package:hodan_hospital/features/appointment/domain/params/get_appointment_pdf.dart';
import 'package:hodan_hospital/features/appointment/domain/repositories/appointment_repository.dart';

class GetAppointmentPDFUseCase
    extends UseCase<Uint8List, GetAppointmentPDFParams> {
  final AppointmentRepository appointmentRepository;

  GetAppointmentPDFUseCase({required this.appointmentRepository});

  @override
  FutureEitherFailOr<Uint8List> call({
    required GetAppointmentPDFParams params,
  }) async {
    return appointmentRepository.getAppointmentPDF(
      appointmentId: params.appointmentId,
    );
  }
}
