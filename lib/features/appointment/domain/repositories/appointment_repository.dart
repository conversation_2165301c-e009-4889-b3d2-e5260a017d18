import 'package:flutter/services.dart';
import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/features/appointment/domain/entities/appointment_entity.dart';

abstract class AppointmentRepository {
  FutureEitherFailOr<List<AppointmentEntity>> getAppointments({
    required bool forceFetch,
    required String mobileNo,
  });
  FutureEitherFailOr<String> makeAppointment({
    required String pID,
    required String doctorPractitioner,
    required String patientMobile,
    required double doctAmount,
    required bool hasFollowUp,
    required String appointmentDate,
    required bool hasMembership,
  });
  FutureEitherFailOr<String> processAppointmentBarCode({
    required String queName,
  });

  FutureEitherFailOr<String> downloadAppointmentPDF({
    required Uint8List pdfBytes,
  });

  FutureEitherFailOr<Uint8List> getAppointmentPDF({
    required String appointmentId,
  });
}
