// import 'package:hodan_hospital/core/enums/database_failure_type.dart';
// import 'package:hodan_hospital/core/enums/database_operation_type.dart';
// import 'package:hodan_hospital/core/errors/database_error_handler.dart';
// import 'package:hodan_hospital/core/errors/app_failure.dart';
// import 'package:hodan_hospital/features/Order/data/models/order_model.dart';

// abstract class OrderDatabaseManager {
//   FutureEitherFailOr<List<int>> saveOrders({required List<OrderModel> orders});
//   // FutureEitherFailOr<bool> deleteAppointments();
//   FutureEitherFailOr<List<OrderModel>> readOrders();
// }

// class OrderDatabaseManagerImpl implements OrderDatabaseManager {
//   final DatabaseErrorHandler databaseErrorHandler;

//   OrderDatabaseManagerImpl({required this.databaseErrorHandler});

//   /// 🟢 **Save Orders**
//   @override
//   FutureEitherFailOr<List<int>> saveOrders(
//       {required List<OrderModel> orders}) async {
//     return await databaseErrorHandler.handleDatabaseOperation(
//       operationType: DatabaseOperationType.write,
//       operationFunction: () async {
//         // await _deleteOrders();
//         // final orderBox =
//         //     await databaseErrorHandler.databaseManager.getBox<OrderModel>();

//         // // Insert or Update order
//         // return orderBox.putMany(orders);
//         return []; // Simulating successful save with an empty list
//       },
//     );
//   }

//   /// 🔴 **Delete All Orders**
//   FutureEitherFailOr<void> _deleteOrders() async {
//     return await databaseErrorHandler.handleDatabaseOperation(
//       operationType: DatabaseOperationType.delete,
//       operationFunction: () async {
//         // final orderBox =
//         //     await databaseErrorHandler.databaseManager.getBox<OrderModel>();

//         // orderBox.removeAll();

//         return;
//       },
//     );
//   }

//   /// 🟡 **Read Orders**
//   @override
//   FutureEitherFailOr<List<OrderModel>> readOrders() async {
//     return await databaseErrorHandler.handleDatabaseOperation(
//       operationType: DatabaseOperationType.read,
//       operationFunction: () async {
//         // final orderBox =
//         //     await databaseErrorHandler.databaseManager.getBox<OrderModel>();

//         // // Find Orders and return
//         // final appointments = orderBox.query().build().find();
//         // if (appointments.isNotEmpty) {
//         //   return appointments;
//         // }

//         throw DatabaseFailure(
//           message: 'No Appointments found in the database',
//           failureType: DatabaseFailureType.noDataFound,
//           stackTrace: StackTrace.current,
//         );
//       },
//     );
//   }
// }
