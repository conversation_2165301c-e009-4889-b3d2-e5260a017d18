// import 'package:fpdart/fpdart.dart';
// import 'package:hodan_hospital/core/enums/cache_failure_type.dart';
// import 'package:hodan_hospital/core/errors/app_failure.dart';
// import 'package:hodan_hospital/features/Order/data/datasources/local/order_database_manager.dart';
// import 'package:hodan_hospital/features/Order/data/models/order_model.dart';

// abstract class OrderLocalDataSource {
//   FutureEitherFailOr<List<int>> saveOrders({required List<OrderModel> orders});
//   FutureEitherFailOr<List<OrderModel>> readOrders();
// }

// class OrderLocalDataSourceImpl implements OrderLocalDataSource {
//   // final OrderDatabaseManager orderDatabaseManager;

//   OrderLocalDataSourceImpl({
//     // required this.orderDatabaseManager,
//   });

//   @override
//   FutureEitherFailOr<List<OrderModel>> readOrders() async {
//     try {
//       return await orderDatabaseManager.readOrders();
//     } catch (e, stackTrace) {
//       return left(
//         CacheFailure(
//           message: 'Failed to read orders from Cache: ${e.toString()}',
//           failureType: CacheFailureType.readError,
//           stackTrace: stackTrace,
//         ),
//       );
//     }
//   }

//   @override
//   FutureEitherFailOr<List<int>> saveOrders(
//       {required List<OrderModel> orders}) async {
//     return await orderDatabaseManager.saveOrders(
//       orders: orders,
//     );
//   }
// }
