// ignore_for_file: unnecessary_null_comparison

import 'package:dio/dio.dart';
import 'package:fpdart/fpdart.dart';
import 'package:hodan_hospital/core/config/enviroment/enviroment_config.dart';
import 'package:hodan_hospital/core/config/logger/app_logger.dart';
import 'package:hodan_hospital/core/constants/api_end_points.dart';
import 'package:hodan_hospital/core/enums/http_method.dart';
import 'package:hodan_hospital/core/enums/payment_failure_type.dart';
import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/core/errors/http_error_handler.dart';
import 'package:hodan_hospital/core/network/api_client/dio_api_client.dart';
import 'package:hodan_hospital/core/utils/helpers/request_data.dart';
import 'package:hodan_hospital/core/utils/helpers/response_handler.dart';
import 'package:hodan_hospital/features/Order/data/models/order_model.dart';

abstract class OrderRemoteDataSource {
  FutureEitherFailOr<List<OrderModel>> getOrders({
    required String mobileNo,
  });
  FutureEitherFailOr<String> proccessOrder({
    required String orderId,
    required String mobileNo,
    required double amount,
  });
}

class OrderRemoteDataSourceImpl implements OrderRemoteDataSource {
  final DioApiClient dioApiClient;
  final HttpErrorHandler httpErrorHandler;

  const OrderRemoteDataSourceImpl(
      {required this.dioApiClient, required this.httpErrorHandler});

  @override
  FutureEitherFailOr<List<OrderModel>> getOrders({
    required String mobileNo,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (data) => OrderModel.fromJsonList(data as List<dynamic>),
      requestFunction: () => dioApiClient.request(
          method: HttpMethod.get,
          endPointUrl: ApiEndpoints.getOrders,
          data: RequestData.json({
            'mobile': mobileNo,
          })),
    );

    return ResponseHandler<List<OrderModel>>(response)
        .handleResponseAndExtractData(
      onFailure: (failure) => left(failure),
      onSuccess: (data) => right(data),
    );
  }

  @override
  FutureEitherFailOr<String> proccessOrder({
    required String orderId,
    required String mobileNo,
    required double amount,
  }) async {
    final cancelToken = CancelToken();

    final url = EnvironmentConfig.hodanMerchantApiUrl;

    final payload = _createPayload(
      serviceName: 'API_PURCHASE',
      patientMobile: mobileNo,
      // amount: amount,
      amount: 0.1, // for testing
    );

    final response = await httpErrorHandler.handlePaymentRequest(
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.post,
        endPointUrl: url,
        data: RequestData.json(payload),
        receiveTimeout: const Duration(seconds: 50),
        cancelToken: cancelToken,
      ),
    );

    return response.fold(
      (failure) {
        //
        cancelToken.cancel(
            'Request order saving cancel due to payment failure'); // Cancel on failure

        return left(failure);
      },
      (success) async {
        String? transactionId;

        // Extract orderId from successful payment
        if (success['params'] is Map) {
          transactionId = success['params']['transactionId']?.toString();
        }

        final appointmentResult = await _saveOrder(
          orderId: orderId,
        );

        return appointmentResult.fold(
          (appointmentFailure) async {
            // 3. Reverse payment if appointment fails
            if (transactionId != null) {
              await _handlePaymentReversal(
                transactionId: transactionId,
                patientMobile: mobileNo,
                amount: amount,
              );
            }
            return left(appointmentFailure);
          },
          (success) async {
            // 3. Reverse payment if appointment fails
            if (transactionId != null) {
              await _handlePaymentReversal(
                transactionId: transactionId,
                patientMobile: mobileNo,
                amount: amount,
              );
            }
            return right(success);
          },
        );
      },
    );
  }

  FutureEitherFailOr<String> _saveOrder({
    required String orderId,
  }) async {
    // try {
    // final response = await dioApiClient.request(
    //   method: HttpMethod.post,
    //   endPointUrl: ApiEndpoints.saveOrder,
    //   data: RequestData.json(
    //     {
    //       'sales_order_id': orderId,
    //     },
    //   ),
    // );

    //   // 🔹 Handle Invalid Response Format
    //   if (response == null || response.data is! Map<String, dynamic>) {
    //     return left(HttpFailure(
    //       message: "Invalid server response",
    //       failureType: HttpFailureType.serverError,
    //       statusCode: response.statusCode,
    //     ));
    //   }

    //   final responseBody = response.data as Map<String, dynamic>;

    //   if (response.statusCode == 200) {
    //     return right("Order Proccessed successfully");
    //   }

    //   // 🔹 Handle ValidationError (Status Code 417) with FrappeFailure
    //   if (response.statusCode == 417) {
    //     // Extract the human-readable error message from _server_messages
    //     final serverMessages = responseBody['_server_messages'];
    //     if (serverMessages != null && serverMessages is String) {
    //       try {
    //         final messages = jsonDecode(serverMessages) as List;
    //         if (messages.isNotEmpty) {
    //           final firstMessage =
    //               jsonDecode(messages.first) as Map<String, dynamic>;
    //           final errorMessage = firstMessage['message'] as String?;
    //           if (errorMessage != null) {
    //             return left(FrappeFailure(
    //               message: errorMessage,
    //               failureType: FrappeFailureType.validationError,
    //               serverMessage: errorMessage,
    //             ));
    //           }
    //         }
    //       } catch (e) {
    //         AppLogger().error("Failed to parse _server_messages", error: e);
    //       }
    //     }

    //     // Fallback to generic FrappeFailure if parsing fails
    //     return left(FrappeFailure(
    //       message: "Validation failed",
    //       failureType: FrappeFailureType.validationError,
    //     ));
    //   }

    //   // 🔹 Generic Error Handling
    //   return left(HttpFailure(
    //     message: "Failed to save appointment",
    //     failureType: HttpFailureType.serverError,
    //     statusCode: response.statusCode ?? 500,
    //   ));
    // } catch (error, stackTrace) {
    //   AppLogger().error(
    //     "Error saving appointment ",
    //     error: error,
    //     stackTrace: stackTrace,
    //   );
    //   return left(HttpFailure(
    //     message: "Failed to save order : $error",
    //     failureType: HttpFailureType.serverError,
    //   ));
    // }

    final response = await httpErrorHandler.handleRequest(
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.post,
        endPointUrl: ApiEndpoints.saveOrder,
        data: RequestData.json(
          {
            'sales_order_id': orderId,
          },
        ),
      ),
    );

    return response.fold(
      (failure) {
        //
        return left(failure);
      },
      (apiResponse) {
        //
        return right(apiResponse.apiMessage);
      },
    );

    // }
  }

  FutureEitherFailOr<String> _handlePaymentReversal({
    required String transactionId,
    required String patientMobile,
    required double amount,
  }) async {
    AppLogger()
        .info('Attempting to reverse payment for orderId: $transactionId');

    try {
      final response = await dioApiClient.request(
        method: HttpMethod.post,
        endPointUrl: EnvironmentConfig.hodanMerchantApiUrl,
        data: RequestData.json(
          _createPayload(
            serviceName: 'API_CANCELPURCHASE',
            patientMobile: patientMobile,
            amount: amount,
            transactionId: transactionId,
          ),
        ),
      );

      if (response.data is Map<String, dynamic>) {
        final responseData = response.data as Map<String, dynamic>;
        final isSuccess = responseData['responseCode'] == '2001' ||
            responseData['responseCode'] == '2000';

        if (isSuccess) {
          AppLogger().info('✅ Payment reversal successful');
          return left(PaymentFailure(
            message:
                'Appointment failed but payment has been reversed. Please try again.',
            failureType: PaymentFailureType.transactionError,
          ));
        }
      }

      AppLogger().error('❌ Payment reversal failed');
      return left(PaymentFailure(
        message:
            'Appointment failed and payment reversal failed. Please contact support.',
        failureType: PaymentFailureType.transactionError,
        statusCode: response.statusCode,
        apiMessage: response.data['responseMsg']?.toString(),
      ));
    } catch (error) {
      AppLogger().error('Error during payment reversal', error: error);
      return left(PaymentFailure(
        message: 'Failed to reverse payment: $error',
        failureType: PaymentFailureType.transactionError,
      ));
    }
  }

  /// Create a payload for the payment request
  Map<String, Object> _createPayload({
    required String serviceName,
    required String patientMobile,
    required double amount,
    String? transactionId, // Optional transactionId: Used for reversal
  }) {
    final serviceParams = {
      'merchantUid': EnvironmentConfig.hodanMerchantUid,
      // "merchantUid": EnvironmentConfig.rasiinMerchantUid,
      'apiUserId': EnvironmentConfig.hodanMerchantApiUserId,
      // "apiUserId": EnvironmentConfig.rasiinMerchantApiUserId,
      'apiKey': EnvironmentConfig.hodanMerchantApiKey,
      // "apiKey": EnvironmentConfig.rasiinMerchantApiKey,
      'paymentMethod': 'MWALLET_ACCOUNT',
      'payerInfo': {'accountNo': patientMobile},
    };

    // Add transaction-specific parameters based on service type
    if (serviceName == 'API_PURCHASE') {
      serviceParams['transactionInfo'] = {
        'referenceId': '********',
        'invoiceId': '********',
        'amount': amount,
        'currency': 'USD',
        'description': 'Medical Appointment Payment'
      };
    } else {
      // For API_CANCELPURCHASE
      serviceParams.addAll({
        'transactionId': transactionId ?? '',
        'referenceId': 'REF-${DateTime.now().millisecondsSinceEpoch}',
        'description': 'Reversal due to appointment saving failure'
      });
    }

    return {
      'schemaVersion': '1.0',
      'requestId': '***********',
      'timestamp': '2024-06-2 Africa',
      'channelName': 'WEB',
      'serviceName': serviceName,
      'serviceParams': serviceParams,
    };
  }
}
