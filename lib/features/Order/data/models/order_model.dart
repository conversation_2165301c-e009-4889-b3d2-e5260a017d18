/*
 {
                "name": "SAL-ORD-2025-01759",
                "transaction_date": "2025-03-02",
                "customer": "CUST-2025-00014",
                "patient": "PID-01168",
                "grand_total": 2.0,
                "status": "Completed",
                "delivery_date": "2025-03-02",
                "contact_mobile": "**********",
                "customer_group": "Membership",
                "items" : [],
                "patient_name": "QAALID C/WAHAAB"
            },
*/
import 'package:hodan_hospital/core/enums/parse_failure_type.dart';
import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/core/utils/extensions/app_date_extensions.dart';
// import 'package:objectbox/objectbox.dart';

// @Entity()
class OrderModel {
  // @Id()
  int id = 0;
  // @Index()
  final String orderID;
  // @Property(type: PropertyType.date)
  final DateTime? orderDate;
  final String customerName;
  final String patientID;
  final String patientName;
  // @Property(type: PropertyType.date)
  final DateTime? deliveryDate;
  final String status;
  final String contactMobile;
  final String customerGroup;
  final double grandTotal;
  // final List<OrderItemModel> items;
  /// ✅ ObjectBox relation for items
  // @Backlink('order')
  // ToMany<OrderItemModel> items = ToMany<OrderItemModel>();
  final List<OrderItemModel> items;

  OrderModel({
    required this.orderID,
    required this.orderDate,
    required this.customerName,
    required this.customerGroup,
    required this.patientID,
    required this.patientName,
    required this.deliveryDate,
    required this.status,
    required this.contactMobile,
    required this.grandTotal,
    required this.items,
  });

  Map<String, dynamic> toMap() => {
        'orderID': orderID,
        'orderDate': orderDate,
        'customerName': customerName,
        'patientID': patientID,
        'patientName': patientName,
        'deliveryDate': deliveryDate,
        'status': status,
        'contactMobile': contactMobile,
        'grandTotal': grandTotal,
        // "items": items.map((item) => item.toMap()).toList(),
      };

  factory OrderModel.fromJson(Map<String, dynamic> map) {
    try {
      final order = OrderModel(
        orderID: map['name'] ?? '',
        orderDate: (map['transaction_date'] as String?)?.toDateTime(),
        customerName: map['customer'] ?? '',
        customerGroup: map['customer_group'] ?? '',
        patientID: map['patient'] ?? '',
        patientName: map['patient_name'] ?? '',
        deliveryDate: (map['delivery_date'] as String?)?.toDateTime(),
        status: map['status'] ?? '',
        contactMobile: map['contact_mobile'] ?? '',
        grandTotal: (map['grand_total'] as num?)?.toDouble() ?? 0,
        items: OrderItemModel.fromJsonList(
          map['items'] as List<dynamic>? ?? [],
        ),
      );
      // final items = map['items'] as List<dynamic>?;
      // final orderItems = items != null && items.isNotEmpty
      //     ? OrderItemModel.fromJsonList(items)
      //     : <OrderItemModel>[]; // Default to an empty list if null or empty

      // Link each item back to the order
      // for (final item in orderItems) {
      //   item.order.target = order;
      // }

      // order.items.addAll(orderItems);

      // order.items = orderItems;

      return order;
    } catch (error, stackTrace) {
      throw ParsingFailure(
        // message: " Failed to parse OrderModel : ${error.toString()} ",
        message:
            "Failed to parse OrderModel (ID: ${map['name']}): ${error.toString()}",
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: OrderModel,
        stackTrace: stackTrace,
      );
    }
  }

  @override
  String toString() {
    return 'OrderModel(orderID: $orderID, orderDate: $orderDate, customerName: $customerName, patientID: $patientID, patientName: $patientName, deliveryDate: $deliveryDate, status: $status, contactMobile: $contactMobile, grandTotal: $grandTotal)';
  }

  // list from json
  static List<OrderModel> fromJsonList(List<dynamic> jsonList) {
    return jsonList.map((json) => OrderModel.fromJson(json)).toList();
  }
}

// @Entity()
class OrderItemModel {
  // @Id()
  int id = 0;

  final String itemCode;
  final String itemName;
  final double qty;
  final double rate;
  final double amount;

  // final order = ToOne<OrderModel>();

  OrderItemModel({
    required this.itemCode,
    required this.itemName,
    required this.qty,
    required this.rate,
    required this.amount,
  });

  factory OrderItemModel.fromJson(Map<String, dynamic> map) {
    try {
      return OrderItemModel(
        itemCode: map['item_code'] ?? '',
        itemName: map['item_name'] ?? '',
        qty: (map['qty'] as num?)?.toDouble() ?? 0,
        rate: (map['rate'] as num?)?.toDouble() ?? 0,
        amount: (map['amount'] as num?)?.toDouble() ?? 0,
      );
    } catch (e, s) {
      throw ParsingFailure(
        message: 'Failed to parse OrderItemModel: ${e.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: OrderItemModel,
        stackTrace: s,
      );
    }
  }

  Map<String, dynamic> toMap() => {
        'itemCode': itemCode,
        'itemName': itemName,
        'qty': qty,
        'rate': rate,
        'amount': amount,
      };

  // list from json
  static List<OrderItemModel> fromJsonList(List<dynamic> jsonList) {
    return jsonList.map((json) => OrderItemModel.fromJson(json)).toList();
  }
}
