import 'package:hodan_hospital/features/Order/data/models/order_model.dart';
import 'package:hodan_hospital/features/Order/domain/entities/order_entity.dart';

class OrderMapper {
  /// Converts a AppointmentEntity to a AppointmentModel
  static OrderModel entityToModel(OrderEntity entity) {
    // return OrderModel(
    //   orderID: entity.orderID,
    //   orderDate: entity.orderDate,
    //   customerName: entity.customerName,
    //   patientID: entity.patientID,
    //   patientName: entity.patientName,
    //   deliveryDate: entity.deliveryDate,
    //   status: entity.status,
    //   contactMobile: entity.contactMobile,
    //   grandTotal: entity.grandTotal,
    // );

    final orderModel = OrderModel(
      orderID: entity.orderID,
      orderDate: entity.orderDate,
      customerName: entity.customerName,
      customerGroup: entity.customerGroup,
      patientID: entity.patientID,
      patientName: entity.patientName,
      deliveryDate: entity.deliveryDate,
      status: entity.status,
      contactMobile: entity.contactMobile,
      grandTotal: entity.grandTotal,
      items: OrderItemMapper.entityListToModelList(entity.items),
    );

    /// Map and attach items + set parent reference
    // final items = OrderItemMapper.entityListToModelList(entity.items);
    // for (final item in items) {
    //   item.order.target = orderModel;
    // }
    // orderModel.items.addAll(items);

    return orderModel;
  }

  /// Converts a AppointmentModel to a AppointmentEntity
  static OrderEntity modelToEntity(OrderModel model) {
    return OrderEntity(
      orderID: model.orderID,
      orderDate: model.orderDate,
      customerName: model.customerName,
      customerGroup: model.customerGroup,
      patientID: model.patientID,
      patientName: model.patientName,
      deliveryDate: model.deliveryDate,
      status: model.status,
      contactMobile: model.contactMobile,
      grandTotal: model.grandTotal,
      // items: OrderItemMapper.modelListToEntityList(model.items),
      items: OrderItemMapper.modelListToEntityList(model.items),
    );
  }

  /// Converts a list of AppointmentModel to a list of AppointmentEntity
  static List<OrderEntity> modelListToEntityList(List<OrderModel> models) {
    return models.map((model) => modelToEntity(model)).toList();
  }

  /// Converts a list of OrderEntity to a list of OrderModel
  static List<OrderModel> entityListToModelList(List<OrderEntity> entities) {
    return entities.map((entity) => entityToModel(entity)).toList();
  }
}

class OrderItemMapper {
  /// Converts a OrderItemEntity to a OrderItemModel
  static OrderItemModel entityToModel(OrderItemEntity entity) {
    return OrderItemModel(
      itemCode: entity.itemCode,
      itemName: entity.itemName,
      qty: entity.qty,
      rate: entity.rate,
      amount: entity.amount,
    );
  }

  /// Converts a OrderItemModel to a OrderItemEntity
  static OrderItemEntity modelToEntity(OrderItemModel model) {
    return OrderItemEntity(
      itemCode: model.itemCode,
      itemName: model.itemName,
      qty: model.qty,
      rate: model.rate,
      amount: model.amount,
    );
  }

  /// Converts a list of OrderItemModel to a list of OrderItemEntity
  static List<OrderItemEntity> modelListToEntityList(
      List<OrderItemModel> models) {
    return models.map((model) => modelToEntity(model)).toList();
  }

  /// Converts a list of OrderItemEntity to a list of OrderItemModel
  static List<OrderItemModel> entityListToModelList(
      List<OrderItemEntity> entities) {
    return entities.map((entity) => entityToModel(entity)).toList();
  }
}
