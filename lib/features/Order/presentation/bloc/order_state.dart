part of 'order_bloc.dart';

sealed class OrderState extends Equatable {
  const OrderState();

  @override
  List<Object> get props => [];
}

final class OrderInitial extends OrderState {}

final class OrderLoading extends OrderState {}

final class OrderLoaded extends OrderState {
  final List<OrderEntity> orders;

  const OrderLoaded({required this.orders});

  @override
  List<Object> get props => [orders];
}

final class OrderFailure extends OrderState {
  final AppFailure appFailure;

  const OrderFailure({required this.appFailure});
}

final class OrderEmpty extends OrderState {}

// OrderProcessingState

final class OrderProcessing extends OrderState {}

final class OrderProcessingFailure extends OrderState {
  final AppFailure appFailure;

  const OrderProcessingFailure({required this.appFailure});

  @override
  List<Object> get props => [appFailure];
}

final class OrderProcessingSuccess extends OrderState {
  final String message;

  const OrderProcessingSuccess({required this.message});

  @override
  List<Object> get props => [message];
}
