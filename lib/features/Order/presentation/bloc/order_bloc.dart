import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/core/utils/helpers/bloc_helper.dart';
import 'package:hodan_hospital/features/Order/domain/entities/order_entity.dart';
import 'package:hodan_hospital/features/Order/domain/params/get_orders_params.dart';
import 'package:hodan_hospital/features/Order/domain/params/process_order_params.dart';
import 'package:hodan_hospital/features/Order/domain/usecases/get_orders_use_case.dart';
import 'package:hodan_hospital/features/Order/domain/usecases/process_order_use_case.dart';

part 'order_event.dart';
part 'order_state.dart';

class OrderBloc extends Bloc<OrderEvent, OrderState> {
  final GetOrdersUseCase getOrdersUseCase;
  final ProcessOrderUseCase processOrderUseCase;

  OrderBloc({
    required this.getOrdersUseCase,
    required this.processOrderUseCase,
  }) : super(OrderInitial()) {
    on<GetAllOrders>(
      _onGetAllOrders,
      transformer: BlocHelper.debounceHelper(),
    );
    on<ProccessOrder>(
      _onProccessOrder,
      transformer: BlocHelper.debounceHelper(),
    );
  }
  List<OrderEntity> _completedOrders = [];
  List<OrderEntity> get completedOrders => _completedOrders;
  List<OrderEntity> _pendingOrders = [];
  List<OrderEntity> get pendingOrders => _pendingOrders;

  Future<void> _onGetAllOrders(
    GetAllOrders event,
    Emitter<OrderState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<List<OrderEntity>, OrderState>(
        emit: emit,
        loadingState: OrderLoading(),
        callUseCase: getOrdersUseCase(
            params: GetOrdersParams(
          mobileNumber: event.mobileNumber,
          forceFetch: event.forceFetch,
        )),
        onSuccess: (orders) {
          if (orders.isEmpty) {
            return OrderEmpty();
          }

          _pendingOrders = orders.where((order) {
            if (order.status.isEmpty) return false;
            // return order.status.toLowerCase() == 'to bill';
            // return order.formattedStatus.toLowerCase() != 'completed';
            return !order.isCompleted;
          }).toList();

          _completedOrders = orders.where((order) {
            if (order.status.isEmpty) return false;
            // return order.status.toLowerCase() != 'to bill';
            // return order.formattedStatus.toLowerCase() == 'completed';
            return order.isCompleted;
          }).toList();

          return OrderLoaded(orders: orders);
        },
        onFailure: (failure) {
          _completedOrders = [];
          _pendingOrders = [];
          return OrderFailure(appFailure: failure);
        });
  }

  Future<void> _onProccessOrder(
    ProccessOrder event,
    Emitter<OrderState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<String, OrderState>(
      emit: emit,
      loadingState: OrderProcessing(),
      callUseCase: processOrderUseCase(
        params: ProcessOrderParams(
          orderId: event.orderId,
          mobileNo: event.mobileNo,
          amount: event.amount,
        ),
      ),
      onSuccess: (message) => OrderProcessingSuccess(message: message),
      onFailure: (failure) => OrderProcessingFailure(appFailure: failure),
    );
  }
}
