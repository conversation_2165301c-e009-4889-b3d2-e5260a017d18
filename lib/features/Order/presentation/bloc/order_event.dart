part of 'order_bloc.dart';

sealed class OrderEvent extends Equatable {
  const OrderEvent();

  @override
  List<Object> get props => [];
}

class GetAllOrders extends OrderEvent {
  final bool forceFetch;
  final String mobileNumber;

  const GetAllOrders({required this.mobileNumber, this.forceFetch = false});

  @override
  List<Object> get props => [mobileNumber, forceFetch];
}

class ProccessOrder extends OrderEvent {
  final String orderId;
  final String mobileNo;
  final double amount;

  const ProccessOrder({
    required this.orderId,
    required this.mobileNo,
    required this.amount,
  });

  @override
  List<Object> get props => [orderId, mobileNo, amount];
}
