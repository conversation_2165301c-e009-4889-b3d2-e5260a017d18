import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hodan_hospital/core/config/router/extension/navigation_extension.dart';
import 'package:hodan_hospital/core/config/theme/colors/app_colors_extension.dart';
import 'package:hodan_hospital/core/utils/extensions/build_context_extensions.dart';
import 'package:hodan_hospital/core/utils/helpers/snack_bar_helper.dart';
import 'package:hodan_hospital/features/Order/domain/entities/order_entity.dart';
import 'package:hodan_hospital/features/shared/presentation/blocs%20or%20cubits/shared%20bloc/shared_bloc.dart';
import 'package:hodan_hospital/features/shared/presentation/pages/pdf_viewer_page.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/animations/animated_app_bar.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_button.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_container.dart';
import 'package:hodan_hospital/core/enums/button_state.dart';
import 'package:hodan_hospital/features/Order/presentation/bloc/order_bloc.dart';
import 'package:hodan_hospital/core/utils/extensions/app_bloc_extensions.dart';

class OrderDetailsPage extends StatelessWidget {
  final OrderEntity order;

  const OrderDetailsPage({super.key, required this.order});

  // refresh orders
  Future<void> _refreshOrders(BuildContext context) async {
    final mobileNo = context.userBloc.currentUser?.phoneNumber ?? '';
    context.orderBloc.add(GetAllOrders(
      mobileNumber: mobileNo,
      forceFetch: true,
    ));
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = context.textTheme;

    return Scaffold(
      appBar: AnimatedAppBar(title: 'Order #${order.orderID}'),
      body: BlocConsumer<OrderBloc, OrderState>(
        listenWhen: (previous, current) =>
            current is OrderProcessing ||
            current is OrderProcessingSuccess ||
            current is OrderProcessingFailure,
        listener: (context, state) {
          if (state is OrderProcessing) {
            context.dialogCubit.showLoadingDialog(
              message: 'Processing Order...',
            );
          }
          if (state is OrderProcessingSuccess) {
            // close the dialog
            context.dialogCubit.closeDialog();

            // close the screen
            context.popRoute();

            // show success snackbar
            SnackBarHelper.showSuccessSnackBar(
              context,
              message: 'Order Processed Successfully',
              duration: const Duration(seconds: 10),
            );

            // refresh orders
            _refreshOrders(context);
          }
          if (state is OrderProcessingFailure) {
            context.dialogCubit.closeDialog();
            // show error snackbar
            SnackBarHelper.showErrorSnackBar(
              context,
              message: state.appFailure.getErrorMessage(),
            );
          }
        },
        builder: (context, state) {
          // return SingleChildScrollView(
          //   padding: const EdgeInsets.all(16),
          //   child: Column(
          //     crossAxisAlignment: CrossAxisAlignment.start,
          //     children: [
          //       SizedBox(height: 16.h),

          //       // Patient & Order Summary
          //       _buildSummaryCard(context),
          //       SizedBox(height: 25.h),

          //       // Items Section
          //       Text('Order Items : ',
          //           style: textTheme.titleMedium
          //               ?.copyWith(fontWeight: FontWeight.bold)),
          //       SizedBox(height: 8.h),
          //       _buildItemsTable(context: context),
          //       SizedBox(height: 24.h),

          //       // Payment Summary
          //       _buildPaymentSummary(context),
          //       SizedBox(height: 16.h),

          //       // Action Buttons
          //       _buildActionButtons(context),
          //     ],
          //   ),
          // );
          return CustomScrollView(
            slivers: [
              // Scrollable content
              SliverPadding(
                padding: const EdgeInsets.all(16),
                sliver: SliverList(
                  delegate: SliverChildListDelegate([
                    _buildSummaryCard(context),
                    SizedBox(height: 25.h),
                    Text('Order Items : ',
                        style: textTheme.titleMedium
                            ?.copyWith(fontWeight: FontWeight.bold)),
                    SizedBox(height: 8.h),
                    _buildItemsTable(context: context),
                    SizedBox(height: 24.h),
                    _buildPaymentSummary(context),
                    SizedBox(height: 16.h),
                  ]),
                ),
              ),

              // Fixed bottom buttons
              SliverFillRemaining(
                hasScrollBody: false, // Prevents unwanted scrolling
                child: Container(
                  padding: const EdgeInsets.all(16),
                  alignment: Alignment.bottomCenter,
                  child: _buildActionButtons(context),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildSummaryCard(BuildContext context) {
    final textTheme = context.textTheme;

    return CustomContainer(
      color: context.appColors.primaryColor.withValues(alpha: 0.1),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Patient Info
            Text('Patient Information',
                style: textTheme.titleLarge?.copyWith(
                  fontSize: 18.sp,
                )),
            SizedBox(height: 8.h),
            _buildInfoRow(Icons.person, 'Name', order.patientName),
            _buildInfoRow(
                Icons.medical_services, 'Patient ID', order.patientID),
                // _buildInfoRow(
                // Icons.person_2, 'Doctor', order.doctor),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, size: 20, color: Colors.grey),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(label,
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 13.sp,
                    )),
                SizedBox(height: 2.h),
                Text(value,
                    style: const TextStyle(
                      fontWeight: FontWeight.w500,
                    )),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildItemsTable({
    required BuildContext context,
  }) {
    final textTheme = context.textTheme;
    final appColors = context.appColors;

    return CustomContainer(
      child: Column(
        children: [
          // Table Header
          Container(
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            decoration: BoxDecoration(
              color: appColors.primaryColor.withValues(alpha: 0.05),
              borderRadius:
                  const BorderRadius.vertical(top: Radius.circular(8)),
            ),
            child: const Row(
              children: [
                Expanded(
                    flex: 3,
                    child: Text('Item',
                        style: TextStyle(fontWeight: FontWeight.bold))),
                Expanded(
                    child: Text('Qty',
                        style: TextStyle(fontWeight: FontWeight.bold),
                        textAlign: TextAlign.center)),
                Expanded(
                    child: Text('Amount',
                        style: TextStyle(fontWeight: FontWeight.bold),
                        textAlign: TextAlign.right)),
              ],
            ),
          ),

          // Items List
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: order.items.length,
            separatorBuilder: (context, index) => const Divider(height: 1),
            itemBuilder: (context, index) {
              final item = order.items[index];
              return Padding(
                padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 16.w),
                child: Row(
                  children: [
                    Expanded(
                      flex: 3,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(item.itemName, style: textTheme.bodyMedium),
                          if (item.itemCode.isNotEmpty)
                            Text(item.itemCode,
                                style: textTheme.bodySmall
                                    ?.copyWith(color: Colors.grey)),
                        ],
                      ),
                    ),
                    Expanded(
                      child: Text(item.qty.toString(),
                          textAlign: TextAlign.center,
                          style: textTheme.bodyMedium),
                    ),
                    Expanded(
                      child: Text(item.formattedAmount,
                          textAlign: TextAlign.right,
                          style: textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w500,
                          )),
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentSummary(BuildContext context) {
    final textTheme = context.textTheme;
    final hasDiscount = order.hasMembership;
    final discount = order.discountAmount;
    final total = order.formattedGrandTotal;
    final amountToPay = order.formattedAmountToPay;

    return Card(
      elevation: 0,
      color: Colors.transparent,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            if (hasDiscount) ...[
              _buildSummaryRow(
                textTheme: textTheme,
                label: 'Original Total',
                value: total,
                isStrikethrough: true,
              ),
              _buildSummaryRow(
                textTheme: textTheme,
                label: 'Membership Discount',
                value: '-\$ $discount',
              ),
              const Divider(),
            ],
            _buildSummaryRow(
              textTheme: textTheme,
              label: 'Amount to Pay',
              value: amountToPay,
              isTotal: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryRow({
    required TextTheme textTheme,
    required String label,
    required String value,
    bool isTotal = false,
    bool isStrikethrough = false,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Text(
              label,
              style: TextStyle(
                fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
                fontSize: 14.sp,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          SizedBox(width: 30.w),
          Text(
            value,
            style: TextStyle(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              fontSize: isTotal ? 18.sp : 14.sp,
              decoration: isStrikethrough ? TextDecoration.lineThrough : null,
              color: isStrikethrough ? Colors.grey : null,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    final isEnabled = !order.isCompleted;
    if (!isEnabled) {
      return BlocConsumer<SharedBloc, SharedState>(
        buildWhen: (_, currentState) {
          return currentState is GenerateOrderPdfLoading ||
              currentState is GenerateOrderPdfSuccess ||
              currentState is GenerateOrderPdfFailure;
        },
        listener: (context, state) {
          if (state is GenerateOrderPdfFailure) {
            final errorMessage = state.failure.getErrorMessage();
            SnackBarHelper.showErrorSnackBar(context, message: errorMessage);
          }
          if (state is GenerateOrderPdfSuccess) {
            final pdf = state.pdfBytes;
            if (context.mounted) {
              context.pushRoute(PdfViewerPage(
                pdfBytes: pdf,
                onDownload: () {
                  //
                },
                title: 'Order PDF',
              ));
            }
          }
        },
        builder: (context, state) {
          final isLoading = state is GenerateOrderPdfLoading;
          return CustomButton(
            buttonState: isLoading ? ButtonState.loading : ButtonState.normal,
            width: 350,
            buttonText: 'Generate PDF',
            onTap: () async {
              context.sharedBloc.add(GenerateOrderPdf(id: order.orderID));
            },
          );
        },
      );
    } else {
      return CustomButton(
        width: 300,
        buttonText: 'Pay Order',
        onTap: () {
          // show bottom sheet to pay order
          _showPaymentConfirmationSheet(context);
        },
      );
    }
  }

  void _showPaymentConfirmationSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (ctx) {
        final textTheme = context.textTheme;
        final appColors = context.appColors;
        return Padding(
          padding: EdgeInsets.only(
            left: 16,
            right: 16,
            top: 24,
            bottom: MediaQuery.of(ctx).viewInsets.bottom + 24,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Title
              Text(
                'Confirm Payment',
                style: textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 25.h),

              // Order Summary
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text('Amount to Pay', style: textTheme.bodyLarge),
                  Text(
                    order.formattedAmountToPay,
                    style: textTheme.titleMedium?.copyWith(
                      color: appColors.primaryColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),

              SizedBox(height: 40.h),

              // Action Buttons
              Row(
                children: [
                  Expanded(
                    child: CustomButton(
                      buttonText: 'Cancel',
                      backgroundColor: Colors.grey.shade300,
                      // textColor: Colors.black,
                      onTap: () => Navigator.pop(ctx),
                    ),
                  ),
                  SizedBox(width: 15.w),
                  Expanded(
                    child: CustomButton(
                      buttonText: 'Confirm',
                      onTap: () {
                        Navigator.pop(ctx);
                        final mobile =
                            context.userBloc.currentUser?.phoneNumber ?? '';
                        // Process the order payment
                        context.orderBloc.add(
                          ProccessOrder(
                            orderId: order.orderID,
                            mobileNo: mobile,
                            amount: order.amountToPay,
                          ),
                        );
                        //   ProcessOrder(
                        //     orderId: order.orderID,
                        //     mobileNo: order.mobileNo,
                        //     amount: order.amountToPay,
                        //   ),
                        // );
                      },
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }
}
