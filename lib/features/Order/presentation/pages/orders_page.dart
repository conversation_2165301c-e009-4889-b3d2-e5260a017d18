import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hodan_hospital/core/config/router/extension/navigation_extension.dart';
import 'package:hodan_hospital/core/config/theme/colors/app_colors_extension.dart';
import 'package:hodan_hospital/core/enums/loading_type.dart';
import 'package:hodan_hospital/core/utils/extensions/app_bloc_extensions.dart';
import 'package:hodan_hospital/core/utils/extensions/build_context_extensions.dart';
import 'package:hodan_hospital/features/Order/domain/entities/order_entity.dart';
import 'package:hodan_hospital/features/Order/presentation/bloc/order_bloc.dart';
import 'package:hodan_hospital/features/Order/presentation/pages/order_details_page.dart';
import 'package:hodan_hospital/features/Order/presentation/widgets/order_card_widget.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/animations/animated_app_bar.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_list_grid_view.dart';

class OrdersPage extends StatefulWidget {
  const OrdersPage({super.key});

  @override
  State<OrdersPage> createState() => _OrdersPageState();
}

class _OrdersPageState extends State<OrdersPage>
    with SingleTickerProviderStateMixin {
  @override
  void initState() {
    super.initState();
    _refreshOrders(forceFetch: false);
  }

  Future<void> _refreshOrders({
    bool forceFetch = true,
  }) async {
    final orderBloc = context.orderBloc;
    final canRefresh = forceFetch ||
        (orderBloc.pendingOrders.isEmpty && orderBloc.completedOrders.isEmpty);
    final mobileNo = context.userBloc.currentUser?.phoneNumber ?? '';
    if (canRefresh) {
      orderBloc.add(GetAllOrders(
        forceFetch: forceFetch,
        mobileNumber: mobileNo,
      ));
    }
  }

  @override
  Widget build(BuildContext context) {
    final appColors = context.appColors;
    final textTheme = context.textTheme;
    return DefaultTabController(
      length: 2,
      child: Scaffold(
        // floatingActionButton: const SupportFABAction(),
        // floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
        appBar: AnimatedAppBar(
          title: 'My Orders',
          appBarHeight: 120,
          bottom: TabBar(
            labelColor: appColors.whiteColor,
            unselectedLabelColor: appColors.whiteColor.withValues(alpha: 0.5),
            labelStyle: textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
            ),
            tabs: const [
              Tab(text: 'Pending'),
              Tab(text: 'Completed'),
            ],
          ),
        ),
        body: BlocBuilder<OrderBloc, OrderState>(
          builder: (context, state) {
            return RefreshIndicator.adaptive(
              onRefresh: _refreshOrders,
              child: TabBarView(
                children: [
                  // Today's Orders Tab
                  _buildOrderList(
                    context,
                    orders: context.orderBloc.pendingOrders,
                    state: state,
                    emptyMessage: 'No pending orders',
                  ),
                  // Previous Orders Tab
                  _buildOrderList(
                    context,
                    orders: context.orderBloc.completedOrders,
                    state: state,
                    emptyMessage: 'No completed orders',
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildOrderList(
    BuildContext context, {
    required List<OrderEntity> orders,
    required OrderState state,
    required String emptyMessage,
  }) {
    return RefreshIndicator.adaptive(
      onRefresh: _refreshOrders,
      child: CustomListGridView<OrderEntity>(
        isLoading: state is OrderLoading,
        isEmpty: state is OrderEmpty,
        items: orders,
        contentType: LoadingType.listView,
        itemBuilder: (context, order) {
          return OrderCardWidget(
            order: order,
            onTap: () {
              // Handle order tap
              context.pushRoute(OrderDetailsPage(order: order));
            },
          );
        },
        emptyDataBuilder: () {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.receipt_long_outlined,
                  size: 48,
                  color: Theme.of(context)
                      .colorScheme
                      .onSurface
                      .withValues(alpha: 0.3),
                ),
                const SizedBox(height: 16),
                Text(
                  emptyMessage,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: Theme.of(context)
                            .colorScheme
                            .onSurface
                            .withValues(alpha: 0.5),
                      ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
