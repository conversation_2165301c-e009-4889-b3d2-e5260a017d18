import 'package:flutter/material.dart';
import 'package:hodan_hospital/core/config/theme/colors/app_colors_extension.dart';
import 'package:hodan_hospital/core/utils/extensions/build_context_extensions.dart';
import 'package:hodan_hospital/features/Order/domain/entities/order_entity.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_container.dart';

class OrderCardWidget extends StatelessWidget {
  final OrderEntity order;
  final VoidCallback? onTap;

  const OrderCardWidget({
    super.key,
    required this.order,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = context.textTheme;
    final appColors = context.appColors;
    return CustomContainer(
      // margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header Row
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    order.orderID,
                    style: textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 10),
                    decoration: BoxDecoration(
                      color: _getStatusColor(order.formattedStatus),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      // order.status,
                      order.formattedStatus,
                      style: textTheme.labelMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: appColors.whiteColor,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),

              // Patient Info
              Text(
                '${order.patientName} (${order.patientID})',
                style: textTheme.bodyMedium,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 12),

              // Order Details
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  _buildDetailItem(
                    context,
                    icon: Icons.calendar_today,
                    title: 'Order Date',
                    value: order.formattedOrderDate,
                  ),
                  _buildDetailItem(
                    context,
                    icon: Icons.delivery_dining,
                    title: 'Delivery Date',
                    value: order.formattedDeliveryDate,
                  ),
                  _buildDetailItem(
                    context,
                    icon: Icons.attach_money,
                    title: 'Total',
                    value: '${order.grandTotal.toStringAsFixed(2)} \$',
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String value,
  }) {
    final textTheme = context.textTheme;
    final appColors = context.appColors;
    return Column(
      children: [
        Icon(icon, size: 20, color: appColors.primaryColor),
        const SizedBox(height: 4),
        Text(
          title,
          style: textTheme.labelSmall,
        ),
        Text(
          value,
          style: textTheme.bodySmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return Colors.green;
      case 'to deliver and bill':
      case 'to bill':
        return Colors.orange;
      case 'processing':
        return Colors.blue;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}
