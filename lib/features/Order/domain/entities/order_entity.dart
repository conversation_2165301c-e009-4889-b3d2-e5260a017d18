import 'package:hodan_hospital/core/utils/extensions/app_date_extensions.dart';
import 'package:hodan_hospital/core/utils/extensions/app_number_extensions.dart';

class OrderEntity {
  final String orderID;
  final DateTime? orderDate;
  final String customerName;
  final String customerGroup;
  final String patientID;
  final String patientName;
  final DateTime? deliveryDate;
  final String status;
  final String contactMobile;
  final double grandTotal;
  final List<OrderItemEntity> items;

  OrderEntity({
    required this.orderID,
    required this.orderDate,
    required this.customerName,
    required this.customerGroup,
    required this.patientID,
    required this.patientName,
    required this.deliveryDate,
    required this.status,
    required this.contactMobile,
    required this.grandTotal,
    required this.items,
  });

  // get total items quantity
  double get totalItemsQty => items.fold(0, (sum, item) => sum + item.qty);

  // get formatted order date
  String get formattedOrderDate {
    return orderDate?.toFormattedString() ?? 'N/A';
  }

  // get formatted delivery date
  String get formattedDeliveryDate {
    return deliveryDate?.toFormattedString() ?? 'N/A';
  }

  // get formatted grand total
  String get formattedGrandTotal {
    // return grandTotal.toMoneyString();
    return '\$ ${grandTotal.toStringAsFixed(2)}';
  }

  // get formatted status
  String get formattedStatus {
    if (status == 'To Deliver and Bill') {
      return 'To Bill';
    }
    if (status == 'To Deliver') {
      return 'Completed';
    }
    return status;
  }

  //
  bool get isCompleted {
    return formattedStatus.toLowerCase() == 'completed';
  }

  //
  /// Check if customer has membership
  bool get hasMembership => customerGroup == 'Membership';

  /// Auto-calculated discount amount
  double get discountAmount => hasMembership ? grandTotal * 0.5 : 0.0;

  /// Formatted discount string
  // String get formattedDiscount => discountAmount.toMoneyString();
  String get formattedDiscount => '\$ ${discountAmount.toStringAsFixed(2)}';

  /// Final amount to pay (after discount)
  double get amountToPay => grandTotal - discountAmount;

  /// Formatted final payment
  // String get formattedAmountToPay => amountToPay.toMoneyString();
  String get formattedAmountToPay => '\$ ${amountToPay.toStringAsFixed(2)}';

  @override
  String toString() {
    return 'OrderEntity(orderID: $orderID, orderDate: $orderDate, customerName: $customerName, patientID: $patientID, patientName: $patientName, deliveryDate: $deliveryDate, status: $status, contactMobile: $contactMobile, grandTotal: $grandTotal, items: $items)';
  }
}

class OrderItemEntity {
  final String itemCode;
  final String itemName;
  final double qty;
  final double rate;
  final double amount;

  OrderItemEntity({
    required this.itemCode,
    required this.itemName,
    required this.qty,
    required this.rate,
    required this.amount,
  });

  // get formatted amount
  // String get formattedAmount => amount.toMoneyString();
  String get formattedAmount => '\$ ${amount.toStringAsFixed(2)}';

  // get formatted rate
  String get formattedRate {
    return rate.toMoneyString();
  }

  @override
  String toString() {
    return 'OrderItemEntity(itemCode: $itemCode, itemName: $itemName, qty: $qty, rate: $rate, amount: $amount)';
  }
}
