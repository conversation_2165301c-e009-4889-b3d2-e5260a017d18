import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/core/use_cases/use_case.dart';
import 'package:hodan_hospital/features/Order/domain/params/process_order_params.dart';
import 'package:hodan_hospital/features/Order/domain/repositories/order_repository.dart';

class ProcessOrderUseCase extends UseCase<String, ProcessOrderParams> {
  final OrderRepository orderRepository;

  ProcessOrderUseCase({required this.orderRepository});

  @override
  FutureEitherFailOr<String> call({
    required ProcessOrderParams params,
  }) async {
    return await orderRepository.proccessOrder(
      orderId: params.orderId,
      mobileNo: params.mobileNo,
      amount: params.amount,
    );
  }
}
