import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/core/use_cases/use_case.dart';
import 'package:hodan_hospital/features/Order/domain/entities/order_entity.dart';
import 'package:hodan_hospital/features/Order/domain/params/get_orders_params.dart';
import 'package:hodan_hospital/features/Order/domain/repositories/order_repository.dart';

class GetOrdersUseCase extends UseCase<List<OrderEntity>, GetOrdersParams> {
  final OrderRepository orderRepository;

  GetOrdersUseCase({required this.orderRepository});

  @override
  FutureEitherFailOr<List<OrderEntity>> call(
      {required GetOrdersParams params}) async {
    return await orderRepository.getAllOrders(
      forceFetch: params.forceFetch,
      mobileNumber: params.mobileNumber,
    );
  }
}
