import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/features/Order/domain/entities/order_entity.dart';

abstract class OrderRepository {
  FutureEitherFailOr<List<OrderEntity>> getAllOrders({
    required bool forceFetch,
    required String mobileNumber,
  });

  FutureEitherFailOr<String> proccessOrder({
    required String orderId,
    required String mobileNo,
    required double amount,
  });
}
