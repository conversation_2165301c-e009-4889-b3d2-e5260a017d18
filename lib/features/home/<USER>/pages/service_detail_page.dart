import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hodan_hospital/core/config/theme/colors/app_colors_extension.dart';
import 'package:hodan_hospital/core/utils/extensions/build_context_extensions.dart';
import 'package:hodan_hospital/features/home/<USER>/model/services_model.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/animations/animated_app_bar.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_container.dart';

class ServiceDetailPage extends StatelessWidget {
  final ServicesModel service;
  const ServiceDetailPage({super.key, required this.service});

  @override
  Widget build(BuildContext context) {
    final appColors = context.appColors;
    final textTheme = context.textTheme;

    return Scaffold(
      appBar: AnimatedAppBar(
        duration: Duration.zero,
        title: service.name,
      ),
      body: SizedBox.expand(
        child: Stack(
          // fit: StackFit.expand,
          children: [
            // 🔹 Background Banner
            Hero(
              tag: service.name,
              child: Container(
                height: context.screenHeight * 0.3,
                width: double.infinity,
                decoration: BoxDecoration(
                  image: DecorationImage(
                    // image: AssetImage(Assets.images.png.banner3.path),
                    image: AssetImage(service.imagePath),
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            ),

            // 🔹 Foreground Content (slides over banner)
            Positioned(
              top: context.screenHeight * 0.22, // slightly below banner height
              left: 0,
              right: 0,
              bottom: 0,
              child: CustomContainer(
                // width: double.infinity,
                padding: EdgeInsets.symmetric(
                  vertical: 20.h,
                  horizontal: 20.w,
                ),
                decoration: BoxDecoration(
                  color: appColors.cardColor,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(50.r),
                    topRight: Radius.circular(50.r),
                  ),
                ),
                child: SingleChildScrollView(
                  physics: const BouncingScrollPhysics(),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Title
                      Text(
                        service.name,
                        style: textTheme.headlineSmall?.copyWith(
                          color: appColors.primaryColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),

                      // Description
                      SizedBox(height: 8.h),
                      Text(
                        service.detailDescription ?? service.description,
                        style: textTheme.bodyLarge,
                      ),

                      // Features
                      if (service.features != null &&
                          service.features!.isNotEmpty) ...[
                        SizedBox(height: 20.h),
                        Text(
                          'What We Offer',
                          style: textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        SizedBox(height: 8.h),
                        ...service.features!.map((f) => Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Icon(Icons.check_circle,
                                    size: 18, color: appColors.primaryColor),
                                SizedBox(width: 6.w),
                                Expanded(
                                  child: Text(
                                    f,
                                    style: textTheme.bodyMedium,
                                  ),
                                ),
                              ],
                            )),
                      ],

                      // Location & Contact
                      SizedBox(height: 20.h),
                      // if (service.location != null ||
                      //     service.contactInfo != null)
                      //   Card(
                      //     color: appColors.cardColor,
                      //     elevation: 2,
                      //     shape: RoundedRectangleBorder(
                      //         borderRadius: BorderRadius.circular(10.r)),
                      //     child: Padding(
                      //       padding: EdgeInsets.all(12.w),
                      //       child: Column(
                      //         crossAxisAlignment: CrossAxisAlignment.start,
                      //         children: [
                      //           if (service.department != null)
                      //             Text("Department: ${service.department!}",
                      //                 style: textTheme.bodyMedium),
                      //           if (service.location != null)
                      //             Text("Location: ${service.location!}",
                      //                 style: textTheme.bodyMedium),
                      //           if (service.contactInfo != null)
                      //             Text("Contact: ${service.contactInfo!}",
                      //                 style: textTheme.bodyMedium),
                      //         ],
                      //       ),
                      //     ),
                      //   ),
                      // More content...
                      // SizedBox(height: 500), // Just to show scroll works
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
