import 'package:flutter/material.dart';
import 'package:hodan_hospital/features/home/<USER>/model/packages_grid_view_model.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/animations/animated_app_bar.dart';

class PackageDetailsPage extends StatelessWidget {
  final PackagesGridViewModel packagesGridViewModel;
  const PackageDetailsPage({super.key, required this.packagesGridViewModel});

  @override
  Widget build(BuildContext context) {
    // final size = MediaQuery.of(context).size;
    // final textTheme = context.textTheme;
    // final appColors = context.appColors;
    return Scaffold(
      appBar: AnimatedAppBar(
        // backgroundColor: appColors.transparentColor,
        // title: packagesGridViewModel.title,
        title: 'Package Details',
      ),
      body: Stack(
        alignment: Alignment.topCenter,
        // fit: StackFit.expand,
        children: [
          Positioned.fill(
            child: <PERSON>Viewer(
              child: Image.asset(
                packagesGridViewModel.packageDetailImagePath,
                // fit: BoxFit.cover,
                // color: Colors.black.withOpacity(0.5),
                // colorBlendMode: BlendMode.darken,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
