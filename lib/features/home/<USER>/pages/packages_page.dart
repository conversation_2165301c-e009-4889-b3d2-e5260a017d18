// stateless widget named PackagesPage
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hodan_hospital/core/config/router/extension/navigation_extension.dart';
import 'package:hodan_hospital/core/enums/loading_type.dart';
import 'package:hodan_hospital/core/utils/extensions/build_context_extensions.dart';
import 'package:hodan_hospital/features/home/<USER>/model/packages_grid_view_model.dart';
import 'package:hodan_hospital/features/home/<USER>/pages/package_details_page.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/animations/animated_app_bar.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_container.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_list_grid_view.dart';

class PackagesPage extends StatelessWidget {
  const PackagesPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AnimatedAppBar(
        title: 'Packages',
      ),
      body: CustomListGridView<PackagesGridViewModel>(
        items: PackagesGridViewModel.data,
        isLoading: false,
        isEmpty: PackagesGridViewModel.data.isEmpty,
        // padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 40.h),
        showFooter: false,
        contentType: LoadingType.gridView,
        childAspectRatio: 0.8,
        itemBuilder: (context, item) {
          return _buildPackageCard(context, item);
        },
        onRefresh: () {},
      ),
    );
  }

  InkWell _buildPackageCard(BuildContext context, PackagesGridViewModel item) {
    return InkWell(
      // onTap: () => item.onTap?.call(context),
      onTap: () {
        context.pushRoute(PackageDetailsPage(
          packagesGridViewModel: item,
        ));
      },
      child: CustomContainer(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          // crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Image.asset(
              item.imagePath,
              width: 50.w,
              height: 50.h,
            ),
            SizedBox(height: 10.h),
            Text(
              item.title,
              textAlign: TextAlign.center,
              maxLines: 2,
              style: context.textTheme.bodyLarge?.copyWith(
                fontSize: 13.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
