import 'package:cached_network_image/cached_network_image.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hodan_hospital/core/config/logger/app_logger.dart';
import 'package:hodan_hospital/core/config/router/extension/navigation_extension.dart';
import 'package:hodan_hospital/core/config/theme/colors/app_colors_extension.dart';
import 'package:hodan_hospital/core/enums/loading_type.dart';
import 'package:hodan_hospital/core/utils/extensions/app_bloc_extensions.dart';
import 'package:hodan_hospital/core/utils/extensions/build_context_extensions.dart';
import 'package:hodan_hospital/features/doctor/domain/entities/banner_entity.dart';
import 'package:hodan_hospital/features/doctor/presentation/bloc/doctor_bloc.dart';
import 'package:hodan_hospital/features/doctor/presentation/pages/doctors_page.dart';
import 'package:hodan_hospital/features/home/<USER>/model/home_gridview_model.dart';
import 'package:hodan_hospital/features/home/<USER>/model/service_banner_model.dart';
import 'package:hodan_hospital/features/home/<USER>/widgets/banner_carousel_widget.dart';
import 'package:hodan_hospital/features/home/<USER>/widgets/home_header_widget.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_image_picker_card.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_list_grid_view.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/image_place_holder.dart';
import 'package:hodan_hospital/features/user/presentation/bloc/user_bloc.dart';
import 'package:hodan_hospital/gen/assets.gen.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  final _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData({bool forceFetch = false}) async {
    if (!mounted) return;

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final doctorBloc = context.doctorBloc;
      final userBloc = context.userBloc;
      if (!mounted) return;

      try {
        await Future.wait<void>([
          Future(
              () => userBloc.add(GetCurrentUserEvent(forceFetch: forceFetch))),
          if (doctorBloc.doctors.isEmpty || forceFetch)
            Future(() => doctorBloc.add(GetDoctors(forceFetch: forceFetch))),
          // Future(() => doctorBloc.add(GetBanners(forceFetch: forceFetch))),
        ]);
      } catch (e, s) {
        AppLogger().error(
          'Failed to load data in home screen : $e',
          error: e,
          stackTrace: s,
        );
        // if (mounted) {
        //   ScaffoldMessenger.of(context).showSnackBar(
        //     SnackBar(content: Text('Failed to load data: ${e.toString()}')),
        //   );
        // }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          //!!!!!!!!!!!  non scrollable widget -------------------------------------
          HomeHeaderWidget(
            onSearch: () {
              // context.pushNamedRoute(RoutePaths.mainDoctors);
              context.pushRoute(const DoctorsPage());
            },
            onDrawerTap: () {
              _scaffoldKey.currentState?.openEndDrawer();
            },
          ),
          Expanded(
            child: RefreshIndicator.adaptive(
              onRefresh: () async {
                // context.userBloc
                //     .add(const GetCurrentUserEvent(forceFetch: true));
                // context.doctorBloc.add(const GetDoctors(forceFetch: true));
                // context.doctorBloc.add(const GetBanners(forceFetch: true));
                await _loadData(forceFetch: true);
              },
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                child: Column(
                  children: [
                    SizedBox(height: 10.h),
                    //!1 banners section
                    BlocBuilder<DoctorBloc, DoctorState>(
                      buildWhen: (previous, currentState) =>
                          currentState is BannersLoading ||
                          currentState is BannersLoaded ||
                          currentState is BannerFailure,
                      builder: (context, state) {
                        return const BannerCarouselWidget();
                      },
                    ),
                    SizedBox(height: 30.h),

                    // SectionHeaderWithAction(
                    //   title: "Doctors",
                    //   onTap: () {
                    //     //
                    //     // Navigator.push(
                    //     //   context,
                    //     //   MaterialPageRoute(
                    //     //     builder: (context) => const DoctorsPage(
                    //     //       topPadding: 50,
                    //     //     ),
                    //     //   ),
                    //     // );

                    //     // context.pushNamedRoute(RoutePaths.mainDoctors);
                    //     context.pushRoute(const DoctorsPage());
                    //   },
                    // ),

                    _buildHomeGridWidget(),
                    SizedBox(height: 20.h),
                    // const HomeServiceBanners(),

                    //! some spacke on bottom
                    SizedBox(height: 70.h),
                  ],
                ),
              ),
            ),
          ),
          //!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
        ],
      ),
    );
  }

  Widget _buildHomeGridWidget() {
    final data = HomeGridViewModel.visibleItems;

    return CustomListGridView<HomeGridViewModel>(
      items: data,
      isLoading: false,
      isEmpty: data.isEmpty,
      showFooter: false,
      contentType: LoadingType.gridView,
      gridCrossAxisCount: 2,
      childAspectRatio: 1.4,
      physics: const NeverScrollableScrollPhysics(),
      itemBuilder: (context, data) {
        return _buildGridCard(
          context: context,
          data: data,
        );
      },
      onRefresh: () {
        context.doctorBloc.add(const GetDoctors(forceFetch: true));
      },
    );
  }

  //
  Widget _buildGridCard({
    required BuildContext context,
    required HomeGridViewModel data,
  }) {
    final textThem = context.textTheme;
    final appColors = context.appColors;
    return GestureDetector(
      onTap: () {
        data.onTap?.call(context);
      },
      child: Container(
        padding: const EdgeInsets.all(10),
        decoration: BoxDecoration(
          color: appColors.cardColor,
          borderRadius: BorderRadius.circular(10.r),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Icon(data.icon),
              CustomImagePickerCard(
                // imageUrl: data.imageUrl,
                imagePath: data.imageUrl,
                radius: 35,
                backgroundColor: appColors.transparentColor,
              ),
              SizedBox(height: 5.h),
              Text(
                data.title,
                style: textThem.bodyMedium,
                overflow: TextOverflow.ellipsis,
                maxLines: 3,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class HomeServiceBanners extends StatefulWidget {
  const HomeServiceBanners({super.key});

  @override
  State<HomeServiceBanners> createState() => _HomeServiceBannersState();
}

class _HomeServiceBannersState extends State<HomeServiceBanners> {
  final CarouselSliderController _carouselController =
      CarouselSliderController();
  int _currentIndex = 0; // Tracks active banner

  @override
  Widget build(BuildContext context) {
    final data = ServiceBannerModel.services;
    final size = 120.h;
    return BlocBuilder<DoctorBloc, DoctorState>(
        buildWhen: (previous, currentState) =>
            currentState is BannersLoading ||
            currentState is BannersLoaded ||
            currentState is BannerFailure,
        builder: (context, state) {
          final serviceBanner = context.doctorBloc.serviceBanners;
          print('serviceBanner length is : ${serviceBanner.length}');
          if (state is BannersLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          } else if (state is BannersLoaded) {
            return SizedBox(
              height: size,
              width: double.infinity,
              child: Stack(
                alignment: Alignment.bottomCenter,
                children: [
                  CarouselSlider.builder(
                    carouselController: _carouselController,
                    itemCount: serviceBanner.length,
                    options: CarouselOptions(
                      height: size,
                      viewportFraction: 0.85,
                      autoPlay: true,
                      autoPlayInterval: const Duration(seconds: 10),
                      autoPlayAnimationDuration:
                          const Duration(milliseconds: 500),
                      enlargeCenterPage: true,
                      onPageChanged: (index, reason) {
                        setState(() => _currentIndex = index);
                      },
                    ),
                    itemBuilder: (context, index, realIndex) {
                      return _buildServiceCard(
                        context: context,
                        data: serviceBanner[index],
                      );
                    },
                  ),
                  Positioned(
                    bottom: 10.h,
                    child: AnimatedSmoothIndicator(
                      activeIndex: _currentIndex,
                      count: data.length,
                      effect:
                          ExpandingDotsEffect(dotHeight: 8.h, dotWidth: 8.h),
                      onDotClicked: (index) =>
                          _carouselController.animateToPage(index),
                    ),
                  ),
                ],
              ),
            );
          } else {
            // return const Center(
            //   child: Text('Failed to load service banners'),
            // );
            return const SizedBox.shrink();
          }
        });
  }

  Widget _buildServiceCard({
    required BuildContext context,
    required BannerEntity data,
  }) {
    return GestureDetector(
      onTap: () {
        // print("Tapped on ${data.title}");

        // data.onTap(context);
        // switch (data.title) {
        //   case "IPD Services":
        //     context.pushRoute(
        //       ServicesPage(
        //         services: ServicesModel.ipdServices,
        //         title: data.title,
        //       ),
        //     );
        //     break;
        //   case "OPD Services":
        //     context.pushRoute(
        //       ServicesPage(
        //         services: ServicesModel.opdServices,
        //         title: data.title,
        //       ),
        //     );
        //     break;
        //   case "Medical Services":
        //     context.pushRoute(
        //       ServicesPage(
        //         services: ServicesModel.medicalServices,
        //         title: data.title,
        //       ),
        //     );
        //     break;
        //   default:
        //     break;
        // }
      },
      child: ClipRRect(
          borderRadius: BorderRadius.circular(0.r),
          child:
              // isNetwork ?
              CachedNetworkImage(
            imageUrl: data.imageUrl,
            fit: BoxFit.fill,
            width: double.infinity,
            height: double.infinity,
            placeholder: (_, __) => const Center(child: ImagePlaceholder()),
            errorWidget: (_, __, ___) =>
                Image.asset(Assets.images.png.banner1.path, fit: BoxFit.cover),
          )
          // : Image.asset(
          //     banner as String,
          //     fit: BoxFit.cover,
          //     width: double.infinity,
          //     height: double.infinity,
          //     errorBuilder: (_, __, ___) => Image.asset(
          //       Assets.images.png.banner1.path,
          //       fit: BoxFit.cover,
          //     ),
          //   ),
          ),
    );
  }
}
