import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hodan_hospital/core/config/router/extension/navigation_extension.dart';
import 'package:hodan_hospital/core/config/theme/colors/app_colors_extension.dart';
import 'package:hodan_hospital/core/enums/loading_type.dart';
import 'package:hodan_hospital/core/utils/extensions/build_context_extensions.dart';
import 'package:hodan_hospital/features/home/<USER>/model/services_model.dart';
import 'package:hodan_hospital/features/home/<USER>/pages/service_detail_page.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/animations/animated_app_bar.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_container.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_list_grid_view.dart';

// class ServicesPage extends StatefulWidget {
//   const ServicesPage({super.key});

//   @override
//   State<ServicesPage> createState() => _ServicesPageState();
// }

// class _ServicesPageState extends State<ServicesPage> {
//   @override
//   Widget build(BuildContext context) {
//     final appColors = context.appColors;
//     final textTheme = context.textTheme;
//     return DefaultTabController(
//       length: 3,
//       initialIndex: 0,
//       child: Scaffold(
//         appBar: AnimatedAppBar(
//           title: "Services",
//           appBarHeight: 100,
//           bottom: PreferredSize(
//             preferredSize: const Size.fromHeight(40),
//             child: TabBar(
//               labelStyle: textTheme.titleLarge?.copyWith(
//                 color: appColors.whiteColor,
//                 fontSize: 14.sp,
//               ),
//               unselectedLabelStyle: textTheme.titleLarge?.copyWith(
//                 color: appColors.whiteColor.withValues(alpha: 0.5),
//                 fontSize: 13.sp,
//               ),
//               tabs: [
//                 Tab(text: "OPD Services"),
//                 Tab(text: "IPD Services"),
//                 Tab(text: "Medical Services"),
//               ],
//             ),
//           ),
//         ),
//         body: TabBarView(
//           children: [
//             _buildServicesList(ServicesModel.opdServices),
//             _buildServicesList(ServicesModel.ipdServices),
//             _buildServicesList(ServicesModel.otherServices),
//           ],
//         ),
//       ),
//     );
//   }

//   Widget _buildServicesList(List<ServicesModel> services) {
//     return CustomListGridView<ServicesModel>(
//       items: services,
//       isLoading: false,
//       isEmpty: services.isEmpty,
//       contentType: LoadingType.listView,
//       itemBuilder: (_, item) {
//         return ServicesCardWidget(
//           servicesModel: item,
//           onTap: () {
//             context.pushRoute(
//               ServiceDetailPage(service: item),
//             );
//           },
//         );
//       },
//     );
//   }
// }

class ServicesPage extends StatelessWidget {
  final List<ServicesModel> services;
  final String title;
  const ServicesPage(
      {super.key, required this.services, this.title = 'Services'});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AnimatedAppBar(
        title: title,
        // appBarHeight: 100,
      ),
      body: _buildServicesList(
        context: context,
        services: services,
      ),
    );
  }

  Widget _buildServicesList({
    required BuildContext context,
    required List<ServicesModel> services,
  }) {
    return CustomListGridView<ServicesModel>(
      items: services,
      isLoading: false,
      isEmpty: services.isEmpty,
      contentType: LoadingType.listView,
      itemBuilder: (_, item) {
        return ServicesCardWidget(
          servicesModel: item,
          onTap: () {
            context.pushRoute(
              ServiceDetailPage(service: item),
            );
          },
        );
      },
    );
  }
}

class ServicesCardWidget extends StatelessWidget {
  final ServicesModel servicesModel;
  final Function()? onTap;
  const ServicesCardWidget({
    super.key,
    required this.servicesModel,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final appColors = context.appColors;
    final textTheme = context.textTheme;
    return GestureDetector(
      onTap: onTap,
      child: CustomContainer(
        margin: EdgeInsets.symmetric(horizontal: 5.w, vertical: 5.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(16.r),
                topRight: Radius.circular(16.r),
              ),
              child: Hero(
                tag: servicesModel.name,
                // transitionOnUserGestures: true,
                child: Image.asset(
                  servicesModel.imagePath,
                  // Assets.images.png.banner3.path,
                  height: 120.h,
                  width: double.infinity,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) => Container(
                    height: 120.h,
                    width: double.infinity,
                    alignment: Alignment.center,
                    color: appColors.secondaryColor.withValues(alpha: 0.2),
                    child: const Icon(Icons.broken_image, size: 50),
                  ),
                ),
              ),
            ),
            SizedBox(height: 10.h),

            /// Text Content (with padding)
            Padding(
              // padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 10.h),
              padding: EdgeInsets.only(
                top: 10.h,
                left: 12.w,
                right: 2.w,
                bottom: 10.h,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    servicesModel.name,
                    style: textTheme.titleLarge?.copyWith(
                      color: appColors.primaryColor,
                      fontSize: 16.sp,
                    ),
                  ),
                  SizedBox(height: 5.h),
                  Text(
                    servicesModel.description,
                    style: textTheme.bodyMedium?.copyWith(
                      // color: appColors.blackColor.withValues(alpha: 0.5),
                      fontSize: 14.sp,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
