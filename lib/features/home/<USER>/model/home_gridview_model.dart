import 'package:flutter/material.dart';
// import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:hodan_hospital/core/config/router/extension/navigation_extension.dart';
import 'package:hodan_hospital/features/Order/presentation/pages/orders_page.dart';
import 'package:hodan_hospital/features/Result/presentation/pages/lab_result_page.dart';
import 'package:hodan_hospital/features/doctor/presentation/pages/doctors_page.dart';
import 'package:hodan_hospital/features/home/<USER>/pages/packages_page.dart';
import 'package:hodan_hospital/gen/assets.gen.dart';

class HomeGridViewModel {
  // final IconData icon;
  final String title;
  final bool isVisible;
  final String imageUrl;
  final Function(BuildContext context)? onTap;

  HomeGridViewModel({
    // required this.icon,
    required this.title,
    required this.imageUrl,
    this.isVisible = true,
    required this.onTap,
  });

  static List<HomeGridViewModel> data = [
    HomeGridViewModel(
      // icon: FontAwesomeIcons.book,
      title: 'Book an Appointment',
      // imageUrl: 'https://cdn-icons-png.flaticon.com/128/9411/9411743.png',
      imageUrl: Assets.images.png.appointment.path,
      onTap: (context) {
        context.pushRoute(const DoctorsPage());
      },
    ),
    HomeGridViewModel(
      // icon: FontAwesomeIcons.box,
      // imageUrl: 'https://cdn-icons-png.flaticon.com/128/17209/17209039.png',
      imageUrl: Assets.images.png.package.path,
      title: 'Packages',
      onTap: (context) {
        context.pushRoute(const PackagesPage());
      },
    ),
    // HomeGridViewModel(
    //   icon: FontAwesomeIcons.servicestack,
    //   title: "Services",
    //   imageUrl: "https://cdn-icons-png.flaticon.com/128/11515/11515286.png",
    //   onTap: (context) {
    //     context.pushRoute(ServicesPage());
    //   },
    // ),
    HomeGridViewModel(
      // icon: FontAwesomeIcons.servicestack,
      title: 'Orders',
      // imageUrl: 'https://cdn-icons-png.flaticon.com/128/5643/5643764.png',
      imageUrl: Assets.images.png.order.path,
      onTap: (context) {
        context.pushRoute(const OrdersPage());
      },
    ),
    HomeGridViewModel(
      // icon: FontAwesomeIcons.addressBook,
      // title: "Support",
      title: 'Lab Result',
      // imageUrl: "https://cdn-icons-png.flaticon.com/128/8898/8898827.png",
      // imageUrl: 'https://cdn-icons-png.flaticon.com/128/7299/7299515.png',
      imageUrl: Assets.images.png.labResult.path,
      onTap: (context) {
        context.pushRoute(const LabResultsPage());
      },
    ),
  ];

  static List<HomeGridViewModel> get visibleItems {
    return data.where((item) => item.isVisible).toList();
  }
}
