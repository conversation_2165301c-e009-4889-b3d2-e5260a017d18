import 'package:flutter/material.dart';
import 'package:hodan_hospital/gen/assets.gen.dart';

class PackagesGridViewModel {
  final String imagePath;
  final String packageDetailImagePath;
  final String title;

  final Function(BuildContext context)? onTap;
  final bool isVisible;

  PackagesGridViewModel({
    required this.imagePath,
    required this.packageDetailImagePath,
    required this.title,
    this.isVisible = true,
    required this.onTap,
  });

  static List<PackagesGridViewModel> data = [
    PackagesGridViewModel(
      imagePath: Assets.images.png.antenatalPackage.path,
      packageDetailImagePath: Assets.images.png.antentalPackageDetail.path,
      title: "Antenatal Package",
      onTap: (context) {
        // context.pushRoute(DoctorsPage());
      },
    ),
    PackagesGridViewModel(
      imagePath: Assets.images.png.sugarPackage.path,
      packageDetailImagePath: Assets.images.png.sugarPackageDetail.path,
      title: "Sugar Package",
      onTap: (context) {
        // context.pushRoute(DoctorsPage());
      },
    ),
    PackagesGridViewModel(
      imagePath: Assets.images.png.cardicPackage.path,
      packageDetailImagePath: Assets.images.png.cardicPackageDetail.path,
      title: "Cardic Package",
      onTap: (context) {
        // context.pushRoute(DoctorsPage());
      },
    ),
    PackagesGridViewModel(
      imagePath: Assets.images.png.hyperthensisPatientsPackage.path,
      packageDetailImagePath: Assets.images.png.hyperthensisPatientsDetail.path,
      title: "Hyperthensis Patients Package",
      onTap: (context) {
        // context.pushRoute(DoctorsPage());
      },
    ),
    PackagesGridViewModel(
      imagePath: Assets.images.png.healthCheckUpMale.path,
      packageDetailImagePath: Assets.images.png.healthCheckUpMaleDetail.path,
      title: "Executive Health Check Up Male",
      onTap: (context) {
        // context.pushRoute(DoctorsPage());
      },
    ),
    PackagesGridViewModel(
      imagePath: Assets.images.png.healthCheckUpFemale.path,
      packageDetailImagePath: Assets.images.png.healthCheckUpFemaleDetail.path,
      title: "Executive Health Check Up Female",
      onTap: (context) {
        // context.pushRoute(DoctorsPage());
      },
    ),
  ];

  static List<PackagesGridViewModel> get visibleItems {
    return data.where((item) => item.isVisible).toList();
  }
}
