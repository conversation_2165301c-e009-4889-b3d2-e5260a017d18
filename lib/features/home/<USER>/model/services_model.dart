import 'package:hodan_hospital/gen/assets.gen.dart';

class ServicesModel {
  final String name;
  final String imagePath;
  final String description;

  final String? detailDescription;
  final List<String>? features;
  final String? iconPath;
  final String? department;
  final String? contactInfo;
  final String? location;
  final String? serviceCode;

  const ServicesModel({
    required this.name,
    required this.imagePath,
    required this.description,
    this.detailDescription,
    this.features,
    this.iconPath,
    this.department,
    this.contactInfo,
    this.location,
    this.serviceCode,
  });

  static List<ServicesModel> opdServices = [
    const ServicesModel(
      name: 'Pediatrics',
      imagePath: 'assets/images/services/pediatrics.png',
      description: 'Healthcare for infants, children, and adolescents.',
      detailDescription:
          'Specialized care for children including vaccinations, growth tracking, and illness management.',
      features: ['Routine check-ups', 'Immunizations', 'Nutrition guidance'],
      department: 'Pediatrics',
      location: 'Block A - 1st Floor',
      contactInfo: '+252 61 0000001',
      serviceCode: 'OPD-001',
    ),
    const ServicesModel(
      name: 'Gynecology and Obstetrics',
      imagePath: 'assets/images/services/gynecology.png',
      description:
          "Women's health, pregnancy, childbirth, and reproductive system care.",
      detailDescription:
          'Comprehensive services for pregnancy, menstrual disorders, and reproductive health.',
      features: ['Prenatal care', 'Family planning', 'Gynecologic surgery'],
      department: 'Gynecology',
      location: 'Block B - 2nd Floor',
      contactInfo: '+252 61 0000002',
      serviceCode: 'OPD-002',
    ),
    const ServicesModel(
      name: 'Gastroenterology',
      imagePath: 'assets/images/services/gastroenterology.png',
      description: 'Diagnosis and treatment of digestive system disorders.',
      detailDescription:
          'Covers all digestive organs with diagnostics and treatment procedures.',
      features: ['Endoscopy', 'Liver tests', 'IBS management'],
      department: 'Gastroenterology',
      location: 'Block C - Ground Floor',
      contactInfo: '+252 61 0000003',
      serviceCode: 'OPD-003',
    ),
    ServicesModel(
      name: 'Orthopedics',
      // imagePath: "assets/images/services/orthopedics.png",
      imagePath: Assets.images.jpeg.orhtopedicsServices.path,
      description: 'Care for bones, joints, ligaments, tendons, and muscles.',
      detailDescription:
          'Treatment for fractures, joint pain, arthritis, and sports injuries.',
      features: [
        'X-ray & MRI support',
        'Fracture management',
        'Rehabilitation'
      ],
      department: 'Orthopedics',
      location: 'Block D - 1st Floor',
      contactInfo: '+252 61 0000004',
      serviceCode: 'OPD-004',
    ),
    const ServicesModel(
      name: 'Neurology & Neurosurgery',
      imagePath: 'assets/images/services/neurology.png',
      description: 'Treatment of brain, spine, and nervous system disorders.',
      detailDescription:
          'Expert care for strokes, epilepsy, spinal issues, and neuro surgeries.',
      features: ['EEG', 'MRI Brain', 'Brain tumor evaluation'],
      department: 'Neurology',
      location: 'Block E - 2nd Floor',
      contactInfo: '+252 61 0000005',
      serviceCode: 'OPD-005',
    ),
    ServicesModel(
      name: 'Dentistry Clinic',
      // imagePath: "assets/images/services/dentistry.png",
      imagePath: Assets.images.jpeg.dentistServices.path,
      description: 'Oral health, dental care, and treatment of teeth and gums.',
      detailDescription:
          'From dental cleaning to surgical procedures, we’ve got you covered.',
      features: ['Tooth extraction', 'Braces', 'Root canal treatment'],
      department: 'Dentistry',
      location: 'Dental Wing - Ground Floor',
      contactInfo: '+252 61 0000006',
      serviceCode: 'OPD-006',
    ),
    const ServicesModel(
      name: 'Ophthalmology & Ophthalmic Surgery',
      imagePath: 'assets/images/services/ophthalmology.png',
      description:
          'Eye care services including vision correction and surgeries.',
      detailDescription:
          'Specialized in diagnosing and treating eye conditions with surgical options.',
      features: ['Eye testing', 'Cataract surgery', 'Laser correction'],
      department: 'Ophthalmology',
      location: 'Block F - 1st Floor',
      contactInfo: '+252 61 0000007',
      serviceCode: 'OPD-007',
    ),
    const ServicesModel(
      name: 'Ear, Nose & Throat (ENT)',
      imagePath: 'assets/images/services/ent.png',
      description: 'Treatment of ear, nose, and throat disorders.',
      detailDescription:
          'We offer diagnostics, surgeries, and therapies for ENT-related problems.',
      features: ['Sinus surgery', 'Ear tube placement', 'Hearing loss tests'],
      department: 'ENT',
      location: 'Block G - Ground Floor',
      contactInfo: '+252 61 0000008',
      serviceCode: 'OPD-008',
    ),
    const ServicesModel(
      name: 'Cardiology',
      imagePath: 'assets/images/services/cardiology.png',
      description: 'Diagnosis and care for heart and blood vessel conditions.',
      detailDescription:
          'We provide advanced care for hypertension, arrhythmia, and heart failure.',
      features: ['ECG', 'Echocardiogram', 'Cardiac consultation'],
      department: 'Cardiology',
      location: 'Block H - 2nd Floor',
      contactInfo: '+252 61 0000009',
      serviceCode: 'OPD-009',
    ),
    const ServicesModel(
      name: 'Internal Medicine',
      imagePath: 'assets/images/services/internal_medicine.png',
      description: 'Comprehensive adult care focusing on internal organs.',
      detailDescription:
          'From diabetes to respiratory conditions, we manage chronic diseases.',
      features: [
        'Diabetes care',
        'Hypertension management',
        'General consultations'
      ],
      department: 'Internal Medicine',
      location: 'Block I - 1st Floor',
      contactInfo: '+252 61 0000010',
      serviceCode: 'OPD-010',
    ),
    const ServicesModel(
      name: 'Urology',
      imagePath: 'assets/images/services/urology.png',
      description: 'Care of urinary tract and male reproductive organs.',
      detailDescription:
          'Services include kidney stones, urinary incontinence, and prostate issues.',
      features: ['Ultrasound', 'Stone removal', 'Prostate screening'],
      department: 'Urology',
      location: 'Block J - 1st Floor',
      contactInfo: '+252 61 0000011',
      serviceCode: 'OPD-011',
    ),
    const ServicesModel(
      name: 'General Surgery',
      imagePath: 'assets/images/services/general_surgery.png',
      description:
          'Surgical treatments for various conditions across the body.',
      detailDescription:
          'Minor and major procedures with pre and post-op care.',
      features: ['Appendectomy', 'Gallbladder surgery', 'Wound care'],
      department: 'Surgery',
      location: 'Surgical Suite - Ground Floor',
      contactInfo: '+252 61 0000012',
      serviceCode: 'OPD-012',
    ),
    const ServicesModel(
      name: 'Nutrition',
      imagePath: 'assets/images/services/nutrition.png',
      description:
          'Dietary guidance and nutritional support for health and recovery.',
      detailDescription:
          'Customized diet plans for weight management and chronic illness care.',
      features: ['Diabetic diet', 'Weight loss programs', 'Prenatal nutrition'],
      department: 'Nutrition',
      location: 'Block K - 2nd Floor',
      contactInfo: '+252 61 0000013',
      serviceCode: 'OPD-013',
    ),
    const ServicesModel(
      name: 'Physiotherapy',
      imagePath: 'assets/images/services/physiotherapy.png',
      description:
          'Rehabilitation and therapy to restore movement and function.',
      detailDescription:
          'We help patients recover from injury, surgery, or chronic pain.',
      features: ['Post-op rehab', 'Pain management', 'Stroke therapy'],
      department: 'Physiotherapy',
      location: 'Rehab Center - Ground Floor',
      contactInfo: '+252 61 0000014',
      serviceCode: 'OPD-014',
    ),
  ];

  static List<ServicesModel> ipdServices = [
    const ServicesModel(
      name: 'ICU and NICU',
      imagePath: 'assets/images/services/icu_nicu.png',
      description: 'Intensive care for critically ill adults and newborns.',
      detailDescription:
          '24/7 care units equipped with modern life support for critical conditions.',
      features: ['Ventilator support', 'Monitored beds', 'Neonatal ICU'],
      department: 'Critical Care',
      location: 'Main Building - Ground Floor',
      contactInfo: '+252 61 0000100',
      serviceCode: 'IPD-001',
    ),
    ServicesModel(
      name: 'Maternity Care',
      // imagePath: "assets/images/services/maternity.png",
      imagePath: Assets.images.jpeg.maternityServices.path,
      description: 'Support for pregnancy, labor, and postnatal care.',
      detailDescription:
          'Personalized care for mother and child before, during, and after birth.',
      features: ['Labor room', 'Postnatal education', 'Family-friendly policy'],
      department: 'Maternity Ward',
      location: 'Block C - 1st Floor',
      contactInfo: '+252 61 0000101',
      serviceCode: 'IPD-002',
    ),
    const ServicesModel(
      name: 'Operation & Anesthesia Unit',
      imagePath: 'assets/images/services/surgery_unit.png',
      description: 'Surgical operations under safe anesthesia management.',
      detailDescription:
          'Fully equipped OT complex with general and specialized surgeries.',
      features: [
        'Minor & major OT',
        'Anesthesia team',
        'Post-op recovery room'
      ],
      department: 'Surgical Unit',
      location: 'Surgical Block - 2nd Floor',
      contactInfo: '+252 61 0000102',
      serviceCode: 'IPD-003',
    ),
    const ServicesModel(
      name: 'VIP Rooms',
      imagePath: 'assets/images/services/vip_room.png',
      description:
          'Exclusive inpatient rooms with premium comfort and privacy.',
      detailDescription:
          'Spacious rooms with enhanced facilities and private nurse support.',
      features: ['TV, Sofa, WiFi', 'Private bathroom', 'Deluxe meal service'],
      department: 'Inpatient',
      location: 'VIP Wing - 3rd Floor',
      contactInfo: '+252 61 0000103',
      serviceCode: 'IPD-004',
    ),
  ];

  static List<ServicesModel> medicalServices = [
    ServicesModel(
      name: 'Laboratory',
      // imagePath: "assets/images/services/laboratory.png",
      imagePath: Assets.images.jpeg.labServices.path,
      description:
          'Diagnostic services for blood, urine, and other sample analysis.',
      detailDescription:
          'Automated lab testing for accurate and timely diagnostics.',
      features: ['Hematology', 'Biochemistry', 'Microbiology'],
      department: 'Diagnostics',
      location: 'Lab Wing - Ground Floor',
      contactInfo: '+252 61 0000200',
      serviceCode: 'MED-001',
    ),
    ServicesModel(
      name: 'Medical Imaging',
      // imagePath: "assets/images/services/imaging.png",
      imagePath: Assets.images.jpeg.imagingServices.path,
      description: 'X-ray, MRI, CT, and ultrasound imaging for diagnosis.',
      detailDescription:
          'Our radiology unit supports diagnosis with advanced imaging.',
      features: ['X-ray', 'Ultrasound', 'CT & MRI scans'],
      department: 'Radiology',
      location: 'Block L - Imaging Dept',
      contactInfo: '+252 61 0000201',
      serviceCode: 'MED-002',
    ),
    ServicesModel(
      name: 'Pharmacy & Optical',
      // imagePath: "assets/images/services/pharmacy_optical.png",
      imagePath: Assets.images.jpeg.pharmacyServices.path,
      description: 'Medication dispensing and vision correction solutions.',
      detailDescription:
          'Full-service pharmacy and optical care for patients and visitors.',
      features: ['Prescription drugs', 'Eyeglass fitting', 'Eye drops & meds'],
      department: 'Pharmacy',
      location: 'Entrance Lobby',
      contactInfo: '+252 61 0000202',
      serviceCode: 'MED-003',
    ),
    const ServicesModel(
      name: '24 Hours Emergency',
      imagePath: 'assets/images/services/emergency.png',
      description: 'Immediate and life-saving care available at any time.',
      detailDescription:
          'Emergency unit with ambulance and trauma readiness around the clock.',
      features: ['Ambulance', 'Triage unit', 'Emergency surgery'],
      department: 'Emergency',
      location: 'ER Block - Ground Floor',
      contactInfo: '+252 61 0000999',
      serviceCode: 'MED-004',
    ),
  ];

  // getter for unique hero tag
  String get heroTag {
    final date = DateTime.now().millisecondsSinceEpoch;
    return 'service_${serviceCode ?? name}_$date';
  }
}
