import 'package:flutter/material.dart';
import 'package:hodan_hospital/core/config/router/extension/navigation_extension.dart';
import 'package:hodan_hospital/features/home/<USER>/model/services_model.dart';
import 'package:hodan_hospital/features/home/<USER>/pages/services_page.dart';

class ServiceBannerModel {
  final String imageUrl;
  final Function(BuildContext context) onTap;

  ServiceBannerModel({
    required this.imageUrl,
    required this.onTap,
  });

  static List<ServiceBannerModel> services = [
    ServiceBannerModel(
      imageUrl:
          'https://img.freepik.com/free-psd/house-moving-service-banner-template_23-**********.jpg?ga=GA1.1.590697846.**********&semt=ais_hybrid&w=740',
      onTap: (context) {
        // Navigate to OPD Services page
        context.pushRoute(ServicesPage(
          services: ServicesModel.opdServices,
          title: "OPD Services",
        ));
      },
    ),
    ServiceBannerModel(
      imageUrl:
          'https://img.freepik.com/free-vector/medical-consultation-banner-template_23-**********.jpg?ga=GA1.1.590697846.**********&semt=ais_hybrid&w=740',
      onTap: (context) {
        // Navigate to OPD Services page
        context.pushRoute(ServicesPage(
          services: ServicesModel.ipdServices,
          title: "IPD Services",
        ));
      },
    ),
    // Medical services
    ServiceBannerModel(
      imageUrl:
          'https://img.freepik.com/free-vector/medical-banner-template_23-**********.jpg?ga=GA1.1.590697846.**********&semt=ais_hybrid&w=740',
      onTap: (context) {
        // Navigate to OPD Services page
        context.pushRoute(ServicesPage(
          services: ServicesModel.medicalServices,
          title: "Medical Services",
        ));
      },
    ),
  ];
}
