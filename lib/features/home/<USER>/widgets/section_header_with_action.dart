import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hodan_hospital/core/config/theme/colors/app_colors_extension.dart';
import 'package:hodan_hospital/core/utils/extensions/build_context_extensions.dart';

class SectionHeaderWithAction extends StatelessWidget {
  const SectionHeaderWithAction({
    super.key,
    required this.title,
    required this.onTap,
  });

  final String title;
  final Function()? onTap;

  @override
  Widget build(BuildContext context) {
    final textThme = context.textTheme;
    // final appColors = context.appColors;
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 10.w),
      child: Row(
        children: [
          Text(
            title,
            style: textThme.titleLarge?.copyWith(
              fontSize: 18.sp,
            ),
          ),
          const Spacer(),
          GestureDetector(
            onTap: onTap,
            child: Row(
              children: [
                Text(
                  "See All",
                  style: textThme.titleSmall?.copyWith(
                    color: context.appColors.primaryColor,
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: context.appColors.primaryColor,
                  size: 15.w,
                ),
              ],
            ),
          )
        ],
      ),
    );
  }
}
