import 'package:cached_network_image/cached_network_image.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hodan_hospital/core/utils/extensions/app_bloc_extensions.dart';
import 'package:hodan_hospital/features/doctor/domain/entities/banner_entity.dart';
import 'package:hodan_hospital/gen/assets.gen.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/image_place_holder.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';

class BannerCarouselWidget extends StatefulWidget {
  const BannerCarouselWidget({super.key});

  @override
  // ignore: library_private_types_in_public_api
  _BannerCarouselWidgetState createState() => _BannerCarouselWidgetState();
}

class _BannerCarouselWidgetState extends State<BannerCarouselWidget> {
  final CarouselSliderController _carouselController =
      CarouselSliderController();
  int _currentIndex = 0; // Tracks active banner

  @override
  Widget build(BuildContext context) {
    final List<BannerEntity> networkBanners = context.doctorBloc.banners;
    final List<String> fallbackBanners = [
      Assets.images.png.banner1.path,
      Assets.images.png.banner2.path,
      Assets.images.png.banner3.path,
    ];

    final bool useNetworkBanners = networkBanners.isNotEmpty;
    final List<dynamic> banners =
        useNetworkBanners ? networkBanners : fallbackBanners;

    final size = 180.h;

    return SizedBox(
      height: size,
      width: double.infinity,
      child: Stack(
        alignment: Alignment.bottomCenter,
        children: [
          CarouselSlider.builder(
            carouselController: _carouselController,
            itemCount: banners.length,
            options: CarouselOptions(
              height: size,
              viewportFraction: 0.85,
              autoPlay: true,
              autoPlayInterval: const Duration(seconds: 3),
              autoPlayAnimationDuration: const Duration(milliseconds: 800),
              enlargeCenterPage: true,
              onPageChanged: (index, reason) {
                setState(() => _currentIndex = index);
              },
            ),
            itemBuilder: (context, index, realIndex) {
              return _buildBannerItem(banners[index], useNetworkBanners);
            },
          ),
          Positioned(
            bottom: 10.h,
            child: AnimatedSmoothIndicator(
              activeIndex: _currentIndex,
              count: banners.length,
              effect: ExpandingDotsEffect(dotHeight: 8.h, dotWidth: 8.h),
              onDotClicked: (index) => _carouselController.animateToPage(index),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBannerItem(dynamic banner, bool isNetwork) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(16.r),
      child: isNetwork
          ? CachedNetworkImage(
              imageUrl: (banner as BannerEntity).imageUrl,
              fit: BoxFit.cover,
              width: double.infinity,
              height: double.infinity,
              placeholder: (_, __) => const Center(child: ImagePlaceholder()),
              errorWidget: (_, __, ___) => Image.asset(
                  Assets.images.png.banner1.path,
                  fit: BoxFit.cover),
            )
          : Image.asset(
              banner as String,
              fit: BoxFit.cover,
              width: double.infinity,
              height: double.infinity,
              errorBuilder: (_, __, ___) => Image.asset(
                Assets.images.png.banner1.path,
                fit: BoxFit.cover,
              ),
            ),
    );
  }
}
