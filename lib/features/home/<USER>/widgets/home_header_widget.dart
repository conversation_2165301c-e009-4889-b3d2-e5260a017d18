import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hodan_hospital/core/config/theme/colors/app_colors_extension.dart';
import 'package:hodan_hospital/core/enums/image_type.dart';
import 'package:hodan_hospital/core/utils/extensions/app_bloc_extensions.dart';
import 'package:hodan_hospital/core/utils/extensions/build_context_extensions.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_image_picker_card.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_textfield.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/flexible_page_layout_widget.dart';
import 'package:hodan_hospital/features/user/presentation/bloc/user_bloc.dart';

class HomeHeaderWidget extends StatelessWidget {
  const HomeHeaderWidget({
    super.key,
    this.onSearch,
    this.onDrawerTap,
  });

  final Function()? onSearch;
  final Function()? onDrawerTap;

  @override
  Widget build(BuildContext context) {
    final textThme = context.textTheme;
    return FlexibleLayoutWidget(
      totalHeight: 200,
      topChildHeight: 160,
      topChildOverlayFactor: 0.2,
      topChildWidget: _buildTopChildWidget(context),
      bottomChildWidget: CustomTextField(
        contentPadding: EdgeInsets.symmetric(vertical: 16.h),
        isReadOnly: true,
        // autofocus: true,
        hintText: "Search a Doctor",
        hintStyle: textThme.titleMedium?.copyWith(
          color: context.appColors.textColor.withValues(alpha: 0.7),
          fontSize: 16.sp,
        ),
        fillColor: context.appColors.cardColor,
        enabledBorderColor: context.appColors.transparentColor,
        focusedBorderColor: context.appColors.transparentColor,
        prefixIcon: Icon(
          Icons.search,
          color: context.appColors.textColor.withValues(alpha: 0.7),
          size: 25.w,
        ),
        onTap: onSearch,
      ),
    );
  }

  Widget _buildTopChildWidget(BuildContext context) {
    final textTheme = context.textTheme;
    final appColors = context.appColors;
    return BlocBuilder<UserBloc, UserState>(
      buildWhen: (previous, current) =>
          current is UserDataLoading ||
          current is UserDataLoaded ||
          current is UserFailure,
      builder: (context, state) {
        final user = context.userBloc.currentUser;
        // final isLoading = state is UserDataLoading;
        return Padding(
          padding: EdgeInsets.only(bottom: 10.h, left: 20.w, right: 20.w),
          child: Row(
            children: [
              /// Leading widget
              CustomImagePickerCard(
                imageUrl: user?.profileImage,
                imageType: ImageType.profile,
                radius: 20.r,
              ),
              SizedBox(width: 12.w),

              /// title and subtitle widget
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  spacing: 3,
                  children: [
                    Text(
                      "Hi, Welcome Back,",
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.start,
                      style: textTheme.bodyLarge?.copyWith(
                        color: appColors.whiteColor.withValues(alpha: 0.9),
                      ),
                    ),
                    Text(
                      user?.fullName ?? "UserName",
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.start,
                      style: textTheme.titleLarge?.copyWith(
                        color: appColors.whiteColor,
                        fontSize: 18.sp,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(width: 10.w),

              /// trailing widget
              /// https://cdn-icons-png.flaticon.com/128/10550/10550106.png
              // IconButton(
              //   onPressed: onDrawerTap,
              //   // padding: EdgeInsets.zero,
              //   icon: Icon(
              //     // Icons.dashboard,
              //     FontAwesomeIcons.bars,
              //     size: 25.w,
              //     color: appColors.whiteColor.withValues(alpha: 0.9),
              //   ),
              // ),
            ],
          ),
        );
      },
    );
  }
}
