// ignore_for_file: unreachable_switch_default

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hodan_hospital/core/config/theme/colors/app_colors_extension.dart';
import 'package:hodan_hospital/core/enums/shape_type.dart';

class CustomFloatingActionButton extends StatelessWidget {
  const CustomFloatingActionButton({
    super.key,
    required this.onPressed,
    this.shapeType = ShapeType.circular,
    this.borderRadius = 16.0,
    this.icon = const Icon(Icons.add),
    this.borderColor,
    this.backgroundColor,
    this.foregroundColor,
    this.tooltip,
  });

  final void Function()? onPressed;
  final ShapeType shapeType;
  final double borderRadius;
  final Icon icon;
  final Color? borderColor;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final String? tooltip;

  @override
  Widget build(BuildContext context) {
    Color effectiveForeground =
        foregroundColor ?? context.appColors.primaryColor;
    return FloatingActionButton(
      foregroundColor: effectiveForeground,
      backgroundColor: backgroundColor ?? getBackgroundColor(context),
      shape: _getShape(context),
      onPressed: onPressed,
      tooltip: tooltip,
      child: icon,
    );
  }

  Color getBackgroundColor(BuildContext context) {
    return shapeType == ShapeType.circular
        ? Theme.of(context).primaryColor
        : Theme.of(context).scaffoldBackgroundColor;
  }

  // Method to return shape based on ShapeType
  ShapeBorder _getShape(BuildContext context) {
    final effectiveBorderColor =
        borderColor ?? context.appColors.transparentColor;

    switch (shapeType) {
      case ShapeType.roundedRectangle:
        return RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadius.r),
          side: BorderSide(color: effectiveBorderColor),
        );
      case ShapeType.rectangle:
        return RoundedRectangleBorder(
          borderRadius: BorderRadius.zero,
          side: BorderSide(color: effectiveBorderColor),
        );
      case ShapeType.circular:
      default:
        return CircleBorder(
          side: BorderSide(color: effectiveBorderColor),
        );
    }
  }
}
