// ignore_for_file: unreachable_switch_default

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hodan_hospital/core/config/theme/colors/app_colors_extension.dart';
import 'package:hodan_hospital/core/enums/button_state.dart';
import 'package:hodan_hospital/core/enums/button_style_type.dart';

class CustomButton extends StatelessWidget {
  const CustomButton({
    super.key,
    required this.onTap,
    this.buttonText,
    this.loadingText,
    this.buttonState = ButtonState.normal,
    this.textStyle,
    this.loadingIndicator,
    this.buttonStyleType = ButtonStyleType.filled,
    this.customBorderSide,
    this.borderRadius = 16,
    this.leadingIcon,
    this.backgroundColor,
    this.rippleColor,
    this.semanticLabel,
    this.width = 200,
    this.height = 40,
    this.padding,
    this.isBorderActive = false,
  });

  // Named Constructor for Filled Button
  const CustomButton.filled({
    super.key,
    required this.onTap,
    this.buttonText,
    this.loadingText,
    this.buttonState = ButtonState.normal,
    this.textStyle,
    this.loadingIndicator,
    this.customBorderSide,
    this.borderRadius = 16,
    this.leadingIcon,
    this.backgroundColor,
    this.rippleColor,
    this.semanticLabel,
    this.width = 200,
    this.height = 30,
    this.padding,
    this.isBorderActive = false,
  }) : buttonStyleType = ButtonStyleType.filled;

// Named Constructor for Outline Button
  const CustomButton.outline({
    super.key,
    required this.onTap,
    this.buttonText,
    this.loadingText,
    this.buttonState = ButtonState.normal,
    this.textStyle,
    this.loadingIndicator,
    this.customBorderSide,
    this.borderRadius = 16,
    this.leadingIcon,
    this.backgroundColor,
    this.rippleColor,
    this.semanticLabel,
    this.width = 200,
    this.height = 30,
    this.padding,
    this.isBorderActive = false,
  }) : buttonStyleType = ButtonStyleType.outline;

// Named Constructor for Inline Button
  const CustomButton.inline({
    super.key,
    required this.onTap,
    this.buttonText,
    this.loadingText,
    this.buttonState = ButtonState.normal,
    this.textStyle,
    this.loadingIndicator,
    this.customBorderSide,
    this.borderRadius = 16,
    this.leadingIcon,
    this.backgroundColor,
    this.rippleColor,
    this.semanticLabel,
    this.width = 200,
    this.height = 30,
    this.padding,
    this.isBorderActive = false,
  }) : buttonStyleType = ButtonStyleType.inline;

// Named Constructor for Disabled Button
  const CustomButton.disabled({
    super.key,
    this.onTap,
    this.buttonText,
    this.loadingText,
    this.buttonState = ButtonState.normal,
    this.textStyle,
    this.loadingIndicator,
    this.customBorderSide,
    this.borderRadius = 16,
    this.leadingIcon,
    this.backgroundColor,
    this.rippleColor,
    this.semanticLabel,
    this.width = 200,
    this.height = 30,
    this.padding,
    this.isBorderActive = false,
  }) : buttonStyleType = ButtonStyleType.disabled;

  final Function()? onTap;
  final String? buttonText;
  final String? loadingText;
  final ButtonState buttonState;
  final TextStyle? textStyle;
  final Widget? loadingIndicator;
  final ButtonStyleType buttonStyleType;
  final BorderSide? customBorderSide;
  final double borderRadius;
  final Widget? leadingIcon;
  final Color? backgroundColor;
  final Color? rippleColor;
  final String? semanticLabel;
  final double? width;
  final double height;
  final EdgeInsetsGeometry? padding;
  final bool isBorderActive;

  @override
  Widget build(BuildContext context) {
    return Semantics(
      label: semanticLabel ?? buttonText,
      button: true,
      enabled: buttonState != ButtonState.disabled,
      child: Opacity(
        opacity: buttonState == ButtonState.disabled ? 0.5 : 1,
        child: Align(
          child: ElevatedButton(
            onPressed: _getOnPressed(),
            style: _getButtonStyle(context),
            child: _getButtonContent(context),
          ),
        ),
      ),
    );
  }

  // Determine the onPressed behavior based on button state
  Function()? _getOnPressed() {
    switch (buttonState) {
      case ButtonState.normal:
        return onTap;
      case ButtonState.loading:
      case ButtonState.disabled:
        return null;
      default:
        return null;
    }
  }

  // Get button content based on the state (either loading indicator or button text)
  Widget _getButtonContent(BuildContext context) {
    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 300),
      child: buttonState == ButtonState.loading
          ? loadingIndicator ?? _defaultLoadingIndicator(context)
          : Row(
              mainAxisSize: MainAxisSize.min, // ✅ Prevent unnecessary space
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // if (leadingIcon != null) ...[
                //   leadingIcon!,
                //   SizedBox(width: 8.w), // Space between icon and text
                // ],
                // if (buttonText != null)
                //   width != null
                //       ? SizedBox(
                //           width: width!.w, // ✅ Fixed width if provided
                //           child: _buildText(context),
                //         )
                //       : Flexible(
                //           // ✅ Auto-resize if no fixed width is provided
                //           child: _buildText(context),
                //         ),

                if (leadingIcon != null) ...[
                  leadingIcon!,
                ],
                if (leadingIcon != null && buttonText != null)
                  SizedBox(width: 8.w),
                if (buttonText != null)
                  Flexible(
                    // Ensure text adapts to the available space
                    child: Text(
                      buttonText!,
                      key: ValueKey(buttonText),
                      style: textStyle ?? _getTextStyle(context),
                      maxLines: 1, // Prevent text from wrapping
                      overflow: TextOverflow.ellipsis, // Handle text overflow
                      textAlign: TextAlign.center, // Center-align the text
                    ),
                  ),
              ],
            ),
    );
  }

// 🔹 Extracted Method for Text Styling
  // Widget _buildText(BuildContext context) {
  //   return Text(
  //     buttonText ?? '',
  //     key: ValueKey(buttonText),
  //     style: textStyle ?? _getTextStyle(context),
  //     maxLines: 1, // ✅ Ensure the text stays in a single line
  //     overflow: TextOverflow.ellipsis, // ✅ Truncate if too long
  //     textAlign: TextAlign.center, // ✅ Keep text centered
  //   );
  // }

  // Get button style based on the style type and state
  ButtonStyle _getButtonStyle(BuildContext context) {
    switch (buttonStyleType) {
      case ButtonStyleType.outline:
        return _getOutlineButtonStyle(context);
      case ButtonStyleType.inline:
        return _getInlineButtonStyle(context);
      case ButtonStyleType.disabled:
        return _getDisabledButtonStyle(context);
      case ButtonStyleType.filled:
      default:
        return _getFilledButtonStyle(context);
    }
  }

  // Filled button style (default)
  ButtonStyle _getFilledButtonStyle(BuildContext context) {
    return ElevatedButton.styleFrom(
      elevation: _getButtonElevation(),
      splashFactory: InkSplash.splashFactory,
      shadowColor: context.appColors.transparentColor,
      backgroundColor: _getButtonBackgroundColor(context),
      disabledBackgroundColor: _getButtonBackgroundColor(context),
      side: customBorderSide ??
          BorderSide(
            color: context.appColors.transparentColor,
            width: 2,
          ),
      foregroundColor:
          context.appColors.whiteColor, // Ensuring text color is always white
      padding: padding ?? EdgeInsets.symmetric(vertical: 5.h, horizontal: 10.w),
      minimumSize: Size(width ?? 40, height.h), // Control size
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadius.r),
      ),
    );
  }

  // Outline button style (with border)
  ButtonStyle _getOutlineButtonStyle(BuildContext context) {
    return ElevatedButton.styleFrom(
      // elevation: _getButtonElevation(),
      elevation: 0,
      splashFactory: InkSplash.splashFactory,
      shadowColor: context.appColors.transparentColor,
      // overlayColor: context.appColors.transparentColor,
      // surfaceTintColor: context.appColors.transparentColor,

      disabledBackgroundColor: context.appColors.transparentColor,
      side: customBorderSide ??
          BorderSide(
            color: _getBorderColor(context),
            width: 2,
          ),
      backgroundColor: context.appColors.transparentColor,
      foregroundColor:
          context.appColors.whiteColor, // Ensuring text color is always white

      padding: padding ?? EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
      minimumSize: Size(40, height.h), // Control size
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadius.r),
      ),
    );
  }

  // DisAbled button style (with border)
  ButtonStyle _getDisabledButtonStyle(BuildContext context) {
    return ElevatedButton.styleFrom(
      elevation: _getButtonElevation(),
      side: customBorderSide ??
          BorderSide(
            color: context.appColors.transparentColor,
            width: 2,
          ),
      backgroundColor: context.appColors.disabledColor,
      disabledBackgroundColor: context.appColors.disabledColor,
      splashFactory: InkSplash.splashFactory,
      shadowColor: context.appColors.transparentColor,

      // overlayColor: context.appColors.transparentColor,
      // surfaceTintColor: context.appColors.transparentColor,
      foregroundColor:
          context.appColors.whiteColor, // Ensuring text color is always white
      padding: padding ?? EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
      minimumSize: Size(40, height.h), // Control size
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadius.r),
      ),
    );
  }

  // Inline button style (no background and no border)
  ButtonStyle _getInlineButtonStyle(BuildContext context) {
    return ElevatedButton.styleFrom(
      elevation: _getButtonElevation(),
      backgroundColor: context.appColors.transparentColor,
      splashFactory: InkSplash.splashFactory,
      shadowColor: context.appColors.transparentColor,
      // overlayColor: context.appColors.transparentColor,
      // surfaceTintColor: context.appColors.transparentColor,
      foregroundColor:
          context.appColors.whiteColor, // Ensuring text color is always white
      padding: padding ?? EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
      side: customBorderSide ?? BorderSide.none,
      minimumSize: Size(40, height.h), // Control size
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadius.r),
      ),
    );
  }

  // Button elevation logic based on the state
  double _getButtonElevation() {
    return (buttonState == ButtonState.normal ||
            buttonState == ButtonState.error)
        ? 4
        : 2;
  }

  // Get button background color based on the state
  Color _getButtonBackgroundColor(BuildContext context) {
    if (backgroundColor != null) {
      return backgroundColor!;
    }
    switch (buttonState) {
      case ButtonState.normal:
        return context.appColors.buttonColor;
      case ButtonState.loading:
        return context.appColors.loadingColor;
      case ButtonState.disabled:
        return context.appColors.disabledColor;
      case ButtonState.error:
        return context.appColors.errorColor;
      default:
        return context.appColors.buttonColor;
    }
  }

  // Default loading indicator widget
  Widget _defaultLoadingIndicator(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(width: 8.w),
        SizedBox(
          width: 24.w,
          height: 24.h,
          child: CircularProgressIndicator(
            color: context.appColors.whiteColor,
            strokeWidth: 2.w,
          ),
        ),
        SizedBox(width: 10.w),
        // Text(
        //   loadingText ?? 'Processing...',
        //   maxLines: 1,
        //   overflow: TextOverflow.ellipsis,
        //   style: _getTextStyle(
        //     context,
        //   ),
        // ),
      ],
    );
  }

  // Define the text style for the button
  TextStyle _getTextStyle(BuildContext context) {
    return TextStyle(
      fontSize: 16.sp,
      color: context.appColors.whiteColor, // Ensure text color is always white
    );
  }

  // Helper method to get the border color based on the isBorderActive flag
  Color _getBorderColor(BuildContext context) {
    return isBorderActive
        ? context.appColors.primaryColor
        : context.appColors.disabledColor;
  }
}
