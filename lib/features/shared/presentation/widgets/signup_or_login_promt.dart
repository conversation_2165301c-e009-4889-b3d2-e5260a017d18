import 'package:flutter/material.dart';
import 'package:hodan_hospital/core/config/theme/colors/app_colors_extension.dart';
import 'package:hodan_hospital/core/utils/extensions/build_context_extensions.dart';

class SignUpOrLoginPromt extends StatelessWidget {
  const SignUpOrLoginPromt({
    super.key,
    required this.promt,
    required this.promtType,
    required this.onTap,
  });

  final String promtType;
  final String promt;
  final Function()? onTap;

  @override
  Widget build(BuildContext context) {
    final textTheme = context.textTheme;
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          "$promtType  ",
          // style: CustomTextStyles.orangeSmalTextStyle,
          style: textTheme.bodyMedium?.copyWith(
            color: context.appColors.textColor,
          ),
        ),
        InkWell(
            onTap: onTap,
            child: Text(
              promt,
              style: textTheme.bodyLarge?.copyWith(
                color: context.appColors.primaryColor,
              ),
            )),
      ],
    );
  }
}
