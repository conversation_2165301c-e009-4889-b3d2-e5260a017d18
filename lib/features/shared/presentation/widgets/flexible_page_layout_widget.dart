import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hodan_hospital/core/config/theme/colors/app_colors_extension.dart';
import 'package:hodan_hospital/core/enums/animation_direction.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/animations/animated_item_wrapper.dart';

class FlexibleLayoutWidget extends StatelessWidget {
  final Widget? topChildWidget;
  final Widget? bottomChildWidget;
  final Color? backgroundColor;
  final double? totalHeight;
  final double? topChildHeight;
  final double
      topChildOverlayFactor; // Controls overlay percentage (e.g. 0.2 = 20%)
  final double bottomChildHorizontalPadding;
  final double borderRadius;

  const FlexibleLayoutWidget({
    super.key,
    this.backgroundColor,
    this.topChildWidget,
    this.totalHeight,
    this.topChildHeight,
    this.topChildOverlayFactor = 0.2, // Default 20% overlay
    this.bottomChildHorizontalPadding = 20.0,
    this.borderRadius = 40.0,
    this.bottomChildWidget,
  });

  @override
  Widget build(BuildContext context) {
    final appColors = context.appColors;
    final size = totalHeight ?? 400.h;
    final topHeight = topChildHeight ?? size * 0.5;
    final overlayHeight = (topHeight * topChildOverlayFactor)
        .clamp(10.h, topHeight * 0.8); // Prevents excessive overlay

    return SizedBox(
      // color: Colors.lightBlueAccent,
      height: size,
      child: Stack(
        children: [
          // Background Container with Rounded Bottom
          AnimatedItemWrapper(
            delay: const Duration(milliseconds: 100),
            animationDirection: AnimationDirection.topToBottom,
            child: Container(
              height: topHeight,
              width: double.infinity,
              padding: EdgeInsets.only(top: 20.h),
              decoration: BoxDecoration(
                color: backgroundColor ?? appColors.primaryColor,
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(borderRadius.r),
                  bottomRight: Radius.circular(borderRadius.r),
                ),
              ),
              child: topChildWidget != null
                  ? Align(
                      alignment: Alignment.topCenter,
                      child: SizedBox(
                        height: topHeight, // Ensures it doesn't overflow
                        child: topChildWidget,
                      ),
                    )
                  : null,
            ),
          ),

          // Bottom Child Positioned with Dynamic Overlay
          if (bottomChildWidget != null)
            Positioned(
              top: topHeight - overlayHeight, // Adjusts overlay dynamically
              left: bottomChildHorizontalPadding.w,
              right: bottomChildHorizontalPadding.w,
              child: AnimatedItemWrapper(
                delay: const Duration(milliseconds: 200),
                animationDirection: AnimationDirection.bottomToTop,
                child: bottomChildWidget!,
              ),
            ),
        ],
      ),
    );
  }
}

/*


class FlexibleLayoutWidget extends StatelessWidget {
  final Widget? topChildWidget;
  final Widget? bottomChildWidget;
  final Color? backgroundColor;
  final double? totalHeight;
  final double? topChildHeight;
  final double
      topChildOverlayFactor; // Controls overlay percentage (e.g. 0.2 = 20%)
  final double bottomChildHorizontalPadding;
  final double borderRadius;

  const FlexibleLayoutWidget({
    super.key,
    this.backgroundColor,
    this.topChildWidget,
    this.totalHeight,
    this.topChildHeight,
    this.topChildOverlayFactor = 0.2, // Default 20% overlay
    this.bottomChildHorizontalPadding = 20.0,
    this.borderRadius = 40.0,
    this.bottomChildWidget,
  });

  @override
  Widget build(BuildContext context) {
    final appColors = context.appColors;
    final size = totalHeight ?? 400.h;
    final topHeight = topChildHeight ?? size * 0.5; // More adaptive height
    final overlayHeight =
        topHeight * topChildOverlayFactor; // Calculates the overlay height

    return SizedBox(
      height: size,
      child: Stack(
        children: [
          // Background Container with Rounded Bottom
          Container(
            height: topHeight,
            width: double.infinity,
            decoration: BoxDecoration(
              color: backgroundColor ?? appColors.primaryColor,
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(borderRadius.r),
                bottomRight: Radius.circular(borderRadius.r),
              ),
            ),
            child: topChildWidget != null
                ? Align(
                    alignment: Alignment.topCenter,
                    child: topChildWidget,
                  )
                : null,
          ),

          // Bottom Child Positioned with Dynamic Overlay
          if (bottomChildWidget != null)
            Positioned(
              top: topHeight - overlayHeight, // Adjusts overlay dynamically
              left: bottomChildHorizontalPadding.w,
              right: bottomChildHorizontalPadding.w,
              child: bottomChildWidget!,
            ),
        ],
      ),
    );
  }
}



*/
