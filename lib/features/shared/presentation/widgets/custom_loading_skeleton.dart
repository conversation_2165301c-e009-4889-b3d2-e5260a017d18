// ignore_for_file: unreachable_switch_default

import 'package:flutter/material.dart';
import 'package:hodan_hospital/core/config/theme/colors/app_colors_extension.dart';
import 'package:hodan_hospital/core/enums/loading_type.dart';
import 'package:shimmer/shimmer.dart';

import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'custom_container.dart';

class CustomLoadingSkeleton extends StatelessWidget {
  final List<Widget> loadingItems; // A list of loading items to display

  const CustomLoadingSkeleton({
    super.key,
    this.loadingItems = const [],
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      physics: const AlwaysScrollableScrollPhysics(),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          ...loadingItems, // Add the list of loading items dynamically
        ],
      ),
    );
  }
}

//
class LoadingSkeletonItem extends StatelessWidget {
  final double height;
  final double? width;
  final int itemCount;
  final LoadingType loadingType;
  final ScrollPhysics? physics;
  final Axis scrollDirection;

  const LoadingSkeletonItem({
    super.key,
    this.height = 150,
    this.width,
    required this.loadingType,
    this.itemCount = 9,
    this.physics,
    this.scrollDirection = Axis.vertical,
  });

  @override
  Widget build(BuildContext context) {
    switch (loadingType) {
      case LoadingType.gridView:
        return SizedBox(
          height: ((itemCount > 0 ? (itemCount / 3).ceil() : 1) * height.h),
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 10.w),
            child: GridView.builder(
              scrollDirection: scrollDirection,
              shrinkWrap: true,
              padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 15.h),
              physics: physics ?? const NeverScrollableScrollPhysics(),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                crossAxisSpacing: 15.w,
                mainAxisSpacing: 15.h,
                childAspectRatio: 0.9,
              ),
              itemCount: itemCount,
              itemBuilder: (context, index) {
                return _buildShimmerContainer(context);
              },
            ),
          ),
        );
      case LoadingType.listView:
        return SizedBox(
          height: height.h * itemCount,
          child: ListView.separated(
            scrollDirection: scrollDirection,
            shrinkWrap: true,
            physics: physics ?? const NeverScrollableScrollPhysics(),
            padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 15.h),
            itemCount: itemCount,
            separatorBuilder: (context, index) {
              if (scrollDirection == Axis.vertical) {
                return SizedBox(height: 15.h);
              }

              return SizedBox(width: 10.h);
            },
            itemBuilder: (context, index) {
              return _buildShimmerContainer(context);
            },
          ),
        );
      case LoadingType.widget:
      default:
        // Build a regular list view
        return Padding(
          padding: EdgeInsets.symmetric(horizontal: 10.w),
          child: _buildShimmerContainer(context),
        );
    }
  }

  Widget _buildShimmerContainer(BuildContext context) {
    return Shimmer.fromColors(
      period: const Duration(milliseconds: 1000),
      baseColor: context.appColors.shimmerBaseColor,
      highlightColor: context.appColors.shimmerHighlightColor,
      child: CustomContainer(
        padding: const EdgeInsets.all(0),
        margin: const EdgeInsets.all(2),
        height: height,
        width: width,
      ),
    );
  }
}
