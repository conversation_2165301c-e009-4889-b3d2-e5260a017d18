import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hodan_hospital/core/config/theme/colors/app_colors_extension.dart';
import 'package:hodan_hospital/core/enums/image_type.dart';
import 'package:hodan_hospital/features/doctor/domain/entities/doctor_entity.dart';
import 'package:hodan_hospital/features/doctor/presentation/pages/doctor_details_page.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_auto_size_text.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_button.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_container.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_image_picker_card.dart';

class FeatureDoctorWidget extends StatelessWidget {
  const FeatureDoctorWidget({
    super.key,
    required this.onTap,
    required this.doctor,
  });

  final DoctorEntity doctor;
  final Function()? onTap;

  @override
  Widget build(BuildContext context) {
    final appColors = context.appColors;
    final textTheme = Theme.of(context).textTheme;
    return GestureDetector(
      onTap: onTap,
      child: CustomContainer(
        padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
        child: Column(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomImagePickerCard(
                  imageUrl: doctor.image,
                  radius: 30,
                  imageType: ImageType.doctor,
                ),
                SizedBox(width: 10.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CustomAutoSizeText(
                        text: doctor.formattedDoctorName,
                        style: textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 5.h),
                      CustomAutoSizeText(
                        text: doctor.department,
                        style: textTheme.bodyLarge,
                        color: appColors.primaryColor,
                      ),
                      SizedBox(height: 2.h),
                      if (doctor.formattedExperience.isNotEmpty)
                        CustomAutoSizeText(
                          text: doctor.formattedExperience,
                          style: textTheme.bodySmall,
                        ),
                    ],
                  ),
                ),
              ],
            ),
            //!
            SizedBox(height: 10.h),
            Row(
              // crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "Available",
                      // style: CustomTextStyles.orangeSmalTextStyle,
                      style: textTheme.bodyMedium?.copyWith(
                        color: appColors.primaryColor,
                      ),
                    ),
                    Text(
                      doctor.formattedAvailableTime,
                      style: textTheme.bodyMedium,
                    ),
                  ],
                ),
                const Spacer(),
                CustomButton(
                  width: 100,
                  buttonText: "Book Now",
                  onTap: () {
                    // context.router
                    //     .push(DoctorDetailsRoute(doctor: doctor));

                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => DoctorDetailsPage(
                          doctor: doctor,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
