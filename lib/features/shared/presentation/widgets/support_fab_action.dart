import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:hodan_hospital/core/config/theme/colors/app_colors_extension.dart';
import 'package:hodan_hospital/core/enums/support_options.dart';
import 'package:popover/popover.dart';
import 'package:url_launcher/url_launcher.dart';

import '../pages/patient_feedback_page.dart';
import 'custom_image_picker_card.dart';

class SupportFABAction extends StatelessWidget {
  const SupportFABAction({super.key});

  @override
  Widget build(BuildContext context) {
    return _buildSupportFAB(context);
  }

  Widget _buildSupportFAB(BuildContext context) {
    final appColors = context.appColors;
    return Builder(
      builder: (context) => GestureDetector(
        onTap: () {
          showPopover(
            context: context,
            bodyBuilder: (context) => _buildPopoverContent(context),
            direction: PopoverDirection.top,
            width: MediaQuery.of(context).size.width * 0.9,
            height: 250,
            arrowHeight: 15,
            arrowWidth: 30,
            barrierColor: Colors.black.withValues(alpha: 0.3),
            backgroundColor: appColors.whiteColor,
          );
        },
        child: Container(
          width: 64.w,
          height: 64.h,
          decoration: BoxDecoration(
            color: appColors.primaryColor,
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: appColors.blackColor.withValues(alpha: 0.2),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          // child: CustomImagePickerCard(
          //   radius: 32,
          //   imageUrl: 'https://cdn-icons-png.flaticon.com/128/8898/8898827.png',
          //   isLoading: true,
          //   showIcon: false,
          // ),
          child: Icon(
            FontAwesomeIcons.headset,
            size: 28.sp,
            color: appColors.whiteColor,
          ),
        ),
      ),
    );
  }

  Widget _buildPopoverContent(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(12.r),
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: SupportOption.values.map((option) {
            final color = _getOptionColor(option);
            return ListTile(
              leading: Container(
                padding: EdgeInsets.all(6.r),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.2),
                  shape: BoxShape.circle,
                ),
                child: Icon(option.icon, color: color, size: 24.sp),
              ),
              title: Text(option.title,
                  style:
                      TextStyle(fontSize: 14.sp, fontWeight: FontWeight.w500)),
              subtitle: Text(option.description,
                  style:
                      TextStyle(fontSize: 12.sp, color: Colors.grey.shade600)),
              onTap: () {
                Navigator.pop(context);
                _handleSupportOption(context, option);
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  Color _getOptionColor(SupportOption option) {
    switch (option) {
      case SupportOption.call:
        return Colors.green;
      case SupportOption.whatsapp:
        return Colors.green;
      // case SupportOption.email:
      // return Colors.blue;
      case SupportOption.feedback:
        return Colors.orange;
    }
  }

  void _handleSupportOption(BuildContext context, SupportOption option) {
    if (option == SupportOption.feedback) {
      // Navigate to feedback screen (example placeholder)
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => const PatientFeedbackPage()),
      );
      return;
    }

    if (option.url.isNotEmpty) {
      _launchUrl(option.url, context);
    }
  }

  Future<void> _launchUrl(String url, BuildContext context) async {
    print('Launching URL: $url');
    try {
      // if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url)).onError((error, stackTrace) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error launching URL: $error')),
        );
        return false;
      }).then((s) {
        print('URL launched successfully: $s');
        return true;
      }, onError: (error) {
        print('Error launching URL: $error');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error launching URL: $error')),
        );
        return false;
      });
      // }
      //  else {
      //   ScaffoldMessenger.of(context).showSnackBar(
      //     SnackBar(content: Text('Could not launch ${url.split(':').first}')),
      //   );
      // }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: $e')),
      );
    }
  }
}
