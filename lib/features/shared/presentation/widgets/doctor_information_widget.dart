import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hodan_hospital/core/config/theme/colors/app_colors_extension.dart';
import 'package:hodan_hospital/core/enums/animation_direction.dart';
import 'package:hodan_hospital/core/enums/image_type.dart';
import 'package:hodan_hospital/core/utils/extensions/build_context_extensions.dart';
import 'package:hodan_hospital/features/doctor/domain/entities/doctor_entity.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/animations/animated_item_wrapper.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_auto_size_text.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_container.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_image_picker_card.dart';

class DoctorInformationWidget extends StatelessWidget {
  const DoctorInformationWidget({
    super.key,
    this.widget,
    this.delay,
  });

  final DoctorEntity? widget;
  final Duration? delay;
  @override
  Widget build(BuildContext context) {
    final appColors = context.appColors;
    final textTheme = context.textTheme;
    return AnimatedItemWrapper(
      delay: delay ?? const Duration(milliseconds: 500),
      animationDirection: AnimationDirection.topToBottom,
      child: CustomContainer(
        margin: EdgeInsets.symmetric(horizontal: 20.w),
        padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 10.w),
        child: Column(
          // crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            //! large row
            Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                CustomImagePickerCard(
                  imageUrl: widget?.image,
                  imageType: ImageType.doctor,
                  radius: 35,
                ),
                SizedBox(width: 15.w),
                Expanded(
                    child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    //! name
                    Row(
                      spacing: 5.w,
                      children: [
                        Flexible(
                          child: CustomAutoSizeText(
                            text: widget?.formattedDoctorName ?? '',
                            style: textTheme.bodyLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 5.h),
                    //! department
                    CustomAutoSizeText(
                      text: widget?.department ?? '',
                      style: textTheme.bodyLarge?.copyWith(
                        color: appColors.subtextColor,
                      ),
                    ),
                    SizedBox(height: 10.h),
                    //! rating and number money
                    Row(
                      children: [
                        // Row(
                        //   children: [
                        //     Icon(
                        //       Icons.star,
                        //       color: appColors.primaryColor,
                        //     ),
                        //     Icon(
                        //       Icons.star,
                        //       color: appColors.primaryColor,
                        //     ),
                        //     Icon(
                        //       Icons.star,
                        //       color: appColors.primaryColor,
                        //     ),
                        //     Icon(
                        //       Icons.star,
                        //       color: appColors.primaryColor,
                        //     ),
                        //     Icon(
                        //       Icons.star_border,
                        //       color: appColors.primaryColor,
                        //     ),
                        //   ],
                        // ),
                        // const Spacer(),
                        Row(
                          children: [
                            Icon(
                              Icons.attach_money,
                              color: appColors.primaryColor,
                            ),
                            Text(
                              "${widget?.consultingCharge}",
                              style: textTheme.titleLarge?.copyWith(
                                fontSize: 17.sp,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                )),
              ],
            ),

            SizedBox(height: 20.h),
          ],
        ),
      ),
    );
  }
}
