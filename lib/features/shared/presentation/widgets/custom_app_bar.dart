import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hodan_hospital/core/enums/animation_direction.dart';
import 'package:hodan_hospital/core/utils/extensions/build_context_extensions.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/animations/animated_item_wrapper.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_container.dart';

class CustomAppBar extends StatelessWidget {
  const CustomAppBar({
    super.key,
    required this.title,
    this.trailing,
    this.topPadding = 40,
  });

  final String title;
  final Widget? trailing;
  final double topPadding;

  @override
  Widget build(BuildContext context) {
    final textTheme = context.textTheme;
    final showBackButton = Navigator.canPop(context);
    return Padding(
      padding: EdgeInsets.only(top: topPadding.h),
      child: ListTile(
        leading: showBackButton
            ? AnimatedItemWrapper(
                delay: const Duration(milliseconds: 100),
                animationDirection: AnimationDirection.leftToRight,
                child: InkWell(
                  onTap: () {
                    // pop
                    Navigator.of(context).pop();
                  },
                  child: CustomContainer(
                    height: 40,
                    width: 40,
                    child: Padding(
                        padding: EdgeInsets.only(left: 10.w),
                        child: Icon(
                          Icons.arrow_back_ios,
                          size: 30.w,
                        )),
                  ),
                ),
              )
            : null,
        title: AnimatedItemWrapper(
          delay: const Duration(milliseconds: 100),
          animationDirection: AnimationDirection.topToBottom,
          child: Text(
            title,
            // style: CustomTextStyles.primaryLargeBoldTextStyle,
            style: textTheme.titleMedium,
          ),
        ),
        trailing: AnimatedItemWrapper(
          delay: const Duration(milliseconds: 100),
          animationDirection: AnimationDirection.rightToLeft,
          child: trailing,
        ),
      ),
    );
  }
}
