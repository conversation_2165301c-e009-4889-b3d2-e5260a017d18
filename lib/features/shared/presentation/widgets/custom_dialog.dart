// ignore_for_file: unreachable_switch_default
import 'package:confetti/confetti.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hodan_hospital/core/config/theme/colors/app_colors_extension.dart';
import 'package:hodan_hospital/core/enums/dialog_type.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_button.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_container.dart';
import 'package:hodan_hospital/gen/assets.gen.dart';
import 'package:lottie/lottie.dart';

class CustomDialog extends StatefulWidget {
  final String title;
  final String message;
  final String confirmBtnText;
  final String cancelBtnText;
  final DialogType dialogType;
  final VoidCallback? onConfirm;
  final VoidCallback? onCancel;
  final double? confirmButtonwidth;
  final double? cancelButtonwidth;
  final double confirmButtonHeight;
  final double cancelButtonHeight;

  const CustomDialog({
    super.key,
    required this.title,
    required this.message,
    required this.confirmBtnText,
    required this.cancelBtnText,
    required this.dialogType,
    required this.onConfirm,
    required this.onCancel,
    required this.confirmButtonwidth,
    required this.cancelButtonwidth,
    this.cancelButtonHeight = 30,
    this.confirmButtonHeight = 30,
  });

  @override
  State<CustomDialog> createState() => _CustomDialogState();
}

class _CustomDialogState extends State<CustomDialog> {
  late ConfettiController _confettiController;

  @override
  void initState() {
    super.initState();
    _confettiController =
        ConfettiController(duration: const Duration(seconds: 5));

    if (widget.dialogType == DialogType.success) {
      _confettiController.play();
    }
  }

  @override
  void dispose() {
    _stopConfettiAnimation();
    _confettiController.dispose();
    super.dispose();
  }

  void _stopConfettiAnimation() {
    if (_confettiController.state == ConfettiControllerState.playing) {
      _confettiController.stop(); // Stop animation
    }
  }

  bool showCancelButton() =>
      widget.dialogType == DialogType.confirmation ||
      widget.dialogType == DialogType.warning ||
      widget.dialogType == DialogType.info;

  bool showConfirmButton() => widget.dialogType != DialogType.loading;

  String getAssetPath() {
    switch (widget.dialogType) {
      case DialogType.confirmation:
        return Assets.images.json.confirm;
      case DialogType.warning:
        return Assets.images.json.warning;
      case DialogType.info:
        return Assets.images.json.info;
      case DialogType.error:
        return Assets.images.json.error;
      case DialogType.success:
        return Assets.images.json.success;
      case DialogType.loading:
        return Assets.images.json.loading;
      default:
        return Assets.images.json.info;
    }
  }

  Color getBgColor() {
    final colors = context.appColors;
    switch (widget.dialogType) {
      case DialogType.confirmation:
        return colors.confirmColor;
      case DialogType.warning:
        return colors.warningColor;
      case DialogType.info:
        return colors.infoColor;
      case DialogType.error:
        return colors.errorColor;
      case DialogType.success:
        return colors.successColor;
      case DialogType.loading:
        return colors.loadingColor;
      default:
        return colors.infoColor;
    }
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final appColors = context.appColors;

    return Dialog(
      backgroundColor: appColors.surfaceColor,
      child: Stack(
        alignment: Alignment.topCenter,
        children: [
          SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Header Container with background color
                CustomContainer(
                  height: 150.h,
                  decoration: BoxDecoration(
                    color: getBgColor(),
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(16.r),
                      topRight: Radius.circular(16.r),
                    ),
                  ),
                  child: Lottie.asset(
                    getAssetPath(),
                    height: 100.h,
                    width: 150.w,
                    fit: BoxFit.contain,
                  ),
                ),
                SizedBox(height: 20.h),

                /// Title and Message
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 20.w),
                  child: Column(
                    children: [
                      Text(
                        widget.title,
                        style: textTheme.titleMedium,
                      ),
                      SizedBox(height: 10.h),
                      Text(
                        widget.message,
                        textAlign: TextAlign.center,
                        style: textTheme.bodyMedium
                            ?.copyWith(fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 20.h),
                // Confirm and Cancel buttons
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 20.w),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      //
                      if (showCancelButton())
                        CustomButton.disabled(
                          buttonText: widget.cancelBtnText,
                          width: widget.cancelButtonwidth,
                          height: widget.cancelButtonHeight,
                          onTap: widget.onCancel,
                        ),

                      //
                      if (showConfirmButton())
                        CustomButton(
                          buttonText: widget.confirmBtnText,
                          width: widget.confirmButtonwidth,
                          height: widget.confirmButtonHeight,
                          onTap: widget.onConfirm,
                        ),
                    ],
                  ),
                ),

                // some space on bottom
                SizedBox(height: 30.h),
              ],
            ),
          ),

          /// Confetti animation (only for success dialogs)
          if (widget.dialogType == DialogType.success)
            Positioned(
              top: -50, // Move it higher for a more dramatic effect
              child: ConfettiWidget(
                confettiController: _confettiController,
                blastDirectionality:
                    BlastDirectionality.explosive, // Random burst effect
                shouldLoop:
                    false, // Prevent looping, plays once per dialog show

                numberOfParticles: 40, // Increased for a richer effect
                emissionFrequency:
                    0.04, // Higher emission rate for a burst-like effect
                minBlastForce: 4, // Adds variation in particle speed
                maxBlastForce: 8, // Some particles shoot faster
                gravity: 0.2, // Slightly higher gravity for more natural fall
                particleDrag: 0.07, // Smooth motion
              ),
            ),
        ],
      ),
    );
  }
}
