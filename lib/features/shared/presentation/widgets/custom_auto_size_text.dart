import 'package:flutter/material.dart';

class CustomAutoSizeText extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final int maxLines;
  final Color? color;

  const CustomAutoSizeText({
    super.key,
    required this.text,
    this.style,
    this.maxLines = 1,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    return FittedBox(
      fit: BoxFit.scaleDown, // Ensures text scales down to fit
      child: Text(
        text,
        style: style?.copyWith(color: color),
        maxLines: maxLines,
        overflow: TextOverflow.ellipsis,
        softWrap: false, // Prevents unnecessary wrapping
      ),
    );
  }
}
