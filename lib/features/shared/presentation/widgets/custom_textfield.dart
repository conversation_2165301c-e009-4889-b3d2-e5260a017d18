// ignore_for_file: public_member_api_docs, sort_constructors_first, must_be_immutable
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hodan_hospital/core/config/theme/colors/app_colors_extension.dart';
import 'package:hodan_hospital/core/utils/extensions/build_context_extensions.dart';

class CustomTextField extends StatefulWidget {
  final TextEditingController? controller;
  final Widget? prefixIcon;
  final String? labelText;
  final String? hintText;
  final String? initialValue;
  final TextInputType? keyboardType;
  final int? maxLine;
  bool isObsecureText;
  final bool isPasswordField;
  final Function(String)? onChanged;
  final AutovalidateMode autovalidateMode;
  final String? Function(String?)? validator;
  final double borderRadius;
  final bool isReadOnly;
  final Function()? onTap;
  final Color? fillColor;
  final Color? focusedBorderColor;
  final Color? enabledBorderColor;
  final String? leadingIconPathSvg;
  final double? height;
  final TextInputAction? textInputAction;
  final List<String>? autofillHints;
  final String? helperText;
  final EdgeInsetsGeometry? contentPadding;
  final TextStyle? labelStyle;
  final TextStyle? hintStyle;
  final bool autofocus;
  final List<TextInputFormatter>? inputFormatters;

  CustomTextField({
    super.key,
    this.initialValue,
    this.height,
    this.isReadOnly = false,
    this.focusedBorderColor,
    this.enabledBorderColor,
    this.leadingIconPathSvg,
    this.fillColor,
    this.onTap,
    this.controller,
    this.prefixIcon,
    this.labelText,
    this.hintText,
    this.autovalidateMode = AutovalidateMode.onUserInteraction,
    this.isObsecureText = false,
    this.textInputAction,
    this.isPasswordField = false,
    this.maxLine = 1, // Default single line, set to null for multiline
    this.keyboardType = TextInputType.text, // Default to single line input
    this.borderRadius = 16,
    this.validator,
    this.onChanged,
    this.autofillHints,
    this.helperText,
    this.contentPadding,
    this.labelStyle,
    this.hintStyle,
    this.autofocus = false,
    this.inputFormatters,
  });

  @override
  State<CustomTextField> createState() => _CustomTextFieldState();
}

class _CustomTextFieldState extends State<CustomTextField> {
  @override
  Widget build(BuildContext context) {
    // bool isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final appColors = context.appColors;
    final textTheme = context.textTheme;
    return Container(
      width: MediaQuery.of(context).size.width.w,
      height: widget.height,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(widget.borderRadius.r),
      ),
      child: TextFormField(
        controller: widget.controller,
        onTap: widget.onTap,
        style: textTheme.bodyLarge,
        initialValue: widget.initialValue,
        enableSuggestions: true,
        autocorrect: true,
        autofocus: widget.autofocus,
        autofillHints: widget.autofillHints,
        keyboardType: widget.maxLine != 1
            ? TextInputType
                .multiline // Set multiline keyboard for multi-line input
            : widget.keyboardType,
        maxLines: widget.maxLine, // Allow multiline input if maxLine is null
        textInputAction: widget.textInputAction ??
            (widget.maxLine! <= 1 ? TextInputAction.next : null),
        obscureText: widget.isObsecureText,
        onChanged: widget.onChanged,
        autovalidateMode: widget.autovalidateMode,
        validator: widget.validator,
        readOnly: widget.isReadOnly,
        inputFormatters: widget.inputFormatters,
        onTapOutside: (event) {
          FocusScope.of(context).unfocus();
        },
        decoration: InputDecoration(
          contentPadding: widget.contentPadding,
          hintText: widget.hintText,
          helperText: widget.helperText,
          helperStyle: textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
          labelText: widget.labelText,
          fillColor: widget.fillColor ?? appColors.surfaceColor,
          filled: true,
          hintStyle: widget.hintStyle ?? textTheme.bodySmall,
          labelStyle: widget.labelStyle ?? textTheme.labelMedium,
          errorStyle: textTheme.titleMedium?.copyWith(
            color: appColors.errorColor,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(widget.borderRadius),
            borderSide: BorderSide(
              color: appColors.surfaceColor,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(widget.borderRadius),
            borderSide: BorderSide(
              color: appColors.surfaceColor,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(widget.borderRadius),
            borderSide: BorderSide(
              color: appColors.primaryColor,
            ),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(widget.borderRadius),
            borderSide: BorderSide(
              color: appColors.errorColor.withValues(alpha: 0.5),
            ),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(widget.borderRadius.r),
            borderSide: BorderSide(
              color: appColors.errorColor,
            ),
          ),
          prefixIcon: widget.prefixIcon != null
              ? Padding(
                  padding: EdgeInsets.all(8.0.w),
                  child: widget.prefixIcon,
                )
              : widget.leadingIconPathSvg != null
                  ? Padding(
                      padding: EdgeInsets.all(8.0.w),
                      child: SvgPicture.asset(
                        widget.leadingIconPathSvg ?? '',
                        width: 20.w,
                        height: 20.h,
                        colorFilter: ColorFilter.mode(
                          Colors.grey.shade600,
                          BlendMode.srcIn,
                        ),
                      ),
                    )
                  : null,
          suffixIcon: widget.isPasswordField
              ? IconButton(
                  onPressed: () {
                    setState(() {
                      widget.isObsecureText = !widget.isObsecureText;
                    });
                  },
                  icon: widget.isObsecureText
                      ? const Icon(
                          Icons.visibility_off,
                        )
                      : Icon(
                          Icons.visibility,
                          color: context.appColors.secondaryColor,
                        ),
                )
              : null,
        ),
      ),
    );
  }
}
