// ignore_for_file: unreachable_switch_default

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hodan_hospital/core/config/logger/app_logger.dart';
import 'package:hodan_hospital/core/config/theme/colors/app_colors_extension.dart';
import 'package:hodan_hospital/core/enums/date_format_type_enum.dart';
import 'package:hodan_hospital/core/enums/date_range_validation_type.dart';
import 'package:hodan_hospital/core/utils/extensions/app_date_extensions.dart';

class CustomDateRangePickerField extends StatefulWidget {
  final String startLabelText;
  final String endLabelText;
  final Function(String startDate, String endDate)? onDateRangeSelected;
  final DateTime? initialStartDate;
  final DateTime? initialEndDate;
  final DateTime? maxDate;
  final DateTime? minDate;
  final DateFormatType dateFormatType;
  final DateRangeValidationType dateRangeValidationType;

  const CustomDateRangePickerField({
    super.key,
    this.startLabelText = "Start Date*",
    this.endLabelText = "End Date*",
    this.onDateRangeSelected,
    this.initialStartDate,
    this.initialEndDate,
    this.maxDate,
    this.minDate,
    this.dateFormatType = DateFormatType.yearMonthDay,
    this.dateRangeValidationType =
        DateRangeValidationType.none, // New default validation
  });

  @override
  CustomDateRangePickerFieldState createState() =>
      CustomDateRangePickerFieldState();
}

class CustomDateRangePickerFieldState
    extends State<CustomDateRangePickerField> {
  DateTime? _startDate;
  DateTime? _endDate;

  @override
  void initState() {
    super.initState();
    DateTime today = DateTime.now();
    if (widget.dateRangeValidationType == DateRangeValidationType.afterToday) {
      DateTime tommorow = DateTime(today.year, today.month, today.day + 1);
      _startDate = widget.initialStartDate ?? tommorow;
      _endDate = widget.initialEndDate ?? tommorow;
    } else {
      _startDate = widget.initialStartDate ?? today;
      _endDate = widget.initialEndDate ?? today;
    }

    // Notify the place that is using this widget
    widget.onDateRangeSelected?.call(
      _startDate!.toFormattedString(formatType: widget.dateFormatType),
      _endDate!.toFormattedString(formatType: widget.dateFormatType),
    );
  }

  Future<void> _selectStartDate(BuildContext context) async {
    try {
      DateTime now = DateTime.now();
      DateTime firstDate = widget.minDate ?? DateTime(2000);
      DateTime lastDate = widget.maxDate ?? DateTime.utc(2040);

      // Adjust date range based on DateRangeValidationType
      switch (widget.dateRangeValidationType) {
        case DateRangeValidationType.fromPastMonth:
          firstDate = DateTime(now.year, now.month - 1, now.day);
          break;
        case DateRangeValidationType.fromPastWeek:
          firstDate = DateTime(now.year, now.month, now.day - 7);
          break;
        case DateRangeValidationType.afterToday:
          firstDate = DateTime(now.year, now.month, now.day + 1);
          break;
        case DateRangeValidationType.todayOrAfter:
          firstDate = DateTime(now.year, now.month, now.day);
          break;
        case DateRangeValidationType.untilNextWeek:
          lastDate = DateTime(now.year, now.month, now.day + 7);
          break;
        case DateRangeValidationType.untilNextMonth:
          lastDate = DateTime(now.year, now.month + 1, now.day);
          break;
        case DateRangeValidationType.none:
        default:
          break;
      }

      DateTime? pickedStartDate = await showDatePicker(
        context: context,
        firstDate: firstDate,
        lastDate: lastDate,
        initialDate: _startDate ?? now,
        builder: (BuildContext context, Widget? child) {
          return MediaQuery(
            data: MediaQuery.of(context).copyWith(alwaysUse24HourFormat: false),
            child: child!,
          );
        },
      );

      if (pickedStartDate != null) {
        setState(() {
          _startDate = pickedStartDate;

          // Ensure end date is after start date
          if (_endDate != null && pickedStartDate.isAfter(_endDate!)) {
            _endDate = pickedStartDate;
          }

          // Trigger callback with formatted dates
          widget.onDateRangeSelected?.call(
            _startDate!.toFormattedString(formatType: widget.dateFormatType),
            _endDate!.toFormattedString(formatType: widget.dateFormatType),
          );
        });
      }
    } catch (error, stackTrace) {
      AppLogger().error(
        'Error selecting start date',
        error: error,
        stackTrace: stackTrace,
      );
    }
  }

  Future<void> _selectEndDate(BuildContext context) async {
    try {
      if (_startDate == null) {
        // If the start date is not selected yet, show an error message
        return;
      }

      DateTime firstDate = _startDate!; // Ensure end date is >= Start date
      DateTime lastDate = widget.maxDate ?? DateTime.utc(2040);

      DateTime? pickedEndDate = await showDatePicker(
        context: context,
        firstDate: firstDate,
        lastDate: lastDate,
        initialDate: _endDate ?? _startDate!,
        builder: (BuildContext context, Widget? child) {
          return MediaQuery(
            data: MediaQuery.of(context).copyWith(alwaysUse24HourFormat: false),
            child: child!,
          );
        },
      );

      if (pickedEndDate != null) {
        setState(() {
          _endDate = pickedEndDate;

          // Trigger callback with formatted dates
          widget.onDateRangeSelected?.call(
            _startDate!.toFormattedString(formatType: widget.dateFormatType),
            _endDate!.toFormattedString(formatType: widget.dateFormatType),
          );
        });
      }
    } catch (error, stackTrace) {
      AppLogger().error(
        'Error selecting end date',
        error: error,
        stackTrace: stackTrace,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Expanded(child: _buildStartDateField(context)),
        SizedBox(width: 16.w),
        Expanded(child: _buildEndDateField(context)),
      ],
    );
  }

  Widget _buildStartDateField(BuildContext context) {
    return GestureDetector(
      onTap: () => _selectStartDate(context),
      child: InputDecorator(
        decoration: _buildDateFieldDecoration(context, widget.startLabelText),
        child: Text(
          _startDate != null
              ? _startDate!.toFormattedString(formatType: widget.dateFormatType)
              : 'Select Start Date',
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ),
    );
  }

  Widget _buildEndDateField(BuildContext context) {
    return GestureDetector(
      onTap: () => _selectEndDate(context),
      child: InputDecorator(
        decoration: _buildDateFieldDecoration(context, widget.endLabelText),
        child: Text(
          _endDate != null
              ? _endDate!.toFormattedString(formatType: widget.dateFormatType)
              : 'Select End Date',
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ),
    );
  }

  InputDecoration _buildDateFieldDecoration(
      BuildContext context, String labelText) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final iconColor = isDarkMode ? Colors.white : Colors.black;

    return InputDecoration(
      labelText: labelText,
      prefixIcon: Padding(
        padding: EdgeInsets.all(0.h),
        child: Icon(Icons.calendar_today, size: 16.h, color: iconColor),
      ),
      fillColor: context.appColors.surfaceColor,
      filled: true,
    );
  }
}
