import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hodan_hospital/core/config/theme/colors/app_colors_extension.dart';
import 'package:hodan_hospital/core/enums/dropdown_type.dart';
import 'package:hodan_hospital/core/utils/extensions/build_context_extensions.dart';

class CustomDropDown<T> extends StatelessWidget {
  const CustomDropDown({
    super.key,
    required this.items,
    required this.displayItem,
    required this.labelText,
    required this.onChanged,
    this.onTap,
    this.leadingImgPath,
    this.validator,
    this.value,
    this.fillColor,
    this.dropdownType = DropdownType.selectable, // Default to selectable
  });

  final List<T> items;
  final void Function(T?)? onChanged;
  final String? Function(T?)? displayItem;
  final String labelText;
  final Function()? onTap;
  final String? leadingImgPath;
  final String? Function(T?)? validator;
  final T? value;
  final Color? fillColor;
  final DropdownType dropdownType; // New property

  @override
  Widget build(BuildContext context) {
    final borderRadius = 16;
    final appColors = context.appColors;
    final textTheme = context.textTheme;

    return DropdownButtonFormField<T>(
      value: value,
      onTap: dropdownType != DropdownType.nonSelectable ? onTap : null,
      menuMaxHeight: 300.h,
      items: dropdownType == DropdownType.singleOption
          ? [
              DropdownMenuItem(
                value: items.first,
                child: Text(
                  displayItem!(items.first)!,
                  style: textTheme.bodyMedium,
                ),
              )
            ]
          : items.map((status) {
              return DropdownMenuItem(
                value: status,
                child: Text(
                  displayItem!(status)!,
                  style: textTheme.bodyMedium,
                ),
              );
            }).toList(),
      onChanged: dropdownType != DropdownType.nonSelectable ? onChanged : null,
      validator: validator,
      autovalidateMode: AutovalidateMode.onUserInteraction,
      decoration: InputDecoration(
        contentPadding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
        label: Text(
          labelText,
          style: textTheme.bodyMedium,
        ),
        errorStyle: textTheme.titleMedium?.copyWith(
          color: appColors.errorColor,
        ),
        fillColor: fillColor ?? appColors.surfaceColor,
        filled: true,
        hintStyle: textTheme.bodySmall,
        labelStyle: textTheme.labelMedium,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadius.r),
          borderSide: BorderSide(
            color: appColors.surfaceColor,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadius.r),
          borderSide: BorderSide(
            color: appColors.surfaceColor,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadius.r),
          borderSide: BorderSide(
            color: appColors.primaryColor,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadius.r),
          borderSide: BorderSide(
            color: appColors.errorColor.withValues(alpha: 0.5),
          ),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadius.r.r),
          borderSide: BorderSide(
            color: appColors.errorColor,
          ),
        ),
        prefix: leadingImgPath != null
            ? Padding(
                padding: EdgeInsets.only(right: 10.w),
                child: SvgPicture.asset(
                  leadingImgPath!,
                  width: 20.w,
                  height: 20.h,
                  colorFilter:
                      ColorFilter.mode(appColors.subtextColor, BlendMode.srcIn),
                ),
              )
            : null,
      ),
    );
  }
}
