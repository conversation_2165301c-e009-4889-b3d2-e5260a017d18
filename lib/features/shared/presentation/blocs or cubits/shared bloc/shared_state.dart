part of 'shared_bloc.dart';

abstract class SharedState extends Equatable {
  const SharedState();

  @override
  List<Object?> get props => [];
}

class SharedInitial extends SharedState {}

// --- Generate Order PDF states ---
class GenerateOrderPdfLoading extends SharedState {}

class GenerateOrderPdfSuccess extends SharedState {
  final Uint8List pdfBytes;

  const GenerateOrderPdfSuccess(this.pdfBytes);

  @override
  List<Object?> get props => [pdfBytes];
}

class GenerateOrderPdfFailure extends SharedState {
  final AppFailure failure;

  const GenerateOrderPdfFailure(this.failure);

  @override
  List<Object?> get props => [failure];
}

// --- Generate Appointment PDF states ---
class GenerateAppointmentPdfLoading extends SharedState {}

class GenerateAppointmentPdfSuccess extends SharedState {
  final Uint8List pdfBytes;

  const GenerateAppointmentPdfSuccess(this.pdfBytes);

  @override
  List<Object?> get props => [pdfBytes];
}

class GenerateAppointmentPdfFailure extends SharedState {
  final AppFailure failure;

  const GenerateAppointmentPdfFailure(this.failure);

  @override
  List<Object?> get props => [failure];
}

// --- Generate Lab Result PDF states ---
class GenerateLabResultPdfLoading extends SharedState {}

class GenerateLabResultPdfSuccess extends SharedState {
  final Uint8List pdfBytes;

  const GenerateLabResultPdfSuccess(this.pdfBytes);

  @override
  List<Object?> get props => [pdfBytes];
}

class GenerateLabResultPdfFailure extends SharedState {
  final AppFailure failure;

  const GenerateLabResultPdfFailure(this.failure);

  @override
  List<Object?> get props => [failure];
}

// --- Download Order PDF states ---
class DownloadOrderPdfLoading extends SharedState {}

class DownloadOrderPdfSuccess extends SharedState {
  final String filePath;

  const DownloadOrderPdfSuccess(this.filePath);

  @override
  List<Object?> get props => [filePath];
}

class DownloadOrderPdfFailure extends SharedState {
  final AppFailure failure;

  const DownloadOrderPdfFailure(this.failure);

  @override
  List<Object?> get props => [failure];
}

// --- Download Appointment PDF states ---
class DownloadAppointmentPdfLoading extends SharedState {}

class DownloadAppointmentPdfSuccess extends SharedState {
  final String filePath;

  const DownloadAppointmentPdfSuccess(this.filePath);

  @override
  List<Object?> get props => [filePath];
}

class DownloadAppointmentPdfFailure extends SharedState {
  final AppFailure failure;

  const DownloadAppointmentPdfFailure(this.failure);

  @override
  List<Object?> get props => [failure];
}


// --- Send SMS states ---
class SendSMSLoading extends SharedState {}

class SendSMSSuccess extends SharedState {
  final String message;

  const SendSMSSuccess(this.message);

  @override
  List<Object?> get props => [message];
}

class SendSMSFailure extends SharedState {
  final AppFailure failure;

  const SendSMSFailure(this.failure);

  @override
  List<Object?> get props => [failure];
}
