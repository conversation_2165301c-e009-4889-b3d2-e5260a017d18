part of 'shared_bloc.dart';

abstract class SharedEvent extends Equatable {
  const SharedEvent();

  @override
  List<Object> get props => [];
}

class GenerateOrderPdf extends SharedEvent {
  final String id;

  const GenerateOrderPdf({
    required this.id,
  });

  @override
  List<Object> get props => [id];
}

class GenerateAppointmentPdf extends SharedEvent {
  final String id;

  const GenerateAppointmentPdf({
    required this.id,
  });

  @override
  List<Object> get props => [id];
}

class GenerateLabResultPdf extends SharedEvent {
  final String id;

  const GenerateLabResultPdf({
    required this.id,
  });

  @override
  List<Object> get props => [id];
}

class DownloadAppointmentPdf extends SharedEvent {
  final Uint8List pdfPytes;

  const DownloadAppointmentPdf({
    required this.pdfPytes,
  });

  @override
  List<Object> get props => [pdfPytes];
}

class DownloadOrderPdf extends SharedEvent {
  final Uint8List pdfBytes;

  const DownloadOrderPdf({
    required this.pdfBytes,
  });

  @override
  List<Object> get props => [pdfBytes];
}

class SendSMS extends SharedEvent {
  final String mobileNumber;
  final String message;

  const SendSMS({
    required this.mobileNumber,
    required this.message,
  });

  @override
  List<Object> get props => [mobileNumber, message];
}
