import 'package:equatable/equatable.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/core/utils/helpers/bloc_helper.dart';
import 'package:hodan_hospital/features/shared/domain/params/generate_pdf_params.dart';
import 'package:hodan_hospital/features/shared/domain/params/download_pdf_params.dart';

import '../../../domain/params/send_sms_params.dart';
import '../../../domain/usecases/download_pdf_use_case.dart';
import '../../../domain/usecases/generate_pdf_use_case.dart';
import '../../../domain/usecases/send_sms_usecase.dart';

part 'shared_event.dart';
part 'shared_state.dart';

class SharedBloc extends Bloc<SharedEvent, SharedState> {
  final GeneratePdfUseCase generatePdfUseCase;
  final DownloadPdfUseCase downloadPdfUseCase;
  final SendSMSUseCase sendSMSUseCase;

  SharedBloc({
    required this.generatePdfUseCase,
    required this.downloadPdfUseCase,
    required this.sendSMSUseCase,
  }) : super(SharedInitial()) {
    on<GenerateOrderPdf>(
      _onGenerateOrderPdf,
      transformer: BlocHelper.debounceHelper(),
    );
    on<GenerateAppointmentPdf>(
      _onGenerateAppointmentPdf,
      transformer: BlocHelper.debounceHelper(),
    );
    on<GenerateLabResultPdf>(
      _onGenerateLabResultPdf,
      transformer: BlocHelper.debounceHelper(),
    );
    on<DownloadOrderPdf>(
      _onDownloadOrderPdf,
      transformer: BlocHelper.debounceHelper(),
    );
    on<DownloadAppointmentPdf>(
      _onDownloadAppointmentPdf,
      transformer: BlocHelper.debounceHelper(),
    );
    on<SendSMS>(
      _onSendSMS,
      transformer: BlocHelper.debounceHelper(),
    );
  }

  Future<void> _onGenerateOrderPdf(
    GenerateOrderPdf event,
    Emitter<SharedState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<Uint8List, SharedState>(
      emit: emit,
      loadingState: GenerateOrderPdfLoading(),
      callUseCase: generatePdfUseCase(
        params: GeneratePdfParams(
          id: event.id,
          doctype: 'Sales Order',
          // format: 'Standard',
          format: 'Sales Order',
        ),
      ),
      onSuccess: (pdfBytes) => GenerateOrderPdfSuccess(pdfBytes),
      onFailure: (failure) => GenerateOrderPdfFailure(failure),
    );
  }

  Future<void> _onGenerateAppointmentPdf(
    GenerateAppointmentPdf event,
    Emitter<SharedState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<Uint8List, SharedState>(
      emit: emit,
      loadingState: GenerateAppointmentPdfLoading(),
      callUseCase: generatePdfUseCase(
        params: GeneratePdfParams(
          id: event.id,
          doctype: 'Que',
          format: 'Que Printing',
        ),
      ),
      onSuccess: (pdfBytes) => GenerateAppointmentPdfSuccess(pdfBytes),
      onFailure: (failure) => GenerateAppointmentPdfFailure(failure),
    );
  }

  Future<void> _onGenerateLabResultPdf(
    GenerateLabResultPdf event,
    Emitter<SharedState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<Uint8List, SharedState>(
      emit: emit,
      loadingState: GenerateLabResultPdfLoading(),
      callUseCase: generatePdfUseCase(
        params: GeneratePdfParams(
          id: event.id,
          doctype: 'Lab Result',
          format: 'Standard',
        ),
      ),
      onSuccess: (pdfBytes) => GenerateLabResultPdfSuccess(pdfBytes),
      onFailure: (failure) => GenerateLabResultPdfFailure(failure),
    );
  }

  Future<void> _onDownloadOrderPdf(
    DownloadOrderPdf event,
    Emitter<SharedState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<String, SharedState>(
      emit: emit,
      loadingState: DownloadOrderPdfLoading(),
      callUseCase: downloadPdfUseCase(
        params: DownloadPdfParams(pdfBytes: event.pdfBytes),
      ),
      onSuccess: (filePath) => DownloadOrderPdfSuccess(filePath),
      onFailure: (failure) => DownloadOrderPdfFailure(failure),
    );
  }

  Future<void> _onDownloadAppointmentPdf(
    DownloadAppointmentPdf event,
    Emitter<SharedState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<String, SharedState>(
      emit: emit,
      loadingState: DownloadAppointmentPdfLoading(),
      callUseCase: downloadPdfUseCase(
        params: DownloadPdfParams(pdfBytes: event.pdfPytes),
      ),
      onSuccess: (filePath) => DownloadAppointmentPdfSuccess(filePath),
      onFailure: (failure) => DownloadAppointmentPdfFailure(failure),
    );
  }

  Future<void> _onSendSMS(
    SendSMS event,
    Emitter<SharedState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<String, SharedState>(
      emit: emit,
      loadingState: SendSMSLoading(),
      callUseCase: sendSMSUseCase(
        params: SendSMSParams(
          mobileNumber: event.mobileNumber,
          message: event.message,
        ),
      ),
      onSuccess: (message) => SendSMSSuccess(message),
      onFailure: (failure) => SendSMSFailure(failure),
    );
  }
}
