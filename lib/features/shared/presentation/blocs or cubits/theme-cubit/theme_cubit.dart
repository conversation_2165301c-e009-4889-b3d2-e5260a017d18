import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/material.dart';
import 'package:hodan_hospital/features/shared/presentation/blocs%20or%20cubits/theme-cubit/theme_storage.dart';

class ThemeCubit extends Cubit<ThemeMode> {
  final ThemeStorage themeStorage;
  ThemeCubit({
    required this.themeStorage,
  }) : super(ThemeMode.light) {
    _loadTheme();
  }

  void _loadTheme() async {
    final savedTheme = await themeStorage.getThemeMode();
    emit(savedTheme);
  }

  void toggleTheme(bool isDark) {
    final mode = isDark ? ThemeMode.dark : ThemeMode.light;
    themeStorage.saveThemeMode(mode);
    emit(mode);
  }

  void setTheme(ThemeMode mode) {
    themeStorage.saveThemeMode(mode);
    emit(mode);
  }
}
