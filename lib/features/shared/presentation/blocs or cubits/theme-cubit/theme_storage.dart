import 'package:flutter/material.dart';
import 'package:hodan_hospital/core/services/flutter_secure_storage_services.dart';

class ThemeStorage {
  static const _key = 'theme_mode';
  final FlutterSecureStorageServices flutterSecureStorageServices;

  ThemeStorage({required this.flutterSecureStorageServices});

  Future<void> saveThemeMode(ThemeMode mode) async {
    await flutterSecureStorageServices.storeData(
      key: _key,
      value: mode.name,
    ); // stores "light" or "dark"
  }

  Future<ThemeMode> getThemeMode() async {
    final value = await flutterSecureStorageServices.readData(key: _key);
    switch (value) {
      case 'dark':
        return ThemeMode.dark;
      case 'light':
      default:
        return ThemeMode.light;
    }
  }
}
