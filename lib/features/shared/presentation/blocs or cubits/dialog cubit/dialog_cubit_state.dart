part of 'dialog_cubit.dart';

abstract class DialogState extends Equatable {
  const DialogState();

  @override
  List<Object?> get props => [];
}

class DialogClosed extends DialogState {}

class DialogOpened extends DialogState {
  final String title;
  final String message;
  final DialogType dialogType;
  final String? confirmButtonText;
  final String? cancelButtonText;
  final double? confirmButtonwidth;
  final double? cancelButtonwidth;
  final double confirmButtonHeight;
  final double cancelButtonHeight;
  final bool barrierDismissible;
  final Duration? autoCloseDuration;
  final VoidCallback? onConfirm;
  final VoidCallback? onCancel;

  const DialogOpened({
    required this.title,
    required this.message,
    required this.dialogType,
    this.confirmButtonText,
    this.cancelButtonText,
    this.barrierDismissible = true,
    this.autoCloseDuration,
    required this.onConfirm,
    required this.onCancel,
    required this.confirmButtonwidth,
    required this.cancelButtonwidth,
    this.cancelButtonHeight = 30,
    this.confirmButtonHeight = 30,
  });

  @override
  List<Object?> get props => [
        title,
        message,
        dialogType,
        confirmButtonText,
        cancelButtonText,
        barrierDismissible,
        autoCloseDuration,
        onConfirm,
        onCancel,
        confirmButtonwidth,
        cancelButtonwidth,
        confirmButtonHeight,
        cancelButtonHeight,
      ];
}
