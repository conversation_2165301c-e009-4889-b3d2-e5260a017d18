import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hodan_hospital/core/config/logger/app_logger.dart';
import 'package:hodan_hospital/core/config/router/extension/navigation_extension.dart';
import 'package:hodan_hospital/core/config/theme/colors/app_colors_extension.dart';
import 'package:hodan_hospital/core/utils/extensions/app_bloc_extensions.dart';
import 'package:hodan_hospital/features/authentication/presentation/bloc/authentication_bloc.dart';
import 'package:hodan_hospital/features/authentication/presentation/pages/login_page.dart';
import 'package:hodan_hospital/features/main/presentation/pages/main_page.dart';
import 'package:hodan_hospital/gen/assets.gen.dart';

class SplashPage extends StatefulWidget {
  const SplashPage({super.key});

  @override
  State<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> {
  @override
  void initState() {
    super.initState();
    context.authenticationBloc.add(CheckUserAuthenticationEvent());
    Future.delayed(const Duration(seconds: 3), _navigate);
  }

  void _navigate() {
    if (!mounted) return;
    final authState = context.authenticationBloc.state;
    AppLogger().info('💡 authState: $authState');

    if (authState is AuthenticationInitial ||
            authState is Unauthenticated ||
            authState is OnboardingCompleted
        //  || authState is RegistrationSuccess
        ) {
      context.pushAndRemoveUntilRoute(const LoginPage());
      // context.pushAndRemoveUntilRoute(const MainPage());
      return;
    }
    if (authState is OnboardingRequired) {
      // context.pushAndRemoveUntilRoute(const OnBoardingPage());
      context.pushAndRemoveUntilRoute(const LoginPage());
      return;
    }
    if (authState is Authenticated) {
      context.pushAndRemoveUntilRoute(const MainPage());
      return;
    }

    //
    else {
      context.pushAndRemoveUntilRoute(const LoginPage());
    }
  }

  @override
  Widget build(BuildContext context) {
    final appColors = context.appColors;
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color.fromARGB(255, 255, 255, 255),
            Color.fromARGB(255, 190, 191, 198),
            Color.fromARGB(255, 190, 191, 198),
            Color.fromARGB(255, 57, 91, 147),
          ],
        ),
      ),
      child: Scaffold(
        backgroundColor: appColors.transparentColor,
        body: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Center(
              child: Container(
                height: 250.h,
                // width: 250.w,
                margin: EdgeInsets.symmetric(horizontal: 40.h),
                child: Image.asset(
                  Assets.icon.appLogo.path,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
