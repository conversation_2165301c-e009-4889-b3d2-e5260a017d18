import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:hodan_hospital/core/config/logger/app_logger.dart';
import 'package:hodan_hospital/core/config/router/extension/navigation_extension.dart';
import 'package:hodan_hospital/core/config/theme/colors/app_colors_extension.dart';
import 'package:hodan_hospital/core/utils/extensions/app_bloc_extensions.dart';
import 'package:hodan_hospital/core/utils/extensions/build_context_extensions.dart';
import 'package:hodan_hospital/features/authentication/presentation/bloc/authentication_bloc.dart';
import 'package:hodan_hospital/features/authentication/presentation/pages/login_page.dart';
import 'package:hodan_hospital/features/main/presentation/pages/main_page.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/animations/animated_item_wrapper.dart';
import 'package:hodan_hospital/gen/assets.gen.dart';

class SplashPage extends StatefulWidget {
  const SplashPage({super.key});

  @override
  State<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> {
  @override
  void initState() {
    super.initState();
    context.authenticationBloc.add(CheckUserAuthenticationEvent());
    Future.delayed(const Duration(seconds: 3000000), _navigate);
  }

  void _navigate() {
    if (!mounted) return;
    final authState = context.authenticationBloc.state;
    AppLogger().info('💡 authState: $authState');

    if (authState is AuthenticationInitial ||
            authState is Unauthenticated ||
            authState is OnboardingCompleted
        //  || authState is RegistrationSuccess
        ) {
      context.pushAndRemoveUntilRoute(const LoginPage());
      // context.pushAndRemoveUntilRoute(const MainPage());
      return;
    }
    if (authState is OnboardingRequired) {
      // context.pushAndRemoveUntilRoute(const OnBoardingPage());
      context.pushAndRemoveUntilRoute(const LoginPage());
      return;
    }
    if (authState is Authenticated) {
      context.pushAndRemoveUntilRoute(const MainPage());
      return;
    }

    //
    else {
      context.pushAndRemoveUntilRoute(const LoginPage());
    }
  }

  @override
  Widget build(BuildContext context) {
    final appColors = context.appColors;
    final textTheme = context.textTheme;

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            appColors.whiteColor,
            appColors.primaryColor.withValues(alpha: 0.1),
            appColors.primaryColor.withValues(alpha: 0.3),
            appColors.primaryColor,
          ],
        ),
      ),
      child: Scaffold(
        backgroundColor: appColors.transparentColor,
        body: SafeArea(
          child: Column(
            children: [
              // Main content area
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // App Logo with Animation
                    AnimatedItemWrapper(
                      child: Container(
                        height: 200.h,
                        width: 200.w,
                        margin: EdgeInsets.symmetric(horizontal: 40.w),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(20.r),
                          boxShadow: [
                            BoxShadow(
                              color:
                                  appColors.primaryColor.withValues(alpha: 0.3),
                              blurRadius: 20,
                              offset: const Offset(0, 10),
                            ),
                          ],
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(20.r),
                          child: Image.asset(
                            Assets.icon.appLogo.path,
                            fit: BoxFit.contain,
                          ),
                        ),
                      ),
                    ),

                    SizedBox(height: 40.h),

                    // App Name/Title (if needed)
                    AnimatedItemWrapper(
                      delay: const Duration(milliseconds: 600),
                      child: Text(
                        'Hodan Hospital',
                        style: textTheme.headlineMedium?.copyWith(
                          color: appColors.whiteColor,
                          fontWeight: FontWeight.bold,
                          letterSpacing: 1.2,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),

                    SizedBox(height: 8.h),

                    // Subtitle
                    AnimatedItemWrapper(
                      delay: const Duration(milliseconds: 900),
                      child: Text(
                        'Your Health, Our Priority',
                        style: textTheme.bodyLarge?.copyWith(
                          color: appColors.whiteColor.withValues(alpha: 0.9),
                          fontWeight: FontWeight.w300,
                          letterSpacing: 0.5,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
              ),

              // Powered by section at bottom
              AnimatedItemWrapper(
                delay: const Duration(milliseconds: 1200),
                child: Container(
                  padding: EdgeInsets.only(bottom: 30.h),
                  child: Column(
                    children: [
                      // Divider line
                      Container(
                        width: 60.w,
                        height: 1.h,
                        color: appColors.whiteColor.withValues(alpha: 0.3),
                        margin: EdgeInsets.only(bottom: 12.h),
                      ),

                      // Powered by text
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            FontAwesomeIcons.code,
                            color: appColors.whiteColor.withValues(alpha: 0.7),
                            size: 14.sp,
                          ),
                          SizedBox(width: 8.w),
                          Text(
                            'Powered by ',
                            style: textTheme.bodySmall?.copyWith(
                              color:
                                  appColors.whiteColor.withValues(alpha: 0.7),
                              fontSize: 12.sp,
                            ),
                          ),
                          Text(
                            'Rasiin Tech',
                            style: textTheme.bodySmall?.copyWith(
                              color: appColors.whiteColor,
                              fontSize: 12.sp,
                              fontWeight: FontWeight.bold,
                              letterSpacing: 0.5,
                            ),
                          ),
                        ],
                      ),

                      SizedBox(height: 4.h),

                      // Version or additional info
                      Text(
                        'Innovative Healthcare Solutions',
                        style: textTheme.bodySmall?.copyWith(
                          color: appColors.whiteColor.withValues(alpha: 0.5),
                          fontSize: 10.sp,
                          letterSpacing: 0.3,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
