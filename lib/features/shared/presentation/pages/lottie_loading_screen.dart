// import 'package:flutter/material.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:hodan_demo/core/utils/extensions/build_context_extensions.dart';
// import 'package:lottie/lottie.dart';

// class LottieLoadinScreen extends StatelessWidget {
//   const LottieLoadinScreen({super.key});

//   @override
//   Widget build(BuildContext context) {
//     final textTheme = context.textTheme;
//     return Scaffold(
//       backgroundColor: Colors.deepPurple.shade400,
//       body: Center(
//         child: Column(
//           mainAxisAlignment: MainAxisAlignment.center,
//           children: [
//             Text(
//               "Please wait...",
//               // style: CustomTextStyles.secondaryMediumTextStyle,
//               style: textTheme.bodyMedium,
//             ),
//             SizedBox(
//               height: 20.h,
//             ),
//             Lottie.asset(
//               'assets/lottie/lottie1.json',
//               height: 200.h,
//               width: 300.w,
//               fit: BoxFit.cover,
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }
