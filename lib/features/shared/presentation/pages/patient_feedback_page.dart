import 'dart:convert';
import 'dart:io' show Platform;

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:hodan_hospital/core/config/logger/app_logger.dart';
import 'package:hodan_hospital/core/config/theme/colors/app_colors_extension.dart';
import 'package:hodan_hospital/core/utils/extensions/app_bloc_extensions.dart';
import 'package:hodan_hospital/core/utils/extensions/build_context_extensions.dart';
import 'package:package_info_plus/package_info_plus.dart';

import '../../../../core/enums/button_state.dart';
import '../../../../core/utils/helpers/snack_bar_helper.dart';
import '../../../user/presentation/bloc/user_bloc.dart';
import '../widgets/custom_button.dart';
import '../widgets/custom_drop_down.dart';
import '../widgets/custom_textfield.dart';

class PatientFeedbackPage extends StatefulWidget {
  const PatientFeedbackPage({super.key});

  @override
  State<PatientFeedbackPage> createState() => _PatientFeedbackPageState();
}

class _PatientFeedbackPageState extends State<PatientFeedbackPage>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _commentsController = TextEditingController();
  final PageController _pageController = PageController();
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  // Form state
  String? _feedbackType;
  double _rating = 0.0;
  String? _appFeedbackCategory;
  String? _appVersion;
  String? _deviceInfo;

  // Feedback type options
  final List<String> _feedbackTypes = [
    'General Feedback',
    'Doctor Feedback',
    'Facility Feedback',
    'Appointment Feedback',
    'Service Feedback',
    'App Related Feedback',
  ];

  // App feedback categories
  final List<String> _appCategories = [
    'UI/UX',
    'Performance',
    'Bug Report',
    'Feature Request',
    'Navigation',
    'Other',
  ];

  // Rating feedback data
  final Map<int, Map<String, String>> _ratingFeedback = {
    1: {
      'emoji': '😞',
      'title': 'Very Dissatisfied',
      'description':
          'We\'re sorry to hear about your poor experience. Your feedback helps us improve.',
      'color': '#FF5252',
    },
    2: {
      'emoji': '😕',
      'title': 'Dissatisfied',
      'description':
          'We understand your concerns and will work to address them.',
      'color': '#FF7043',
    },
    3: {
      'emoji': '😐',
      'title': 'Neutral',
      'description':
          'Thank you for your feedback. We\'d love to know how we can improve.',
      'color': '#FFA726',
    },
    4: {
      'emoji': '😊',
      'title': 'Satisfied',
      'description':
          'Great! We\'re happy you had a positive experience with us.',
      'color': '#66BB6A',
    },
    5: {
      'emoji': '😍',
      'title': 'Very Satisfied',
      'description':
          'Excellent! Thank you for choosing Hodan Hospital. Your satisfaction is our priority.',
      'color': '#4CAF50',
    },
  };

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _animationController.forward();
    _fetchDeviceAndVersionInfo();
  }

  Future<void> _fetchDeviceAndVersionInfo() async {
    try {
      // Run both operations in parallel
      final results = await Future.wait([
        PackageInfo.fromPlatform(),
        _getDeviceInfoDetails(), // Renamed to avoid confusion
      ]);

      final packageInfo = results[0] as PackageInfo;
      final deviceInfo = results[1] as String;

      setState(() {
        _appVersion = '${packageInfo.version}+${packageInfo.buildNumber}';
        _deviceInfo = deviceInfo;
      });

      AppLogger().info('''
      App Info Collected:
      Version: $_appVersion
      Device: $deviceInfo
    ''');
    } catch (e, s) {
      AppLogger().error(
        'Failed to collect app/device info',
        error: e,
        stackTrace: s,
      );
      // Set safe defaults
      setState(() {
        _appVersion = 'unknown';
        _deviceInfo = '{"error":"collection_failed"}';
      });
    }
  }

  Future<String> _getDeviceInfoDetails() async {
    final deviceInfo = DeviceInfoPlugin();
    try {
      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        return jsonEncode({
          'model': androidInfo.model,
          'brand': androidInfo.brand,
          'sdkVersion': androidInfo.version.sdkInt,
          'device': androidInfo.device,
          'platform': 'android',
        });
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        return jsonEncode({
          'model': iosInfo.model,
          'systemVersion': iosInfo.systemVersion,
          'machine': iosInfo.utsname.machine,
          'platform': 'ios',
        });
      }
      return '{"platform":"unknown"}';
    } catch (e) {
      return '{"error":"${e.toString().replaceAll('"', "'")}"}';
    }
  }

  @override
  void dispose() {
    _commentsController.dispose();
    _pageController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final appColors = context.appColors;
    final textTheme = context.textTheme;
    final isAppFeedback = _feedbackType == 'App Related Feedback';

    return Scaffold(
      backgroundColor: appColors.backgroundColor,
      appBar: AppBar(
        title: Text(
          'Share Your Experience',
          style: textTheme.titleLarge?.copyWith(
            color: appColors.whiteColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: appColors.primaryColor,
        elevation: 0,
        centerTitle: true,
        iconTheme: IconThemeData(color: appColors.whiteColor),
      ),
      body: BlocListener<UserBloc, UserState>(
        listener: (context, state) {
          if (state is FeedbackSending) {
            context.dialogCubit.showLoadingDialog(
              message: 'Sending feedback, please wait...',
            );
          }
          if (state is FeedbackSent) {
            context.dialogCubit.closeDialog();
            SnackBarHelper.showSuccessSnackBar(
              context,
              message: state.message,
            );
            Navigator.pop(context);
          }
          if (state is FeedbackFailure) {
            context.dialogCubit.closeDialog();
            SnackBarHelper.showErrorSnackBar(
              context,
              message: state.appFailure.getErrorMessage(),
            );
          }
        },
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: SingleChildScrollView(
            child: Column(
              children: [
                // Main Content
                Padding(
                  padding: const EdgeInsets.all(20.0),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      children: [
                        // Question Title and Description
                        _buildQuestionSection(context),

                        const SizedBox(height: 32),

                        // Rating Section
                        _buildRatingSection(context),

                        // Rating Feedback Card (shows when rating is selected)
                        if (_rating > 0) ...[
                          const SizedBox(height: 24),
                          _buildRatingFeedbackCard(context),
                        ],

                        const SizedBox(height: 32),

                        // Feedback Details Section
                        if (_rating > 0) ...[
                          _buildFeedbackDetailsSection(context, isAppFeedback),
                          const SizedBox(height: 32),

                          // Submit Button
                          _buildSubmitButton(context),
                        ],
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildQuestionSection(
    BuildContext context,
  ) {
    final appColors = context.appColors;
    final textTheme = context.textTheme;
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: appColors.whiteColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: appColors.dividerColor.withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(
            Icons.help_outline,
            size: 48,
            color: appColors.primaryColor,
          ),
          const SizedBox(height: 16),
          Text(
            'How was your experience?',
            style: textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: appColors.textColor,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 12),
          Text(
            'Please rate your overall experience with our hospital services. Your honest feedback helps us serve you better.',
            style: textTheme.bodyMedium?.copyWith(
              color: appColors.subtextColor,
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildRatingSection(BuildContext context) {
    final appColors = context.appColors;
    final textTheme = context.textTheme;
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: appColors.transparentColor,
        borderRadius: BorderRadius.circular(16),
        // boxShadow: [
        //   BoxShadow(
        //     color: appColors.dividerColor.withValues(alpha: 0.3),
        //     blurRadius: 10,
        //     offset: const Offset(0, 2),
        //   ),
        // ],
      ),
      child: Column(
        children: [
          Text(
            'Rate Your Experience',
            style: textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: appColors.textColor,
            ),
          ),
          const SizedBox(height: 20),

          // Star Rating
          RatingBar.builder(
            initialRating: _rating,
            minRating: 1,
            maxRating: 5,
            itemSize: 50,
            itemPadding: const EdgeInsets.symmetric(horizontal: 8.0),
            itemBuilder: (context, index) => const Icon(
              Icons.star,
              color: Colors.amber,
            ),
            onRatingUpdate: (rating) {
              setState(() => _rating = rating);
            },
          ),

          if (_rating > 0) ...[
            const SizedBox(height: 16),
            Text(
              '${_rating.toInt()} out of 5 stars',
              style: textTheme.bodyLarge?.copyWith(
                color: appColors.primaryColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildRatingFeedbackCard(BuildContext context) {
    final appColors = context.appColors;
    final textTheme = context.textTheme;

    final ratingData = _ratingFeedback[_rating.toInt()];
    if (ratingData == null) return const SizedBox.shrink();

    final color =
        Color(int.parse(ratingData['color']!.replaceFirst('#', '0xFF')));

    return AnimatedContainer(
      duration: const Duration(milliseconds: 500),
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            color.withValues(alpha: 0.1),
            color.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 2,
        ),
      ),
      child: Column(
        children: [
          // Emoji
          Text(
            ratingData['emoji']!,
            style: const TextStyle(fontSize: 64),
          ),
          const SizedBox(height: 16),

          // Title
          Text(
            ratingData['title']!,
            style: textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 12),

          // Description
          Text(
            ratingData['description']!,
            style: textTheme.bodyMedium?.copyWith(
              color: appColors.textColor,
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildFeedbackDetailsSection(
      BuildContext context, bool isAppFeedback) {
    final appColors = context.appColors;
    final textTheme = context.textTheme;
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: appColors.whiteColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: appColors.dividerColor.withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Tell us more',
            style: textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: appColors.textColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Your detailed feedback helps us understand how to improve',
            style: textTheme.bodyMedium?.copyWith(
              color: appColors.subtextColor,
            ),
          ),
          const SizedBox(height: 20),

          // Feedback Type
          CustomDropDown<String>(
            labelText: 'Feedback Category',
            items: _feedbackTypes,
            displayItem: (item) => item,
            onChanged: (value) => setState(() => _feedbackType = value),
            validator: (value) =>
                value == null ? 'Please select feedback type' : null,
          ),

          const SizedBox(height: 20),

          // Comments
          CustomTextField(
            controller: _commentsController,
            hintText: 'Share your detailed feedback here...',
            maxLine: 5,
            validator: (value) =>
                value?.isEmpty ?? true ? 'Please share your feedback' : null,
          ),

          // App Feedback Section (Conditional)
          if (isAppFeedback) ...[
            const SizedBox(height: 20),
            CustomDropDown<String>(
              labelText: 'App Issue Category',
              items: _appCategories,
              displayItem: (item) => item,
              onChanged: (value) =>
                  setState(() => _appFeedbackCategory = value),
              validator: (value) =>
                  value == null ? 'Please select a category' : null,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSubmitButton(
    BuildContext context,
  ) {
    final appColors = context.appColors;
    // final textTheme = context.textTheme;
    return BlocBuilder<UserBloc, UserState>(
      buildWhen: (previous, current) =>
          current is FeedbackSending ||
          current is FeedbackSent ||
          current is FeedbackFailure,
      builder: (context, state) {
        final isSubmitting = state is FeedbackSending;
        final buttonState =
            isSubmitting ? ButtonState.loading : ButtonState.normal;

        return Container(
          width: double.infinity,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: appColors.primaryColor.withValues(alpha: 0.3),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: CustomButton(
            buttonState: buttonState,
            buttonText: 'Submit Feedback',
            width: double.infinity,
            height: 56,
            onTap: () {
              if (_formKey.currentState!.validate()) {
                context.userBloc.add(
                  SendFeedbackEvent(
                    feedbackType: _feedbackType!,
                    rating: _rating,
                    appFeedbackCategory: _appFeedbackCategory,
                    comments: _commentsController.text,
                    appVersion: _appVersion!,
                    deviceInfo: _deviceInfo!,
                  ),
                );
              }
            },
          ),
        );
      },
    );
  }
}
