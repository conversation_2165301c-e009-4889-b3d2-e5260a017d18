import 'dart:convert';
import 'dart:io' show Platform;

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:hodan_hospital/core/config/logger/app_logger.dart';
import 'package:hodan_hospital/core/utils/extensions/app_bloc_extensions.dart';
import 'package:hodan_hospital/core/utils/extensions/build_context_extensions.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/animations/animated_app_bar.dart';
import 'package:package_info_plus/package_info_plus.dart';

import '../../../../core/enums/button_state.dart';
import '../../../../core/utils/helpers/snack_bar_helper.dart';
import '../../../user/presentation/bloc/user_bloc.dart';
import '../widgets/custom_button.dart';
import '../widgets/custom_drop_down.dart';
import '../widgets/custom_textfield.dart';

class PatientFeedbackPage extends StatefulWidget {
  const PatientFeedbackPage({super.key});

  @override
  State<PatientFeedbackPage> createState() => _PatientFeedbackPageState();
}

class _PatientFeedbackPageState extends State<PatientFeedbackPage> {
  final _formKey = GlobalKey<FormState>();
  final _commentsController = TextEditingController();

  // Form state
  String? _feedbackType;
  double _rating = 3.0;
  String? _appFeedbackCategory;
  String? _appVersion;
  String? _deviceInfo;

  // Feedback type options
  final List<String> _feedbackTypes = [
    'General Feedback',
    'Doctor Feedback',
    'Facility Feedback',
    'Appointment Feedback',
    'Service Feedback',
    'App Related Feedback',
  ];

  // App feedback categories
  final List<String> _appCategories = [
    'UI/UX',
    'Performance',
    'Bug Report',
    'Feature Request',
    'Navigation',
    'Other',
  ];

  @override
  void initState() {
    super.initState();
    _fetchDeviceAndVersionInfo();
  }

  Future<void> _fetchDeviceAndVersionInfo() async {
    try {
      // Run both operations in parallel
      final results = await Future.wait([
        PackageInfo.fromPlatform(),
        _getDeviceInfoDetails(), // Renamed to avoid confusion
      ]);

      final packageInfo = results[0] as PackageInfo;
      final deviceInfo = results[1] as String;

      setState(() {
        _appVersion = '${packageInfo.version}+${packageInfo.buildNumber}';
        _deviceInfo = deviceInfo;
      });

      AppLogger().info('''
      App Info Collected:
      Version: $_appVersion
      Device: $deviceInfo
    ''');
    } catch (e, s) {
      AppLogger().error(
        'Failed to collect app/device info',
        error: e,
        stackTrace: s,
      );
      // Set safe defaults
      setState(() {
        _appVersion = 'unknown';
        _deviceInfo = '{"error":"collection_failed"}';
      });
    }
  }

  Future<String> _getDeviceInfoDetails() async {
    final deviceInfo = DeviceInfoPlugin();
    try {
      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        return jsonEncode({
          'model': androidInfo.model,
          'brand': androidInfo.brand,
          'sdkVersion': androidInfo.version.sdkInt,
          'device': androidInfo.device,
          'platform': 'android',
        });
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        return jsonEncode({
          'model': iosInfo.model,
          'systemVersion': iosInfo.systemVersion,
          'machine': iosInfo.utsname.machine,
          'platform': 'ios',
        });
      }
      return '{"platform":"unknown"}';
    } catch (e) {
      return '{"error":"${e.toString().replaceAll('"', "'")}"}';
    }
  }

  @override
  void dispose() {
    _commentsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isAppFeedback = _feedbackType == 'App Related Feedback';

    return Scaffold(
      appBar: const AnimatedAppBar(
        title: 'Submit Feedback',
      ),
      body: BlocListener<UserBloc, UserState>(
        listener: (context, state) {
          if (state is FeedbackSending) {
            context.dialogCubit.showLoadingDialog(
              // title: 'Please Wait',
              message: 'Sending feedback, please wait...',
            );
          }
          if (state is FeedbackSent) {
            context.dialogCubit.closeDialog();

            //
            SnackBarHelper.showSuccessSnackBar(
              context,
              message: state.message,
            );

            // pop
            Navigator.pop(context);
          }
          if (state is FeedbackFailure) {
            context.dialogCubit.closeDialog();

            //
            SnackBarHelper.showErrorSnackBar(
              context,
              message: state.appFailure.getErrorMessage(),
            );
          }
        },
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 24),

                // Feedback Type
                CustomDropDown<String>(
                  labelText: 'Feedback Type',
                  items: _feedbackTypes,
                  displayItem: (item) => item,
                  onChanged: (value) => setState(() => _feedbackType = value),
                  validator: (value) =>
                      value == null ? 'Please select feedback type' : null,
                ),

                const SizedBox(height: 20),

                // Rating Slider
                // Column(
                //   crossAxisAlignment: CrossAxisAlignment.start,
                //   children: [
                //     Text('Rating: ${_rating.toStringAsFixed(1)}',
                //         style: context.textTheme.bodyMedium),
                //     Slider(
                //       value: _rating,
                //       min: 1,
                //       max: 5,
                //       divisions: 4,
                //       label: _rating.toStringAsFixed(1),
                //       onChanged: (value) => setState(() => _rating = value),
                //     ),
                //   ],
                // ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Rating: ${_rating.toStringAsFixed(1)}',
                        style: context.textTheme.bodyMedium),
                    const SizedBox(height: 8),
                    RatingBar.builder(
                      initialRating: _rating,
                      minRating: 1,
                      maxRating: 5,
                      // direction: Axis.horizontal,
                      allowHalfRating: true,
                      itemPadding: const EdgeInsets.symmetric(horizontal: 4.0),
                      itemBuilder: (context, _) => const Icon(
                        Icons.star,
                        color: Colors.amber,
                      ),
                      onRatingUpdate: (rating) {
                        setState(() => _rating = rating);
                      },
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // Comments
                CustomTextField(
                  controller: _commentsController,
                  hintText: 'Share your feedback details...',
                  maxLine: 5,
                  validator: (value) =>
                      value?.isEmpty ?? true ? 'Feedback are required' : null,
                ),

                // App Feedback Section (Conditional)
                if (isAppFeedback) ...[
                  const SizedBox(height: 24),
                  Text('App Feedback Details',
                      style: context.textTheme.titleMedium),

                  const SizedBox(height: 16),

                  // App Feedback Category
                  CustomDropDown<String>(
                    labelText: 'Category',
                    items: _appCategories,
                    displayItem: (item) => item,
                    onChanged: (value) =>
                        setState(() => _appFeedbackCategory = value),
                    validator: (value) => value == null
                        ? 'Please select a category for app feedback'
                        : null,
                  ),

                  const SizedBox(height: 16),
                ],

                const SizedBox(height: 32),

                // Submit Button
                BlocBuilder<UserBloc, UserState>(
                  buildWhen: (previous, current) =>
                      current is FeedbackSending ||
                      current is FeedbackSent ||
                      current is FeedbackFailure,
                  builder: (context, state) {
                    final isSubmitting = state is FeedbackSending;
                    final buttonState =
                        isSubmitting ? ButtonState.loading : ButtonState.normal;
                    return CustomButton(
                      buttonState: buttonState,
                      buttonText: 'Submit Feedback',
                      width: double.infinity,
                      height: 50,
                      onTap: () {
                        if (_formKey.currentState!.validate()) {
                          context.userBloc.add(
                            SendFeedbackEvent(
                              feedbackType: _feedbackType!,
                              rating: _rating,
                              appFeedbackCategory: _appFeedbackCategory,
                              comments: _commentsController.text,
                              appVersion: _appVersion!,
                              deviceInfo: _deviceInfo!,
                            ),
                          );
                        }
                      },
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
