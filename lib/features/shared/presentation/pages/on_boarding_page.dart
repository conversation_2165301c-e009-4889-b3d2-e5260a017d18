// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:hodan_hospital/core/config/router/extension/navigation_extension.dart';
// import 'package:hodan_hospital/core/config/theme/colors/app_colors_extension.dart';
// import 'package:hodan_hospital/core/enums/animation_direction.dart';
// import 'package:hodan_hospital/core/utils/extensions/app_bloc_extensions.dart';
// import 'package:hodan_hospital/core/utils/extensions/build_context_extensions.dart';
// import 'package:hodan_hospital/features/authentication/presentation/bloc/authentication_bloc.dart';
// import 'package:hodan_hospital/features/authentication/presentation/pages/login_page.dart';
// import 'package:hodan_hospital/features/shared/presentation/widgets/animations/animated_item_wrapper.dart';
// import 'package:hodan_hospital/features/shared/presentation/widgets/custom_button.dart';
// import 'package:hodan_hospital/gen/assets.gen.dart';
// import 'package:hodan_hospital/main.dart';
// import 'package:lottie/lottie.dart';
// import 'package:smooth_page_indicator/smooth_page_indicator.dart';

// class OnBoardingPage extends StatefulWidget {
//   const OnBoardingPage({super.key});

//   @override
//   State<OnBoardingPage> createState() => _OnBoardingPageState();
// }

// class _OnBoardingPageState extends State<OnBoardingPage> {
//   List<String> images = [
//     Assets.lottie.boarding2,
//     Assets.lottie.boarding1,
//     Assets.lottie.boarding3,
//   ];
//   // List<String> images = [
//   //   Assets.images.svg.onboarding1.path,
//   //   Assets.images.svg.onboarding2.path,
//   //   Assets.images.svg.onboarding3.path,
//   // ];

//   List<String> titles = [
//     'Welcome To Hodan Hospital',
//     'Your Health, Our Priority',
//     'We are here to help you',
//   ];

//   List<String> descriptions = [
//     // 'Access medical services, track your health, and communicate with our experts all in one place.',
//     "Easily access medical services, track your health, and connect with expert doctors—all in one place. Your well-being starts here.",
//     // 'Experience world-class healthcare with our state-of-the-art facilities and compassionate staff.',
//     "Experience top-quality healthcare with cutting-edge facilities and a dedicated team that puts you first.",
//     // 'Get personalized care and attention from our dedicated team of medical professionals.',
//     "Personalized care, expert support, and a commitment to your health—because you deserve the best."
//   ];

//   final PageController _controller = PageController();
//   int _currentIndex = 0;

//   bool get _isLastPage => _currentIndex == images.length - 1;

//   @override
//   void initState() {
//     super.initState();
//     setStatusBar();
//   }

//   @override
//   void dispose() {
//     _controller.dispose();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     final appColors = context.appColors;
//     final textTheme = context.textTheme;
//     return Scaffold(
//       appBar: AppBar(
//         backgroundColor: appColors.transparentColor,
//         actions: [
//           // Skip Button
//           if (!_isLastPage)
//             AnimatedItemWrapper(
//               delay: const Duration(milliseconds: 300),
//               animationDirection: AnimationDirection.leftToRight,
//               child: CustomButton.inline(
//                 width: 80,
//                 buttonText: "Skip",
//                 textStyle: textTheme.titleLarge?.copyWith(
//                   color: appColors.primaryColor,
//                   fontSize: 18.sp,
//                 ),
//                 onTap: () {
//                   _controller.animateToPage(
//                     images.length - 1,
//                     duration: const Duration(milliseconds: 500),
//                     curve: Curves.easeInOut,
//                   );
//                 },
//               ),
//             ),
//         ],
//       ),
//       body: BlocConsumer<AuthenticationBloc, AuthenticationState>(
//         listener: (context, state) {
//           if (state is OnboardingCompleted) {
//             context.pushAndRemoveUntilRoute(const LoginPage());
//           }
//         },
//         builder: (context, state) {
//           return Container(
//             padding: EdgeInsets.symmetric(horizontal: 20.w),
//             // decoration: const BoxDecoration(
//             //   gradient: LinearGradient(
//             //     colors: [Colors.blueAccent, Colors.lightBlueAccent],
//             //     begin: Alignment.topCenter,
//             //     end: Alignment.bottomCenter,
//             //   ),
//             // ),
//             child: Column(
//               children: [
//                 // ✅ PAGE VIEW (Contains Image, Title, Description)
//                 Expanded(
//                   child: PageView.builder(
//                     itemCount: images.length,
//                     controller: _controller,
//                     onPageChanged: (index) {
//                       setState(() {
//                         _currentIndex = index;
//                       });
//                     },
//                     itemBuilder: (context, index) {
//                       return OnBoardingContent(
//                         imgPath: images[index],
//                         title: titles[index],
//                         description: descriptions[index],
//                       );
//                     },
//                   ),
//                 ),

//                 // Page Indicator
//                 AnimatedItemWrapper(
//                   delay: const Duration(milliseconds: 1100),
//                   animationDirection: AnimationDirection.bottomToTop,
//                   child: SmoothPageIndicator(
//                     effect: WormEffect(
//                       // dotColor: context.appColors.disabledColor,
//                       // activeDotColor: context.appColors.whiteColor,
//                       dotHeight: 12.h,
//                       dotWidth: 22.w,
//                       spacing: 12.0,
//                     ),
//                     controller: _controller,
//                     count: images.length,
//                   ),
//                 ),

//                 SizedBox(height: 50.h),

//                 // ✅  BUTTON
//                 AnimatedItemWrapper(
//                   delay: const Duration(milliseconds: 1200),
//                   animationDirection: AnimationDirection.rightToLeft,
//                   child: CustomButton(
//                     buttonText: _isLastPage ? "Get Started" : "Next",
//                     // style: context.textTheme.bodyMedium,
//                     onTap: () {
//                       if (_isLastPage) {
//                         context.authenticationBloc
//                             .add(CompleteOnBoardingEvent());
//                       } else {
//                         _controller.nextPage(
//                           duration: const Duration(milliseconds: 800),
//                           curve: Curves.easeInOut,
//                         );
//                       }
//                     },
//                   ),
//                 ),

//                 // Extra Spacing at Bottom
//                 SizedBox(height: 70.h),
//               ],
//             ),
//           );
//         },
//       ),
//     );
//   }
// }

// class OnBoardingContent extends StatelessWidget {
//   final String imgPath;
//   final String title;
//   final String description;

//   const OnBoardingContent({
//     super.key,
//     required this.imgPath,
//     required this.title,
//     required this.description,
//   });

//   @override
//   Widget build(BuildContext context) {
//     final textTheme = context.textTheme;
//     return Column(
//       mainAxisAlignment: MainAxisAlignment.center,
//       // crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         const Spacer(),

//         /// Image
//         AnimatedItemWrapper(
//           delay: const Duration(milliseconds: 500),
//           animationDirection: AnimationDirection.topToBottom,
//           child: Center(
//             child: SizedBox(
//               height: 200.h,
//               width: 250.w,
//               child: Lottie.asset(
//                 imgPath,
//                 fit: BoxFit.fill,
//                 errorBuilder: (context, error, stackTrace) {
//                   return const Icon(Icons.image, size: 50, color: Colors.red);
//                 },
//                 frameRate: FrameRate.max,
//               ),
//             ),
//           ),
//         ),

//         SizedBox(height: 30.h),

//         // Title
//         AnimatedItemWrapper(
//           delay: const Duration(milliseconds: 700),
//           animationDirection: AnimationDirection.leftToRight,
//           child: Text(
//             title,
//             style: textTheme.titleLarge?.copyWith(
//               color: context.appColors.primaryColor,
//             ),
//             textAlign: TextAlign.center,
//           ),
//         ),

//         SizedBox(height: 20.h),

//         // Description
//         AnimatedItemWrapper(
//           delay: const Duration(milliseconds: 900),
//           animationDirection: AnimationDirection.rightToLeft,
//           child: Container(
//             width: double.infinity,
//             margin: EdgeInsets.only(left: 20.w),
//             child: Text(
//               description,
//               style: textTheme.bodyMedium,
//               textAlign: TextAlign.start,
//             ),
//           ),
//         ),

//         const Spacer(),
//       ],
//     );
//   }
// }
