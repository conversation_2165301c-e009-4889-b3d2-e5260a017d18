import 'package:flutter/services.dart';

import '../../../../core/errors/app_failure.dart';

abstract class SharedRepository {
  // FutureEitherFailOr<String> processAppointmentBarCode({
  // required String queName,
  // });

  FutureEitherFailOr<String> downloadPDF({
    required Uint8List pdfBytes,
  });

  FutureEitherFailOr<Uint8List> generatePDF({
    required String id,
    required String doctype,
    required String format,
  });

  FutureEitherFailOr<String> sendSMS({
    required String mobileNumber,
    required String message,
  });
}
