import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/core/use_cases/use_case.dart';
import 'package:hodan_hospital/features/shared/domain/params/download_pdf_params.dart';
import 'package:hodan_hospital/features/shared/domain/repositories/shared_repository.dart';

// ✅ Concrete Use Case for downloading pdf
class DownloadPdfUseCase implements UseCase<String, DownloadPdfParams> {
  final SharedRepository repository;

  DownloadPdfUseCase({required this.repository});

  @override
  FutureEitherFailOr<String> call({
    required DownloadPdfParams params,
  }) {
    return repository.downloadPDF(pdfBytes: params.pdfBytes);
  }
}
