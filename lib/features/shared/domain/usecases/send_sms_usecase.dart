
import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/core/use_cases/use_case.dart';
import 'package:hodan_hospital/features/shared/domain/params/send_sms_params.dart';
import 'package:hodan_hospital/features/shared/domain/repositories/shared_repository.dart';

// ✅ Concrete Use Case for sending sms
class SendSMSUseCase implements UseCase<String, SendSMSParams> {
  final SharedRepository repository;

  SendSMSUseCase({required this.repository});

  @override
  FutureEitherFailOr<String> call({
    required SendSMSParams params,
  }) {
    return repository.sendSMS(
      mobileNumber: params.mobileNumber,
      message: params.message,
    );
  }
}
