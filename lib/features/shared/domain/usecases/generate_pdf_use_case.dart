import 'package:flutter/services.dart';
import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/core/use_cases/use_case.dart';
import 'package:hodan_hospital/features/shared/domain/params/generate_pdf_params.dart';
import 'package:hodan_hospital/features/shared/domain/repositories/shared_repository.dart';

// ✅ Concrete Use Case for generating pdf
class GeneratePdfUseCase implements UseCase<Uint8List, GeneratePdfParams> {
  final SharedRepository repository;

  GeneratePdfUseCase({required this.repository});

  @override
  FutureEitherFailOr<Uint8List> call({
    required GeneratePdfParams params,
  }) {
    return repository.generatePDF(
      id: params.id,
      doctype: params.doctype,
      format: params.format,
    );
  }
}
