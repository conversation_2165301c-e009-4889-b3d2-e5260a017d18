class UserEntity {
  final String pID;
  final String fullName;
  final String phoneNumber;
  final double age;
  final String district;
  final String gender;
  final String? profileImage;

  UserEntity({
    required this.pID,
    required this.fullName,
    required this.phoneNumber,
    required this.age,
    required this.district,
    required this.gender,
    this.profileImage,
  });

  @override
  String toString() {
    return 'UserEntity{pID: $pID, fullName: $fullName, phoneNumber: $phoneNumber, age: $age, district: $district, gender: $gender, profileImage: $profileImage}';
  }
}
