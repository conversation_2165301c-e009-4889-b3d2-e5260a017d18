import 'package:hodan_hospital/core/enums/parse_failure_type.dart';
import 'package:hodan_hospital/core/errors/app_failure.dart';
// import 'package:objectbox/objectbox.dart';

// @Entity()
class UserModel {
  // @Id()
  int id = 0;

  // @Index()
  final String pID;
  final String fullName;
  // @Index()
  final String phoneNumber;
  final double age;
  final String district;
  final String gender;
  final String? profileImage;

  UserModel({
    required this.pID,
    required this.fullName,
    required this.phoneNumber,
    required this.age,
    required this.district,
    required this.gender,
    this.profileImage,
  });

  factory UserModel.fromJson(Map<String, dynamic> map) {
    try {
      return UserModel(
        pID: map['patient_id'] ?? '',
        fullName:
            map['first_name'] ?? map['full_name'] ?? '',
        phoneNumber: map['mobile'] ?? '',
        district: map['district'] ?? '',
        gender: map['Gender'] ?? '',
        age: (map['age'] as num?)?.toDouble() ?? 0,
        profileImage: map['image'] ?? '',
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: ' Failed to parse UserModel : ${error.toString()} ',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: UserModel,
        stackTrace: stackTrace,
      );
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'pID': pID,
      'full_name': fullName,
      'mobile': phoneNumber,
      'age': age,
      'district': district,
      'gender': gender,
      'image': profileImage,
    };
  }

  @override
  String toString() {
    return 'UserModel(pID: $pID, fullName: $fullName, phoneNumber: $phoneNumber, age: $age, district: $district, gender: $gender, profileImage: $profileImage)';
  }
}
