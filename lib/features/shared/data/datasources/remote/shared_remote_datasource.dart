// ignore_for_file: unnecessary_null_comparison

import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/services.dart';
import 'package:fpdart/fpdart.dart';
import 'package:hodan_hospital/core/config/logger/app_logger.dart';
import 'package:hodan_hospital/core/constants/api_end_points.dart';
import 'package:hodan_hospital/core/enums/cache_failure_type.dart';
import 'package:hodan_hospital/core/enums/http_method.dart';
import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/core/errors/http_error_handler.dart';
import 'package:hodan_hospital/core/network/api_client/dio_api_client.dart';
import 'package:hodan_hospital/core/services/flutter_secure_storage_services.dart';

import '../../../../../core/config/enviroment/enviroment_config.dart';
import '../../../../../core/enums/http_failure_type.dart';
import '../../../../../core/utils/helpers/request_data.dart';

abstract class SharedRemoteDatasource {
  // FutureEitherFailOr<String> processAppointmentBarCode({
  // required String queName,
  // });

  FutureEitherFailOr<String> downloadPDF({
    required Uint8List pdfBytes,
  });

  FutureEitherFailOr<Uint8List> generatePDF({
    required String id,
    required String doctype,
    required String format,
  });

  FutureEitherFailOr<String> sendSMS({
    required String mobileNumber,
    required String message,
  });
}

class SharedRemoteDatasourceImpl implements SharedRemoteDatasource {
  final DioApiClient dioApiClient;
  final HttpErrorHandler httpErrorHandler;
  final FlutterSecureStorageServices flutterSecureStorageServices;

  const SharedRemoteDatasourceImpl({
    required this.dioApiClient,
    required this.httpErrorHandler,
    required this.flutterSecureStorageServices,
  });

  // @override
  // FutureEitherFailOr<String> processAppointmentBarCode({
  //   required String queName,
  // }) async {
  //   final response = await httpErrorHandler.handleRequest<String>(
  //     requestFunction: () => dioApiClient.request(
  //       method: HttpMethod.get,
  //       endPointUrl: ApiEndpoints.processAppointmentBarCode,
  //       queryParameters: {
  //         'que_name': queName,
  //       },
  //     ),
  //   );

  //   return ResponseHandler<String>(response).handleResponse(
  //     onFailure: (failure) => left(failure),
  //     onSuccess: (data) => right(data.apiMessage),
  //   );
  // }

  @override
  FutureEitherFailOr<Uint8List> generatePDF({
    required String id,
    required String doctype,
    required String format,
  }) async {
    final response = await httpErrorHandler.handleRequestBytes(
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.get,
        endPointUrl: ApiEndpoints.getPDF,
        responseType: ResponseType.bytes,
        queryParameters: {
          'doctype': doctype,
          'format': format,
          'name': id,
        },
      ),
    );

    return response.fold(
      (failure) => left(failure),
      (bytes) {
        return right(bytes);
      },
    );
  }

  @override
  FutureEitherFailOr<String> downloadPDF({
    required Uint8List pdfBytes,
  }) async {
    try {
      // Define the download path
      final time = DateTime.now().millisecondsSinceEpoch;
      final path = '/storage/emulated/0/Download/appointment_$time.pdf';

      // Save the PDF file
      final file = File(path);
      await file.writeAsBytes(pdfBytes);

      // Log and show success message
      AppLogger().info('✅ PDF saved at: $path');

      return right(path);
    } catch (error, stackTrace) {
      AppLogger().error(
        'Error downloading PDF : $error',
        error: error,
        stackTrace: stackTrace,
      );
      return left(CacheFailure(
        message: 'Error initializing file download : $error',
        failureType: CacheFailureType.unknown,
        stackTrace: stackTrace,
      ));
    }
  }

  ///----------  SMS Related

  // get access token
  FutureEitherFailOr<String> _getAccessToken() async {
    try {
      final tokenJson = await flutterSecureStorageServices.readData(
        key: EnvironmentConfig.smsCacheKey,
      );

      if (tokenJson != null) {
        final tokenMap = jsonDecode(tokenJson);
        final token = tokenMap['access_token'];
        final expiresAt = DateTime.tryParse(tokenMap['expires_at'] ?? '');
        if (token != null &&
            expiresAt != null &&
            DateTime.now().isBefore(expiresAt)) {
          return right(token);
        }
      }

      final response = await dioApiClient.request(
          method: HttpMethod.post,
          endPointUrl: ApiEndpoints.getAccessToken,
          data: RequestData.json({
            'grant_type': 'password',
            'username': EnvironmentConfig.smsUsername,
            'password': EnvironmentConfig.smsPassword,
          }),
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          });

      if (response.data is Map<String, dynamic>) {
        final responseData = response.data as Map<String, dynamic>;
        final accessToken = responseData['access_token'];
        final responseCode = response.statusCode;
        if (responseCode != 200) {
          return left(HttpFailure(
            message: 'Failed to get access token',
            failureType: HttpFailureType.serverError,
            apiMessage:
                'Failed to get access token, status code: $responseCode',
            statusCode: responseCode,
          ));
        }

        if (accessToken != null) {
          await flutterSecureStorageServices.storeData(
            key: EnvironmentConfig.smsCacheKey,
            value: jsonEncode({
              'access_token': accessToken,
              'expires_at': DateTime.now()
                  .add(const Duration(minutes: 55))
                  .toIso8601String(), // Safe margin
            }),
          );
          return right(accessToken);
        }
      }

      return left(HttpFailure(
        message: 'Failed to get access token',
        failureType: HttpFailureType.serverError,
        apiMessage: response.data['error_description']?.toString(),
      ));
    } catch (e, s) {
      AppLogger().error(
        'Error getting access token : $e',
        error: e,
        stackTrace: s,
      );
      return left(HttpFailure(
        message: 'Failed to get access token : $e',
        failureType: HttpFailureType.serverError,
      ));
    }
  }

  @override
  FutureEitherFailOr<String> sendSMS({
    required String mobileNumber,
    required String message,
  }) async {
    try {
      // Step 1: Get access token (cached or fresh)
      final tokenResult = await _getAccessToken();

      return await tokenResult.fold(
        (failure) {
          AppLogger().error(
            '❌ Failed to retrieve access token: ${failure.getErrorMessage()}',
          );
          return left(failure);
        },
        (accessToken) async {
          // Step 2: Prepare SMS request
          final response = await dioApiClient.request(
            method: HttpMethod.post,
            endPointUrl: ApiEndpoints.sendSMS,
            headers: {
              'Authorization': 'Bearer $accessToken',
              'Content-Type': 'application/json',
            },
            data: RequestData.json({
              'refid': 'flutter-${DateTime.now().millisecondsSinceEpoch}',
              'mobile': mobileNumber,
              'message': message,
              'senderid': EnvironmentConfig.smsSenderId,
              'validity': 5,
            }),
          );

          // Step 3: Validate response
          if (response.data is Map<String, dynamic>) {
            final responseData = response.data as Map<String, dynamic>;
            final rawResponseCode = responseData['ResponseCode'];
            final responseCode =
                int.tryParse(rawResponseCode.toString()) ?? 500;
            final description =
                responseData['ResponseMessage'] ?? 'Message sent';

            if (responseCode == 200) {
              AppLogger().info(
                '📨 SMS sent to $mobileNumber: $description',
              );
              return right(description.toString());
            } else {
              AppLogger().error(
                '❌ SMS failed [API Error]: $description',
              );
              return left(HttpFailure(
                message: 'Failed to send SMS',
                failureType: HttpFailureType.serverError,
                apiMessage: description.toString(),
                statusCode: responseCode,
              ));
            }
          }

          // Step 4: Handle unexpected response
          AppLogger().error(
            '❌ SMS failed: Unexpected response format: ${response.data}',
          );
          return left(HttpFailure(
            message: 'Failed to send SMS: Unexpected response format',
            failureType: HttpFailureType.unknown,
          ));
        },
      );
    } catch (e, s) {
      AppLogger().error(
        '❌ SMS sending exception: $e',
        error: e,
        stackTrace: s,
      );
      return left(HttpFailure(
        message: 'Failed to send SMS: $e',
        failureType: HttpFailureType.serverError,
        stackTrace: s,
      ));
    }
  }
}
