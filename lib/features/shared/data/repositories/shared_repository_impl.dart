import 'package:flutter/services.dart';
import 'package:hodan_hospital/features/shared/data/datasources/remote/shared_remote_datasource.dart';

import '../../../../core/errors/app_failure.dart';
import '../../domain/repositories/shared_repository.dart';

class SharedRepositoryImpl implements SharedRepository {
  final SharedRemoteDatasource sharedRemoteDatasource;

  const SharedRepositoryImpl({required this.sharedRemoteDatasource});

  // FutureEitherFailOr<String> processAppointmentBarCode({
  // required String queName,
  // });

  @override
  FutureEitherFailOr<String> downloadPDF({
    required Uint8List pdfBytes,
  }) async {
    return await sharedRemoteDatasource.downloadPDF(pdfBytes: pdfBytes);
  }

  @override
  FutureEitherFailOr<Uint8List> generatePDF({
    required String id,
    required String doctype,
    required String format,
  }) async {
    return await sharedRemoteDatasource.generatePDF(
      id: id,
      doctype: doctype,
      format: format,
    );
  }

  @override
  FutureEitherFailOr<String> sendSMS({
    required String mobileNumber,
    required String message,
  }) async {
    return await sharedRemoteDatasource.sendSMS(
      mobileNumber: mobileNumber,
      message: message,
    );
  }
}
