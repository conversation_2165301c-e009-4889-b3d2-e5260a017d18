import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hodan_hospital/core/config/di/dependency_injection.dart';
import 'package:hodan_hospital/features/Order/presentation/bloc/order_bloc.dart';
import 'package:hodan_hospital/features/Result/presentation/bloc/result_bloc.dart';
import 'package:hodan_hospital/features/appointment/presentation/bloc/appointment_bloc.dart';
import 'package:hodan_hospital/features/authentication/presentation/bloc/authentication_bloc.dart';
import 'package:hodan_hospital/core/config/theme/app_theme.dart';
import 'package:hodan_hospital/features/doctor/presentation/bloc/doctor_bloc.dart';
import 'package:hodan_hospital/features/shared/presentation/blocs%20or%20cubits/bottom%20nav%20cubit/bottom_nav_cubit.dart';
import 'package:hodan_hospital/features/shared/presentation/blocs%20or%20cubits/dialog%20cubit/dialog_cubit.dart';
import 'package:hodan_hospital/features/shared/presentation/blocs%20or%20cubits/shared%20bloc/shared_bloc.dart';
import 'package:hodan_hospital/features/shared/presentation/blocs%20or%20cubits/theme-cubit/theme_cubit.dart';
import 'package:hodan_hospital/features/shared/presentation/pages/splash_page.dart';
import 'package:hodan_hospital/features/user/presentation/bloc/user_bloc.dart';

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) =>
              sl<AuthenticationBloc>()..add(CheckUserAuthenticationEvent()),
        ),
        BlocProvider(
          create: (context) => sl<UserBloc>()..add(const GetCurrentUserEvent()),
        ),
        BlocProvider(
          create: (context) => sl<AppointmentBloc>(),
        ),
        BlocProvider(
          create: (context) => sl<DoctorBloc>(),
        ),
        BlocProvider(
          create: (context) => sl<OrderBloc>(),
        ),
        BlocProvider(
          create: (context) => sl<ResultBloc>(),
        ),
        BlocProvider(
          create: (context) => sl<DialogCubit>(),
        ),
        BlocProvider(
          create: (context) => sl<BottomNavCubit>(),
        ),
        BlocProvider(
          create: (context) => sl<ThemeCubit>(),
        ),
        BlocProvider(
          create: (context) => sl<SharedBloc>(),
        ),
      ],
      child: ScreenUtilInit(
        designSize: const Size(375, 812),
        minTextAdapt: true,
        splitScreenMode: true,
        builder: (context, child) {
          return BlocBuilder<ThemeCubit, ThemeMode>(
            builder: (context, themeMode) {
              return MaterialApp(
                debugShowCheckedModeBanner: false,
                title: 'Hodan Hospital',
                theme: AppTheme.light,
                darkTheme: AppTheme.dark,
                // themeMode: ThemeMode.light,
                themeMode: themeMode,
                home: const SplashPage(),
              );
            },
          );
        },
      ),
    );
  }
}
