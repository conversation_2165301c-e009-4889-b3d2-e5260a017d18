// import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:hodan_hospital/app.dart';
import 'package:hodan_hospital/core/config/di/dependency_injection.dart' as di;
import 'package:hodan_hospital/core/config/enviroment/enviroment_config.dart';
import 'package:hodan_hospital/core/config/logger/app_logger.dart';

// import 'core/network/ssl validation/app_http_overrides.dart';
import 'features/shared/presentation/pages/modern_error_page.dart';
// import 'package:hodan_hospital/core/network/ssl%20validation/app_http_overrides.dart';
// import 'package:package_info_plus/package_info_plus.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  /// Handle errors gracefully
  ErrorWidget.builder =
      (FlutterErrorDetails details) => ModernErrorPage(details: details);

  FlutterError.onError = (FlutterErrorDetails details) {
    // Log the error to console or a logging service
    // print('Flutter Error: ${details.exceptionAsString()}');
  };
  // PlatformDispatcher.instance.onError = (Object error, StackTrace stack) {
  //   // Log the error to console or a logging service
  //   print('Platform Error: $error');
  //   return true; // Prevents the default error handling
  // };

  /// setup custom SSL validation
  // HttpOverrides.global = AppHttpOverrides();

  /// setup logger
  final AppLogger appLogger = AppLogger();
  await appLogger.setupLogging();

  /// Load environment config
  await EnvironmentConfig.loadEnv();

  /// Registering dependencies
  await di.setupServiceLocator();

  /// get package info
  // PackageInfo packageInfo = await PackageInfo.fromPlatform();
  // print('Package Name: ${packageInfo.appName}');
  // print('Package Version: ${packageInfo.version}');
  // print('Package Build Number: ${packageInfo.buildNumber}');
  // appLogger.info('Package Build Signature: ${packageInfo.buildSignature}');

  ///
  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  /// Run the app
  runApp(const MyApp());

  /*
     /// my privacy policy url
     https://www.freeprivacypolicy.com/live/************************************

  */
}

/*

 //! some shorebird command
 To create a patch for this release, run
 shorebird patch --platforms=android --release-version=1.0.2+5

 Note: without the --release-version option will patch the current version of the app.
 shorebird patch --platforms=android


 //
 shorebird patch --platforms=android --release-version=1.0.3+6

  // patch for staging
  shorebird patch android --track=staging

  // preview
  shorebird preview --staging --app-id d54a87ef-28ef-4d2c-9eb8-b21ae422df98 --release-version 1.0.3+7

  // promote to stable
  shorebird promote --track=staging --to=stable



*/
