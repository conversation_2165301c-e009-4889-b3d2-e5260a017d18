/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: directives_ordering,unnecessary_import,implicit_dynamic_list_literal,deprecated_member_use

import 'package:flutter/widgets.dart';

class $AssetsIconGen {
  const $AssetsIconGen();

  /// File path: assets/icon/app_icon.png
  AssetGenImage get appIcon => const AssetGenImage('assets/icon/app_icon.png');

  /// File path: assets/icon/app_icon_original.png
  AssetGenImage get appIconOriginal =>
      const AssetGenImage('assets/icon/app_icon_original.png');

  /// File path: assets/icon/app_logo.png
  AssetGenImage get appLogo => const AssetGenImage('assets/icon/app_logo.png');

  /// List of all assets
  List<AssetGenImage> get values => [appIcon, appIconOriginal, appLogo];
}

class $AssetsImagesGen {
  const $AssetsImagesGen();

  /// Directory path: assets/images/jpeg
  $AssetsImagesJpegGen get jpeg => const $AssetsImagesJpegGen();

  /// Directory path: assets/images/json
  $AssetsImagesJsonGen get json => const $AssetsImagesJsonGen();

  /// Directory path: assets/images/png
  $AssetsImagesPngGen get png => const $AssetsImagesPngGen();

  /// Directory path: assets/images/svg
  $AssetsImagesSvgGen get svg => const $AssetsImagesSvgGen();
}

class $AssetsImagesJpegGen {
  const $AssetsImagesJpegGen();

  /// File path: assets/images/jpeg/dentist_services.jpeg
  AssetGenImage get dentistServices =>
      const AssetGenImage('assets/images/jpeg/dentist_services.jpeg');

  /// File path: assets/images/jpeg/imaging_services.jpeg
  AssetGenImage get imagingServices =>
      const AssetGenImage('assets/images/jpeg/imaging_services.jpeg');

  /// File path: assets/images/jpeg/lab_services.jpeg
  AssetGenImage get labServices =>
      const AssetGenImage('assets/images/jpeg/lab_services.jpeg');

  /// File path: assets/images/jpeg/maternity_services.jpeg
  AssetGenImage get maternityServices =>
      const AssetGenImage('assets/images/jpeg/maternity_services.jpeg');

  /// File path: assets/images/jpeg/orhtopedics_services.jpeg
  AssetGenImage get orhtopedicsServices =>
      const AssetGenImage('assets/images/jpeg/orhtopedics_services.jpeg');

  /// File path: assets/images/jpeg/pharmacy_services.jpeg
  AssetGenImage get pharmacyServices =>
      const AssetGenImage('assets/images/jpeg/pharmacy_services.jpeg');

  /// List of all assets
  List<AssetGenImage> get values => [
        dentistServices,
        imagingServices,
        labServices,
        maternityServices,
        orhtopedicsServices,
        pharmacyServices,
      ];
}

class $AssetsImagesJsonGen {
  const $AssetsImagesJsonGen();

  /// File path: assets/images/json/confirm.json
  String get confirm => 'assets/images/json/confirm.json';

  /// File path: assets/images/json/error.json
  String get error => 'assets/images/json/error.json';

  /// File path: assets/images/json/info.json
  String get info => 'assets/images/json/info.json';

  /// File path: assets/images/json/loading.json
  String get loading => 'assets/images/json/loading.json';

  /// File path: assets/images/json/success.json
  String get success => 'assets/images/json/success.json';

  /// File path: assets/images/json/warning.json
  String get warning => 'assets/images/json/warning.json';

  /// List of all assets
  List<String> get values => [confirm, error, info, loading, success, warning];
}

class $AssetsImagesPngGen {
  const $AssetsImagesPngGen();

  /// File path: assets/images/png/antenatal_package.png
  AssetGenImage get antenatalPackage =>
      const AssetGenImage('assets/images/png/antenatal_package.png');

  /// File path: assets/images/png/antental_package_detail.png
  AssetGenImage get antentalPackageDetail =>
      const AssetGenImage('assets/images/png/antental_package_detail.png');

  /// File path: assets/images/png/app_logo.png
  AssetGenImage get appLogo =>
      const AssetGenImage('assets/images/png/app_logo.png');

  /// File path: assets/images/png/appointment.png
  AssetGenImage get appointment =>
      const AssetGenImage('assets/images/png/appointment.png');

  /// File path: assets/images/png/banner1.jpg
  AssetGenImage get banner1 =>
      const AssetGenImage('assets/images/png/banner1.jpg');

  /// File path: assets/images/png/banner2.png
  AssetGenImage get banner2 =>
      const AssetGenImage('assets/images/png/banner2.png');

  /// File path: assets/images/png/banner3.jpeg
  AssetGenImage get banner3 =>
      const AssetGenImage('assets/images/png/banner3.jpeg');

  /// File path: assets/images/png/cardic_package.png
  AssetGenImage get cardicPackage =>
      const AssetGenImage('assets/images/png/cardic_package.png');

  /// File path: assets/images/png/cardic_package_detail.png
  AssetGenImage get cardicPackageDetail =>
      const AssetGenImage('assets/images/png/cardic_package_detail.png');

  /// File path: assets/images/png/doctor.png
  AssetGenImage get doctor =>
      const AssetGenImage('assets/images/png/doctor.png');

  /// File path: assets/images/png/error.png
  AssetGenImage get error => const AssetGenImage('assets/images/png/error.png');

  /// File path: assets/images/png/health_check_up_female.png
  AssetGenImage get healthCheckUpFemale =>
      const AssetGenImage('assets/images/png/health_check_up_female.png');

  /// File path: assets/images/png/health_check_up_female_detail.png
  AssetGenImage get healthCheckUpFemaleDetail => const AssetGenImage(
        'assets/images/png/health_check_up_female_detail.png',
      );

  /// File path: assets/images/png/health_check_up_male.png
  AssetGenImage get healthCheckUpMale =>
      const AssetGenImage('assets/images/png/health_check_up_male.png');

  /// File path: assets/images/png/health_check_up_male_detail.png
  AssetGenImage get healthCheckUpMaleDetail =>
      const AssetGenImage('assets/images/png/health_check_up_male_detail.png');

  /// File path: assets/images/png/hodan_login_cover.png
  AssetGenImage get hodanLoginCover =>
      const AssetGenImage('assets/images/png/hodan_login_cover.png');

  /// File path: assets/images/png/hyperthensis_patients_detail.png
  AssetGenImage get hyperthensisPatientsDetail =>
      const AssetGenImage('assets/images/png/hyperthensis_patients_detail.png');

  /// File path: assets/images/png/hyperthensis_patients_package.png
  AssetGenImage get hyperthensisPatientsPackage => const AssetGenImage(
        'assets/images/png/hyperthensis_patients_package.png',
      );

  /// File path: assets/images/png/lab_result.png
  AssetGenImage get labResult =>
      const AssetGenImage('assets/images/png/lab_result.png');

  /// File path: assets/images/png/login_cover.jpeg
  AssetGenImage get loginCover =>
      const AssetGenImage('assets/images/png/login_cover.jpeg');

  /// File path: assets/images/png/logo_white_text.png
  AssetGenImage get logoWhiteText =>
      const AssetGenImage('assets/images/png/logo_white_text.png');

  /// File path: assets/images/png/order.png
  AssetGenImage get order => const AssetGenImage('assets/images/png/order.png');

  /// File path: assets/images/png/package.png
  AssetGenImage get package =>
      const AssetGenImage('assets/images/png/package.png');

  /// File path: assets/images/png/profile.png
  AssetGenImage get profile =>
      const AssetGenImage('assets/images/png/profile.png');

  /// File path: assets/images/png/sugar_package.png
  AssetGenImage get sugarPackage =>
      const AssetGenImage('assets/images/png/sugar_package.png');

  /// File path: assets/images/png/sugar_package_detail.png
  AssetGenImage get sugarPackageDetail =>
      const AssetGenImage('assets/images/png/sugar_package_detail.png');

  /// List of all assets
  List<AssetGenImage> get values => [
        antenatalPackage,
        antentalPackageDetail,
        appLogo,
        appointment,
        banner1,
        banner2,
        banner3,
        cardicPackage,
        cardicPackageDetail,
        doctor,
        error,
        healthCheckUpFemale,
        healthCheckUpFemaleDetail,
        healthCheckUpMale,
        healthCheckUpMaleDetail,
        hodanLoginCover,
        hyperthensisPatientsDetail,
        hyperthensisPatientsPackage,
        labResult,
        loginCover,
        logoWhiteText,
        order,
        package,
        profile,
        sugarPackage,
        sugarPackageDetail,
      ];
}

class $AssetsImagesSvgGen {
  const $AssetsImagesSvgGen();

  /// File path: assets/images/svg/calender.svg
  String get calender => 'assets/images/svg/calender.svg';

  /// File path: assets/images/svg/doctor.svg
  String get doctor => 'assets/images/svg/doctor.svg';

  /// File path: assets/images/svg/home.svg
  String get home => 'assets/images/svg/home.svg';

  /// File path: assets/images/svg/login_rounded_left_shape.svg
  String get loginRoundedLeftShape =>
      'assets/images/svg/login_rounded_left_shape.svg';

  /// File path: assets/images/svg/login_rounded_right_shape.svg
  String get loginRoundedRightShape =>
      'assets/images/svg/login_rounded_right_shape.svg';

  /// File path: assets/images/svg/onboarding1.svg
  String get onboarding1 => 'assets/images/svg/onboarding1.svg';

  /// File path: assets/images/svg/onboarding2.svg
  String get onboarding2 => 'assets/images/svg/onboarding2.svg';

  /// File path: assets/images/svg/onboarding3.svg
  String get onboarding3 => 'assets/images/svg/onboarding3.svg';

  /// File path: assets/images/svg/person.svg
  String get person => 'assets/images/svg/person.svg';

  /// List of all assets
  List<String> get values => [
        calender,
        doctor,
        home,
        loginRoundedLeftShape,
        loginRoundedRightShape,
        onboarding1,
        onboarding2,
        onboarding3,
        person,
      ];
}

class Assets {
  const Assets._();

  static const String aEnv = '.env.development';
  // static const String aEnv = '.env.production';
  static const $AssetsIconGen icon = $AssetsIconGen();
  static const $AssetsImagesGen images = $AssetsImagesGen();

  /// List of all assets
  static List<String> get values => [aEnv, aEnv];
}

class AssetGenImage {
  const AssetGenImage(this._assetName, {this.size, this.flavors = const {}});

  final String _assetName;

  final Size? size;
  final Set<String> flavors;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = true,
    bool isAntiAlias = false,
    String? package,
    FilterQuality filterQuality = FilterQuality.medium,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({AssetBundle? bundle, String? package}) {
    return AssetImage(_assetName, bundle: bundle, package: package);
  }

  String get path => _assetName;

  String get keyName => _assetName;
}
