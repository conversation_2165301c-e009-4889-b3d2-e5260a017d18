// ignore_for_file: library_private_types_in_public_api

import 'package:dio/dio.dart';

abstract class RequestData {
  /// Creates a JSON request.
  static _JsonRequest json(Map<String, dynamic> json) {
    return _JsonRequest(json: json);
  }

  /// Creates a FormData request.
  static _FormDataRequest formData(FormData formData) {
    return _FormDataRequest(formData: formData);
  }

  /// Creates an Empty request.
  static _EmptyRequest empty() {
    return _EmptyRequest();
  }

  /// Returns the content type of the request data.
  String get contentType;

  /// Returs the data of the request that dio accepts.
  dynamic get toHttpPayload;

  @override
  String toString() {
    return 'RequestData(contentType: $contentType, toHttpPayload: $toHttpPayload)';
  }
}

class _JsonRequest extends RequestData {
  final Map<String, dynamic> json;

  _JsonRequest({required this.json});

  @override
  String get contentType => 'application/json';

  @override
  String toString() {
    return 'JsonRequest(json: $json, contentType: $contentType)';
  }

  @override
  dynamic get toHttpPayload => json;
}

class _FormDataRequest extends RequestData {
  final FormData formData;

  _FormDataRequest({required this.formData});

  @override
  String get contentType => 'multipart/form-data';

  @override
  String toString() {
    return 'FormDataRequest(formData: $formData, contentType: $contentType)';
  }

  @override
  dynamic get toHttpPayload => formData;
}

class _EmptyRequest extends RequestData {
  @override
  String get contentType => 'text/plain';

  @override
  String toString() {
    return 'EmptyRequest(contentType: $contentType)';
  }

  @override
  dynamic get toHttpPayload => null;
}
