import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:hodan_hospital/core/errors/app_failure.dart';

class BlocHelper {
  /// 🟢 Debounce Helper Method (Prevents rapid event triggering)
  static EventTransformer<E> debounceHelper<E>({
    Duration duration = const Duration(milliseconds: 300),
  }) {
    return (events, mapper) => events.debounce(duration).switchMap(mapper);
  }

  /// 🟢 Generic Event Handling and State Emission
  static Future<void> handleEventAndEmit<T, State>({
    required Emitter<State> emit,
    required State loadingState,
    required FutureEitherFailOr<T> callUseCase,
    required State Function(T) onSuccess,
    required State Function(AppFailure) onFailure,
  }) async {
    // Emit loading state if provided
    emit(loadingState);

    final result = await callUseCase;
    result.fold(
      (failure) => emit(onFailure(failure)),
      (success) => emit(onSuccess(success)),
    );
  }

  /// 🟢 Generic Stream Event Handling and State Emission
  static Stream<State> handleStreamEventAndEmit<T, State>({
    required Emitter<State> emit,
    required State loadingState,
    required StreamEitherFailOr<T> callUseCase,
    required State Function(T) onSuccess,
    required State Function(AppFailure) onFailure,
  }) async* {
    emit(loadingState);
    await for (final result in callUseCase) {
      result.fold(onFailure, onSuccess);
    }
  }
}
