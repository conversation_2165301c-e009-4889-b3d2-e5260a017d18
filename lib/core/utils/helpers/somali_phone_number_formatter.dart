// import 'package:flutter/services.dart';

// class SomaliPhoneNumberFormatter extends TextInputFormatter {
//   @override
//   TextEditingValue formatEditUpdate(
//     TextEditingValue oldValue,
//     TextEditingValue newValue,
//   ) {
//     // Remove all non-digits
//     String digitsOnly = newValue.text.replaceAll(RegExp(r'[^\d]'), '');

//     // Prevent entering leading zero
//     if (digitsOnly.startsWith('0')) {
//       digitsOnly = digitsOnly.substring(1);
//     }

//     // Prevent typing '252' again
//     if (digitsOnly.startsWith('252')) {
//       digitsOnly = digitsOnly.substring(3);
//     }

//     // Limit to max 9 digits after country code (+25261xxxxxx)
//     if (digitsOnly.length > 9) {
//       digitsOnly = digitsOnly.substring(0, 9);
//     }

//     return TextEditingValue(
//       text: digitsOnly,
//       selection: TextSelection.collapsed(offset: digitsOnly.length),
//     );
//   }
// }


import 'package:flutter/services.dart';

class SomaliPhoneNumberFormatter extends TextInputFormatter {
  final bool allowLeadingZero;

  SomaliPhoneNumberFormatter({this.allowLeadingZero = false});

  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    // Remove all non-digits
    String digitsOnly = newValue.text.replaceAll(RegExp(r'[^\d]'), '');

    // Remove country code prefix if typed
    if (digitsOnly.startsWith('252')) {
      digitsOnly = digitsOnly.substring(3);
    }

    // Handle leading zero logic
    if (!allowLeadingZero && digitsOnly.startsWith('0')) {
      digitsOnly = digitsOnly.substring(1);
    }

    // Limit based on mode:
    //  - allowLeadingZero: max 10 (e.g., 061xxxxxxx)
    //  - else: max 9 (e.g., 61xxxxxxx)
    final maxLength = allowLeadingZero ? 10 : 9;
    if (digitsOnly.length > maxLength) {
      digitsOnly = digitsOnly.substring(0, maxLength);
    }

    return TextEditingValue(
      text: digitsOnly,
      selection: TextSelection.collapsed(offset: digitsOnly.length),
    );
  }
}
