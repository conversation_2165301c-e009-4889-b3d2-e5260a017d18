class FormValidationHelper {
  /// ✅ **Checks if a field is empty**
  static String? validateRequiredField({
    required String? value,
    String fieldName = 'Field',
  }) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName is required.';
    }
    return null;
  }

  /// 📧 **Validates Email Format**
  static String? validateEmail({required String? value}) {
    if (value == null || value.trim().isEmpty) {
      return 'Email is required.';
    }
    // Email regex pattern
    const pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$';
    final regex = RegExp(pattern);

    if (!regex.hasMatch(value.trim())) {
      return 'Enter a valid email address.';
    }
    return null;
  }

  /// 📞 **Validates Phone Number**
  static String? validatePhoneNumber({required String? value}) {
    if (value == null || value.trim().isEmpty) {
      return 'Phone number is required.';
    }
    // Supports international format + country codes
    const pattern = r'^\+?[0-9]{9,15}$';
    final regex = RegExp(pattern);

    if (!regex.hasMatch(value.trim())) {
      return 'Enter a valid phone number.';
    }
    return null;
  }

  /// 🔑 **Validates Password Strength** (Customizable)
  static String? validatePassword({
    required String? value,
    int minLength = 8,
    int maxLength = 20,
    bool requireUppercase = true,
    bool requireLowercase = true,
    bool requireNumber = true,
    bool requireSpecialChar = true,
  }) {
    if (value == null || value.trim().isEmpty) {
      return 'Password is required.';
    }
    if (value.length < minLength) {
      return 'Password must be at least $minLength characters.';
    }
    if (value.length > maxLength) {
      return 'Password cannot be more than $maxLength characters.';
    }
    if (requireUppercase && !RegExp(r'[A-Z]').hasMatch(value)) {
      return 'Password must contain at least one uppercase letter.';
    }
    if (requireLowercase && !RegExp(r'[a-z]').hasMatch(value)) {
      return 'Password must contain at least one lowercase letter.';
    }
    if (requireNumber && !RegExp(r'[0-9]').hasMatch(value)) {
      return 'Password must contain at least one number.';
    }
    if (requireSpecialChar &&
        !RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(value)) {
      return 'Password must contain at least one special character.';
    }
    return null;
  }

  /// 👤 **Validates Name (Only Letters)**
  static String? validateName({
    required String? value,
    String fieldName = 'Name',
  }) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName is required.';
    }
    if (!RegExp(r'^[a-zA-Z\s]+$').hasMatch(value.trim())) {
      return '$fieldName must contain only letters.';
    }
    return null;
  }

  /// 🔢 **Validates Number Fields**
  static String? validateNumber({
    required String? value,
    String fieldName = 'Number',
  }) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName is required.';
    }
    if (!RegExp(r'^\d+$').hasMatch(value.trim())) {
      return '$fieldName must be a valid number.';
    }
    return null;
  }

  /// 🔁 **Confirm Password Validation**
  static String? validateConfirmPassword({
    required String? value,
    required String password,
  }) {
    if (value == null || value.trim().isEmpty) {
      return 'Please confirm your password.';
    }
    if (value != password) {
      return 'Passwords do not match.';
    }
    return null;
  }

  /// 📆 **Validates Date Input (YYYY-MM-DD)**
  static String? validateDate({required String? value}) {
    if (value == null || value.trim().isEmpty) {
      return 'Date is required.';
    }
    // Matches formats like 2024-02-01
    if (!RegExp(r'^\d{4}-\d{2}-\d{2}$').hasMatch(value.trim())) {
      return 'Enter a valid date (YYYY-MM-DD).';
    }
    return null;
  }

  /// 🎂 **Age Validation (Min & Max Age)**
  static String? validateAge({
    required String? value,
    int minAge = 1,
    int maxAge = 120,
  }) {
    if (value == null || value.trim().isEmpty) {
      return 'Age is required.';
    }
    final int? age = int.tryParse(value.trim());
    if (age == null) {
      return 'Enter a valid age.';
    }
    if (age < minAge) {
      return 'Age must be at least $minAge.';
    }
    if (age > maxAge) {
      return 'Age cannot be more than $maxAge.';
    }
    return null;
  }
}
