import 'package:flutter/material.dart';
import 'package:another_flushbar/flushbar.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hodan_hospital/core/config/theme/colors/app_colors_extension.dart';

/// later i will use material banner instead of flushbar inshaallah
class SnackBarHelper {
  /// ✅ Show Success Snackbar
  static void showSuccessSnackBar(
    BuildContext context, {
    required String message,
    String title = 'Success',
    Duration duration = const Duration(seconds: 3),
    FlushbarPosition position = FlushbarPosition.TOP,
    IconData icon = Icons.check_circle_rounded,
    VoidCallback? onTap,
    String? actionLabel,
    VoidCallback? onActionPressed,
    VoidCallback? onDismissed,
    VoidCallback? onShow,
  }) {
    _showFlushbar(
      context: context,
      title: title,
      message: message,
      backgroundColor: context.appColors.successColor,
      icon: icon,
      duration: duration,
      position: position,
      onTap: onTap,
      actionLabel: actionLabel,
      onActionPressed: onActionPressed,
      onDismissed: onDismissed,
      onShow: onShow,
    );
  }

  /// ❌ Show Error Snackbar
  static void showErrorSnackBar(
    BuildContext context, {
    required String message,
    String title = 'Error',
    Duration duration = const Duration(seconds: 4),
    FlushbarPosition position = FlushbarPosition.TOP,
    IconData icon = Icons.error_outline_rounded,
    VoidCallback? onTap,
    String? actionLabel,
    VoidCallback? onActionPressed,
    VoidCallback? onDismissed,
    VoidCallback? onShow,
  }) {
    _showFlushbar(
      context: context,
      title: title,
      message: message,
      backgroundColor: context.appColors.errorColor,
      icon: icon,
      duration: duration,
      position: position,
      onTap: onTap,
      actionLabel: actionLabel,
      onActionPressed: onActionPressed,
      onDismissed: onDismissed,
      onShow: onShow,
    );
  }

  /// ⚠️ Show Warning Snackbar
  static void showWarningSnackBar(
    BuildContext context, {
    required String message,
    String title = 'Warning',
    Duration duration = const Duration(seconds: 4),
    FlushbarPosition position = FlushbarPosition.TOP,
    IconData icon = Icons.warning_amber_rounded,
    VoidCallback? onTap,
    String? actionLabel,
    VoidCallback? onActionPressed,
    VoidCallback? onDismissed,
    VoidCallback? onShow,
  }) {
    _showFlushbar(
      context: context,
      title: title,
      message: message,
      backgroundColor: context.appColors.warningColor,
      icon: icon,
      duration: duration,
      position: position,
      onTap: onTap,
      actionLabel: actionLabel,
      onActionPressed: onActionPressed,
      onDismissed: onDismissed,
      onShow: onShow,
    );
  }

  /// ℹ️ Show Info Snackbar
  static void showInfoSnackBar(
    BuildContext context, {
    required String message,
    String title = 'Info',
    Duration duration = const Duration(seconds: 3),
    FlushbarPosition position = FlushbarPosition.TOP,
    IconData icon = Icons.info_outline_rounded,
    VoidCallback? onTap,
    String? actionLabel,
    VoidCallback? onActionPressed,
    VoidCallback? onDismissed,
    VoidCallback? onShow,
  }) {
    _showFlushbar(
      context: context,
      title: title,
      message: message,
      backgroundColor: context.appColors.infoColor,
      icon: icon,
      duration: duration,
      position: position,
      onTap: onTap,
      actionLabel: actionLabel,
      onActionPressed: onActionPressed,
      onDismissed: onDismissed,
      onShow: onShow,
    );
  }

  /// 🎨 **Universal Snackbar Method**
  static void _showFlushbar({
    required BuildContext context,
    required String title,
    required String message,
    required Color backgroundColor,
    IconData? icon,
    Duration duration = const Duration(seconds: 3),
    FlushbarPosition position = FlushbarPosition.TOP,
    FlushbarDismissDirection dismissDirection =
        FlushbarDismissDirection.HORIZONTAL, // ✅ Allow swipe to dismiss
    String? actionLabel,
    VoidCallback? onActionPressed,
    VoidCallback? onTap,
    VoidCallback? onDismissed,
    VoidCallback? onShow,
  }) {
    if (!context.mounted) {
      return; // ✅ Prevents errors if context is unavailable
    }

    // ✅ Wraps the entire Flushbar inside `WidgetsBinding.instance.addPostFrameCallback`
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Flushbar(
        title: title,
        titleSize: 18.sp,
        message: message.isNotEmpty
            ? message
            : "No Message Found to display the snackbar",
        messageSize: 14.sp,
        backgroundColor: backgroundColor,
        borderRadius: BorderRadius.circular(12.r),
        margin: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
        padding: EdgeInsets.all(16.w),
        flushbarPosition: position,
        dismissDirection: dismissDirection,
        duration: duration,
        animationDuration:
            const Duration(milliseconds: 500), // ✅ Smooth fade-in
        icon: icon != null
            ? Icon(
                icon,
                color: context.appColors.whiteColor,
                size: 28.sp,
              )
            : null,
        shouldIconPulse: false,
        boxShadows: [
          BoxShadow(
            color: context.appColors.blackColor.withValues(alpha: 0.15),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
        onTap: (flushbar) {
          if (onTap != null) onTap();
          flushbar.dismiss();
        },
        onStatusChanged: (status) {
          if (status == FlushbarStatus.SHOWING && onShow != null) {
            onShow();
          }

          if (status == FlushbarStatus.DISMISSED && onDismissed != null) {
            onDismissed();
          }
        },
        mainButton: actionLabel != null
            ? TextButton(
                onPressed: () {
                  if (onActionPressed != null) onActionPressed();
                  if (Navigator.canPop(context)) {
                    Navigator.of(context).pop();
                  }
                },
                child: Text(
                  actionLabel,
                  style: TextStyle(
                    color: context.appColors.whiteColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              )
            : null,
      ).show(context);
    });
  }
}
