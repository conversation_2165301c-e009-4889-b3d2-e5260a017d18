import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hodan_hospital/core/config/theme/colors/app_colors_extension.dart';
import 'package:hodan_hospital/core/enums/button_state.dart';
import 'package:hodan_hospital/core/utils/extensions/app_bloc_extensions.dart';
import 'package:hodan_hospital/core/utils/extensions/build_context_extensions.dart';
import 'package:hodan_hospital/core/utils/helpers/form_validation_helper.dart';
import 'package:hodan_hospital/features/shared/data/models/gender.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_button.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_drop_down.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_textfield.dart';
import 'package:hodan_hospital/features/user/domain/entities/district_entity.dart';
import 'package:hodan_hospital/features/user/presentation/bloc/user_bloc.dart';

class BottomSheetHelper {
  // Singleton instance
  static final BottomSheetHelper instance = BottomSheetHelper._internal();
  BottomSheetHelper._internal();

  /// Shows a polished and properly aligned confirmation bottom sheet
  void showAppointmentConfirmationBottomSheet({
    required BuildContext context,
    required String title,
    required String message,
    Widget? content,
    required VoidCallback onConfirm,
    VoidCallback? onCancel,
    String? confirmButtonText,
    bool isDismissible = true,
    bool enableDrag = true,
  }) {
    final appColors = context.appColors;
    final textTheme = context.textTheme;

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor:
          appColors.transparentColor, // Transparent background for effect
      isDismissible: isDismissible,
      enableDrag: enableDrag,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(25.r)),
      ),
      builder: (context) {
        return Padding(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ),
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 25.h),
            decoration: BoxDecoration(
              color: appColors.surfaceColor,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(25.r),
                topRight: Radius.circular(25.r),
              ),
            ),
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  /// Close Button
                  Align(
                    alignment: Alignment.topRight,
                    child: GestureDetector(
                      onTap: () => Navigator.pop(context),
                      child: Icon(
                        Icons.close,
                        color: appColors.errorColor,
                        size: 34.sp,
                      ),
                    ),
                  ),

                  /// Title
                  Text(
                    title,
                    style: textTheme.titleLarge?.copyWith(
                      color: appColors.primaryColor,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 12.h),

                  /// Dynamic Content (e.g., Doctor Name, Fee, Date)
                  if (content != null)
                    Padding(
                      padding: EdgeInsets.symmetric(vertical: 8.h),
                      child: content,
                    ),

                  /// Message
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 8.w),
                    child: Text(
                      message,
                      style: textTheme.bodyMedium?.copyWith(
                        fontSize: 16.sp,
                        height: 1.5,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  SizedBox(height: 20.h),

                  /// Buttons Row
                  CustomButton(
                    // width: context.screenWidth * 0.3,
                    onTap: () {
                      Navigator.pop(context);
                      onConfirm();
                    },
                    buttonText: confirmButtonText ?? 'Confirm',
                  ),
                  SizedBox(height: 10.h),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// Shows a bottom sheet to register a new patient
  void showAddNewPatientBottomSheet({
    required BuildContext context,
    required VoidCallback onPatientAdd,
    required TextEditingController nameController,
    required TextEditingController ageController,
    required Function(Gender?) onGenderSelected,
    required Function(DistrictEntity?) onDistrictSelected,
    Gender? selectedGender,
    DistrictEntity? selectedDistrict,
    bool isDismissible = true,
    bool enableDrag = true,
    required bool isLoading,
  }) {
    final appColors = context.appColors;
    final textTheme = context.textTheme;

    final registerFormKey = GlobalKey<FormState>();

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: appColors.transparentColor,
      isDismissible: isDismissible,
      enableDrag: enableDrag,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(25.r)),
      ),
      builder: (context) {
        return Padding(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ),
          child: PopScope(
            canPop: !isLoading,
            child: StatefulBuilder(builder: (context, setState) {
              return Container(
                padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 25.h),
                decoration: BoxDecoration(
                  color: appColors.surfaceColor,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(25.r),
                    topRight: Radius.circular(25.r),
                  ),
                ),
                child: Form(
                  key: registerFormKey,
                  child: SingleChildScrollView(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        /// Close Button
                        Align(
                          alignment: Alignment.topRight,
                          child: GestureDetector(
                            onTap: () =>
                                isLoading ? null : Navigator.pop(context),
                            child: Icon(
                              Icons.close,
                              color: appColors.errorColor,
                              size: 34.sp,
                            ),
                          ),
                        ),

                        /// Title
                        Text(
                          'Register New Patient',
                          style: textTheme.titleLarge?.copyWith(
                            color: appColors.primaryColor,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        SizedBox(height: 12.h),

                        /// Patient Name Input
                        CustomTextField(
                          controller: nameController,
                          fillColor: appColors.cardColor,
                          labelText: 'Patient Name',
                          validator: (value) =>
                              FormValidationHelper.validateRequiredField(
                                  value: value, fieldName: 'Patient Name'),
                        ),
                        SizedBox(height: 10.h),

                        /// Age Input
                        CustomTextField(
                          controller: ageController,
                          fillColor: appColors.cardColor,
                          labelText: 'Age',
                          keyboardType: TextInputType.number,
                          validator: (value) =>
                              FormValidationHelper.validateAge(value: value),
                        ),

                        SizedBox(height: 10.h),

                        /// Gender Dropdown
                        CustomDropDown<Gender>(
                          labelText: 'Gender',
                          fillColor: appColors.cardColor,
                          items: Gender.values,
                          displayItem: (Gender? value) => value?.genderType,
                          onChanged: (Gender? value) {
                            setState(() {
                              selectedGender = value;
                            });
                            onGenderSelected(value); // Pass back to parent
                          },
                          validator: (value) =>
                              FormValidationHelper.validateRequiredField(
                                  value: value?.genderType,
                                  fieldName: 'Gender'),
                        ),
                        SizedBox(height: 10.h),

                        /// District Dropdown
                        BlocBuilder<UserBloc, UserState>(
                          builder: (context, state) {
                            final districts = context.userBloc.districts;
                            return CustomDropDown<DistrictEntity>(
                              labelText: 'District',
                              fillColor: appColors.cardColor,
                              items: districts,
                              displayItem: (DistrictEntity? value) =>
                                  value?.name,
                              onChanged: (DistrictEntity? value) {
                                setState(() {
                                  selectedDistrict = value;
                                });
                                onDistrictSelected(
                                    value); // Pass back to parent
                              },
                              validator: (value) =>
                                  FormValidationHelper.validateRequiredField(
                                value: value?.name,
                                fieldName: 'District',
                              ),
                            );
                          },
                        ),
                        SizedBox(height: 20.h),

                        /// Confirm Button
                        CustomButton(
                          buttonState: isLoading
                              ? ButtonState.loading
                              : ButtonState.normal,
                          onTap: () {
                            if (registerFormKey.currentState!.validate()) {
                              // Handle new patient registration logic here
                              onPatientAdd();
                            }
                          },
                          buttonText: 'Add Patient',
                        ),
                        SizedBox(height: 10.h),
                      ],
                    ),
                  ),
                ),
              );
            }),
          ),
        );
      },
    );
  }
}
