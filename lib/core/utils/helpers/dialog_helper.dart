import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:hodan_hospital/core/utils/extensions/app_bloc_extensions.dart';
import 'package:hodan_hospital/features/shared/presentation/blocs%20or%20cubits/dialog%20cubit/dialog_cubit.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_dialog.dart';

void listenDialogCubit(BuildContext context, DialogState state) {
  if (state is DialogOpened) {
    final dialogWidget = PopScope(
      canPop: state.barrierDismissible,
      child: CustomDialog(
        dialogType: state.dialogType,
        title: state.title,
        message: state.message,
        cancelBtnText: state.cancelButtonText ?? 'Cancel',
        confirmBtnText: state.confirmButtonText ?? 'OK',
        cancelButtonwidth: state.cancelButtonwidth,
        confirmButtonwidth: state.confirmButtonwidth,
        cancelButtonHeight: state.cancelButtonHeight,
        confirmButtonHeight: state.confirmButtonHeight,
        onConfirm: () {
          context.dialogCubit.closeDialog();
          state.onConfirm?.call();
        },
        onCancel: () {
          context.dialogCubit.closeDialog();
          state.onCancel?.call();
        },
      ),
    );

    if (Theme.of(context).platform == TargetPlatform.iOS) {
      showCupertinoDialog(
        context: context,
        barrierDismissible: state.barrierDismissible,
        builder: (_) => dialogWidget,
      );
    } else {
      showDialog(
        context: context,
        barrierDismissible: state.barrierDismissible,
        builder: (_) => dialogWidget,
      );
    }
  } else if (state is DialogClosed) {
    if (Navigator.of(context, rootNavigator: true).canPop()) {
      Navigator.of(context, rootNavigator: true).pop();
    }
  }
}
