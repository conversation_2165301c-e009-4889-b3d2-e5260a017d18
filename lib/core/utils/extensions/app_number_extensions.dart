// ignore_for_file: unreachable_switch_default

import 'package:hodan_hospital/core/enums/language_enum.dart';
import 'package:intl/intl.dart';

// extension MoneyFormatting on num {
//   // Method to format a number as money
//   String toMoneyString({
//     Language language = Language.english, // Pass language enum
//     int decimalPlaces = 2,
//     bool showSign = false,
//     bool showSymbol = true,
//     bool isExpense = false,
//   }) {
//     // Define locales and symbols for each language
//     // String locale;
//     // String symbol;

//     // switch (language) {
//     //   case Language.arabic:
//     //     locale = 'ar_SA'; // Arabic locale for Saudi Arabia
//     //     // symbol = 'ر.س'; // Arabic currency symbol for Saudi Riyal
//     //     symbol = '\$';
//     //     break;
//     //   case Language.somali:
//     //   case Language.english:
//     //   default:
//     //     locale = 'en_US'; // English locale
//     //     symbol = '\$'; // Dollar symbol
//     //     break;
//     // }

//     // Determine if the number is a whole number
//     final bool isWholeNumber = this % 1 == 0;
//     final bool hasOneDecimalPlace =
//         (this * 10) % 10 != 0 && (this * 100) % 10 == 0;

//     // Effective decimal places
//     final int effectiveDecimalPlaces = isWholeNumber
//         ? 0
//         : hasOneDecimalPlace
//             ? 1
//             : decimalPlaces;

//     // Create the number format with custom pattern and options
//     NumberFormat format = NumberFormat.currency(
//       // locale: locale,
//       // symbol: showSymbol ? symbol : '', // Pass the symbol if showSymbol is true
//       decimalDigits: effectiveDecimalPlaces,
//     );

//     String formattedValue = format.format(this); // Format the number as money

//     // Convert English numerals to Arabic numerals if Arabic locale is selected
//     if (language == Language.arabic) {
//       formattedValue = formattedValue.replaceAllMapped(
//         RegExp(r'[0-9]'), // Match digits 0-9
//         (match) => _englishToArabicNumerals(match.group(0)!),
//       );
//     }

//     // Conditionally add the "+" or "-" sign
//     if (showSign) {
//       formattedValue = isExpense ? '-$formattedValue' : '+$formattedValue';
//     }

//     return formattedValue;
//   }

//   // Helper function to convert English digits to Arabic numerals
//   String _englishToArabicNumerals(String digit) {
//     const arabicNumerals = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
//     return arabicNumerals[int.parse(digit)];
//   }
// }

extension MoneyFormatting on num {
  String toMoneyString({
    Language language = Language.english,
    int decimalPlaces = 2,
    bool showSign = false,
    bool showSymbol = true, // Set default to true
  }) {
    // Create number format without currency symbol
    NumberFormat format =
        NumberFormat.decimalPattern(); // Use decimalPattern instead of currency

    String formattedValue = format.format(this);

    // Remove any group separators (commas) if you don't want them
    formattedValue = formattedValue.replaceAll(',', '');

    // Conditionally add the "+" or "-" sign
    if (showSign) {
      formattedValue = this >= 0 ? '+$formattedValue' : formattedValue;
    }

    if (showSymbol) {
      formattedValue = '\$$formattedValue';
    }

    return formattedValue;
  }
}
