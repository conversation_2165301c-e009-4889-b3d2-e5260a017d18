// ignore_for_file: unnecessary_null_comparison

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hodan_hospital/features/Order/presentation/bloc/order_bloc.dart';
import 'package:hodan_hospital/features/Result/presentation/bloc/result_bloc.dart';
// import 'package:hodan_hospital/features/Result/presentation/bloc/Result_bloc.dart';
import 'package:hodan_hospital/features/appointment/presentation/bloc/appointment_bloc.dart';
import 'package:hodan_hospital/features/authentication/presentation/bloc/authentication_bloc.dart';
import 'package:hodan_hospital/features/doctor/presentation/bloc/doctor_bloc.dart';
import 'package:hodan_hospital/features/shared/presentation/blocs%20or%20cubits/dialog%20cubit/dialog_cubit.dart';
import 'package:hodan_hospital/features/shared/presentation/blocs%20or%20cubits/shared%20bloc/shared_bloc.dart';
import 'package:hodan_hospital/features/shared/presentation/blocs%20or%20cubits/theme-cubit/theme_cubit.dart';
import 'package:hodan_hospital/features/user/presentation/bloc/user_bloc.dart';

import '../../../features/shared/presentation/blocs or cubits/bottom nav cubit/bottom_nav_cubit.dart';

extension BuildContextExtensions on BuildContext {
  AuthenticationBloc get authenticationBloc {
    assert(BlocProvider.of<AuthenticationBloc>(this) != null,
        'AuthenticationBloc not found in context');
    return BlocProvider.of<AuthenticationBloc>(this);
  }

  UserBloc get userBloc {
    assert(BlocProvider.of<UserBloc>(this) != null,
        'UserBloc not found in context');
    return BlocProvider.of<UserBloc>(this);
  }

  AppointmentBloc get appointmentBloc {
    assert(BlocProvider.of<AppointmentBloc>(this) != null,
        'AppointmentBloc not found in context');
    return BlocProvider.of<AppointmentBloc>(this);
  }

  DoctorBloc get doctorBloc {
    assert(BlocProvider.of<DoctorBloc>(this) != null,
        'DoctorBloc not found in context');
    return BlocProvider.of<DoctorBloc>(this);
  }

  OrderBloc get orderBloc {
    assert(BlocProvider.of<OrderBloc>(this) != null,
        'OrderBloc not found in context');
    return BlocProvider.of<OrderBloc>(this);
  }

  ResultBloc get resultBloc {
    assert(BlocProvider.of<ResultBloc>(this) != null,
        'ResultBloc not found in context');
    return BlocProvider.of<ResultBloc>(this);
  }

  DialogCubit get dialogCubit {
    assert(BlocProvider.of<DialogCubit>(this) != null,
        'DialogCubit not found in context');
    return BlocProvider.of<DialogCubit>(this);
  }

  BottomNavCubit get bottomNavCubit {
    assert(BlocProvider.of<BottomNavCubit>(this) != null,
        'BottomNavCubit not found in context');
    return BlocProvider.of<BottomNavCubit>(this);
  }

  ThemeCubit get themeCubit {
    assert(BlocProvider.of<ThemeCubit>(this) != null,
        'ThemeCubit not found in context');
    return BlocProvider.of<ThemeCubit>(this);
  }

  SharedBloc get sharedBloc {
    assert(BlocProvider.of<SharedBloc>(this) != null,
        'SharedBloc not found in context');
    return BlocProvider.of<SharedBloc>(this);
  }
}
