import 'dart:async';
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:fpdart/fpdart.dart';
import 'package:hodan_hospital/core/config/logger/app_logger.dart';
import 'package:hodan_hospital/core/constants/app_error_messages.dart';
import 'package:hodan_hospital/core/enums/http_failure_type.dart';
import 'package:hodan_hospital/core/enums/payment_failure_type.dart';
import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/features/shared/data/models/api_response_model.dart';
import 'package:hodan_hospital/core/network/connection_checker.dart';

class HttpErrorHandler {
  final ConnectionChecker connectionChecker;

  HttpErrorHandler({required this.connectionChecker});

  /// Handles Dio exceptions and maps them to `AppFailure`.
  AppFailure _handleDioError(DioException e) {
    HttpFailureType failureType;
    String errorMessage;

    // Extract API Message safely
    String? apiMessage;
    if (e.response?.data is Map<String, dynamic>) {
      final responseData = e.response!.data as Map<String, dynamic>;
      if (responseData.containsKey("message")) {
        final messageData = responseData["message"];
        if (messageData is String) {
          apiMessage = messageData;
        } else if (messageData is Map<String, dynamic> &&
            messageData.containsKey("msg")) {
          apiMessage = messageData["msg"];
        }
      }
    }

    // Map DioException to `HttpFailureType`
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        failureType = HttpFailureType.timeout;
        errorMessage = AppErrorMessages.httpTimeout;
        break;
      case DioExceptionType.connectionError:
        failureType = HttpFailureType.network;
        errorMessage = AppErrorMessages.httpNetworkError;
        break;
      case DioExceptionType.badResponse:
        failureType =
            e.response?.statusCode != null && e.response!.statusCode! >= 500
                ? HttpFailureType.serverError
                : HttpFailureType.clientError;
        errorMessage = AppErrorMessages.httpClientError(apiMessage);
        break;
      case DioExceptionType.cancel:
        failureType = HttpFailureType.cancelled;
        errorMessage = AppErrorMessages.httpCancelled;
        break;
      case DioExceptionType.unknown:
        if (e.error is SocketException || e.error is HttpException) {
          failureType = HttpFailureType.network;
          errorMessage = AppErrorMessages.httpNetworkError;
        } else {
          failureType = HttpFailureType.unknown;
          errorMessage = AppErrorMessages.httpUnknownError;
        }
        break;
      default:
        failureType = HttpFailureType.unknown;
        errorMessage = AppErrorMessages.httpUnknownError;
        break;
    }

    return HttpFailure(
      message: errorMessage,
      failureType: failureType,
      stackTrace: e.stackTrace,
      apiMessage: apiMessage,
      statusCode: e.response?.statusCode,
    );
  }

  /// Handles client-side errors (e.g., 4xx status codes) and returns an `AppFailure`.
  AppFailure _handleClientError<T>({
    required ApiResponseModel<T>? apiResponse,
    required int statusCode,
    required Map<String, dynamic> response,
  }) {
    String errorMessage = apiResponse?.apiMessage ?? '';
    HttpFailureType failureType;

    switch (statusCode) {
      case 400:
        failureType = HttpFailureType.badRequest;
        errorMessage = apiResponse?.apiMessage ?? '';
        break;
      case 401:
        failureType = HttpFailureType.unauthorized;
        errorMessage = apiResponse?.apiMessage ?? '';
        break;
      case 403:
        failureType = HttpFailureType.forbidden;
        // errorMessage = AppErrorMessages.httpForbidden;
        errorMessage = apiResponse?.apiMessage ?? '';
        break;
      case 404:
        failureType = HttpFailureType.notFound;
        // errorMessage = AppErrorMessages.httpNoDataFound;
        errorMessage = apiResponse?.apiMessage ?? '';
        break;

      case 417:
        // failureType = HttpFailureType.expectationFailed;
        // errorMessage = AppErrorMessages.httpExpectationFailed;
        final error = FrappeFailure.fromFrappeException(response);
        return error;
      default:
        failureType = HttpFailureType.clientError;
        errorMessage = apiResponse?.apiMessage ?? '';
        break;
    }

    return HttpFailure(
      message: AppErrorMessages.httpClientError(errorMessage),
      apiMessage: errorMessage,
      failureType: failureType,
      statusCode: statusCode,
    );
  }

  /// Main method to handle network requests and errors.
  FutureEitherFailOr<ApiResponseModel<T>> handleRequest<T>({
    required Future<Response?> Function() requestFunction,
    T Function(Object?)? fromJsonT,
    bool skipConnectionCheck = false,
  }) async {
    try {
      // ✅ Only check the connection if `skipConnectionCheck` is false
      if (!skipConnectionCheck) {
        final isConnected = await connectionChecker.isConnected;
        if (!isConnected) {
          return left(
            HttpFailure(
              message: "Check your internet connection and try again.",
              failureType: HttpFailureType.network,
            ),
          );
        }
      }

      // ✅ Make the network request
      final response = await requestFunction();

      // Validate the response format
      if (response == null || response.data is! Map<String, dynamic>) {
        return left(
          HttpFailure(
            message: "Invalid server response",
            failureType: HttpFailureType.serverError,
            statusCode: response?.statusCode,
          ),
        );
      }

      final responseBody = response.data as Map<String, dynamic>;

      // Hanlde success
      if (response.statusCode != null &&
          response.statusCode! >= 200 &&
          response.statusCode! < 400) {
        // Parse the API response
        final apiResponse = ApiResponseModel<T>.fromJson(
          json: responseBody,
          fromJsonT: fromJsonT,
          skipFrappeException: true,
        );
        return right(apiResponse);
      }

      final isClientError = response.statusCode != null &&
          response.statusCode! >= 400 &&
          response.statusCode! < 500;

      // Handle client-side errors (4xx)
      if (isClientError) {
        final apiResponse = ApiResponseModel<T>.fromJson(
          json: responseBody,
          fromJsonT: fromJsonT,
        );
        // return _handleClientError(apiResponse, response.statusCode!);
        return left(
          _handleClientError(
            apiResponse: apiResponse,
            statusCode: response.statusCode!,
            response: response.data,
          ),
        );
      }

      /// Handle server-side errors (5xx)
      /// Check if the response contains a Frappe exception
      if (responseBody.containsKey('exc_type') ||
          responseBody.containsKey('exception') ||
          responseBody.containsKey('exc')) {
        try {
          throw FrappeFailure.fromFrappeException(responseBody);
        } on FrappeFailure catch (error) {
          return left(
            FrappeFailure(
              message: error.message,
              failureType: error.failureType,
              stackTrace: error.stackTrace,
              serverMessage: error.serverMessage,
              serverStackTrace: error.serverStackTrace,
            ),
          );
        }
      }

      // Handle server-side errors (5xx)
      return left(
        HttpFailure(
          message: "Server error occurred",
          failureType: HttpFailureType.serverError,
          statusCode: response.statusCode,
        ),
      );
    } on DioException catch (error) {
      return left(_handleDioError(error));
    } catch (error, stackTrace) {
      return left(
        HttpFailure(
          message: "Unexpected error occurred",
          failureType: HttpFailureType.unknown,
          stackTrace: stackTrace,
        ),
      );
    }
  }

  /// Handles file download requests and errors with streaming progress.
  // Stream<Either<AppFailure, DownloadProgress>> handleFileDownload({
  //   required Future<Response?> Function(void Function(int, int))
  //       requestFunction,
  // }) async* {
  //   try {
  //     final StreamController<Either<HttpFailure, DownloadProgress>> controller =
  //         StreamController();

  //     final response = await requestFunction((received, total) {
  //       controller
  //           .add(right(DownloadProgress(received: received, total: total)));
  //     });

  //     if (response == null || response.statusCode != 200) {
  //       controller.add(left(HttpFailure(
  //         message: "Invalid server response",
  //         failureType: HttpFailureType.serverError,
  //         statusCode: response?.statusCode,
  //       )));
  //       await controller.close();
  //       return;
  //     }

  //     AppLogger().info("✅ File downloaded successfully.");
  //     await controller.close();
  //   } on DioException catch (error) {
  //     final failure = _handleDioError(error);
  //     AppLogger().error("❌ Download failed: ${failure.message}");
  //     yield left(failure);
  //   } catch (error, stackTrace) {
  //     AppLogger().error("❌ Unexpected error during file download",
  //         error: error, stackTrace: stackTrace);
  //     yield left(HttpFailure(
  //       message: "Unexpected error occurred",
  //       failureType: HttpFailureType.unknown,
  //       stackTrace: stackTrace,
  //     ));
  //   }
  // }

  FutureEitherFailOr<Uint8List> handleRequestBytes({
    required Future<Response?> Function() requestFunction,
  }) async {
    try {
      final response = await requestFunction();
      if (response == null || response.data is! List<int>) {
        return left(
          HttpFailure(
            message: AppErrorMessages.httpInvalidResponse,
            failureType: HttpFailureType.serverError,
            statusCode: response?.statusCode,
            apiMessage: "Invalid server response",
          ),
        );
      }
      final Uint8List bytes = Uint8List.fromList(response.data);
      return right(bytes);
    } on DioException catch (error) {
      return left(_handleDioError(error));
    } catch (error, stackTrace) {
      return left(
        HttpFailure(
          message: "Unexpected error occurred",
          failureType: HttpFailureType.unknown,
          stackTrace: stackTrace,
        ),
      );
    }
  }

  ///------------------------- PAYMENT SECTION  ------------

  /// Encapsulated method to handle payment requests.
  FutureEitherFailOr<Map<String, dynamic>> handlePaymentRequest({
    required Future<Response?> Function() requestFunction,
  }) async {
    try {
      // ✅ Check Internet Connection Before Sending the Request
      final isConnected = await connectionChecker.isConnected;
      if (!isConnected) {
        return left(PaymentFailure(
          message: AppErrorMessages.paymentNetworkError,
          failureType: PaymentFailureType.network,
        ));
      }

      final response = await requestFunction();

      // ✅ Validate Response Structure
      if (response == null || response.data is! Map<String, dynamic>) {
        AppLogger().error("❌ Invalid response from the server");
        return left(PaymentFailure(
          message: AppErrorMessages.paymentTransactionError,
          failureType: PaymentFailureType.transactionError,
          statusCode: response?.statusCode,
        ));
      }

      final responseBody = response.data as Map<String, dynamic>;
      AppLogger().info("💡 Payment response: $responseBody");

      // ✅ Extract Essential Fields Safely
      final int responseCode =
          int.tryParse(responseBody["responseCode"].toString()) ?? -1;
      final String responseMsg =
          responseBody["responseMsg"]?.toString() ?? "Unknown error occurred.";
      final String? errorCode = responseBody["errorCode"]?.toString();

      // ✅ Handle Successful Payment
      if (responseCode == 2000 || responseCode == 2001) {
        AppLogger().info("✅ Payment successful");
        // return right("Payment successful");
        return right(responseBody);
      }

      // ✅ Handle Special Case: `5206` (Same Code, Different Messages)
      if (responseCode == 5206) {
        AppLogger().warning("⚠️ Payment failed: $responseMsg");

        return left(PaymentFailure(
          message: _getPaymentFailureMessage(responseMsg),
          failureType: _getPaymentFailureType(responseMsg),
          statusCode: response.statusCode,
          apiMessage: responseMsg,
        ));
      }

      // ❌ Handle Other
      final failureType = PaymentFailureType.transactionError;
      AppLogger()
          .error("❌ Payment failed: $responseMsg (Error Code: $errorCode)");

      return left(PaymentFailure(
        message: AppErrorMessages.paymentTransactionError,
        failureType: failureType,
        statusCode: response.statusCode,
        apiMessage: responseMsg,
      ));
    } on DioException catch (dioError, stackTrace) {
      return left(_handleDioPaymentError(dioError, stackTrace));
    } catch (error, stackTrace) {
      AppLogger().error(
        "❌ Unexpected error during payment: ${error.toString()}",
        error: error,
        stackTrace: stackTrace,
      );

      return left(PaymentFailure(
        message: AppErrorMessages.paymentUnexpectedError(error.toString()),
        failureType: PaymentFailureType.unknown,
        stackTrace: stackTrace,
      ));
    }
  }

  /// Handles Dio exceptions for payment requests.
  PaymentFailure _handleDioPaymentError(
      DioException e, StackTrace? stackTrace) {
    PaymentFailureType failureType;
    String errorMessage;

    // ✅ Extract API Message safely
    String? apiMessage;
    if (e.response?.data is Map<String, dynamic>) {
      final responseData = e.response!.data as Map<String, dynamic>;
      apiMessage = responseData["message"]?.toString() ??
          responseData["msg"]?.toString();
    }

    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        failureType = PaymentFailureType.timeout;
        errorMessage = AppErrorMessages.paymentTimeout;
        break;
      case DioExceptionType.connectionError:
        failureType = PaymentFailureType.network;
        errorMessage = AppErrorMessages.paymentNetworkError;
        break;
      case DioExceptionType.badResponse:
        failureType = PaymentFailureType.transactionError;
        errorMessage = apiMessage ?? AppErrorMessages.paymentTransactionError;
        break;
      case DioExceptionType.cancel:
        failureType = PaymentFailureType.cancelled;
        errorMessage = AppErrorMessages.paymentCancelled;
        break;
      case DioExceptionType.unknown:
        failureType = (e.error is SocketException || e.error is HttpException)
            ? PaymentFailureType.network
            : PaymentFailureType.unknown;
        errorMessage =
            AppErrorMessages.paymentUnknownError("Unknown Dio error");
        break;
      default:
        failureType = PaymentFailureType.unknown;
        errorMessage = AppErrorMessages.paymentUnknownError("Unhandled error");
        break;
    }

    AppLogger().error(
      "❌ DioException during payment: $errorMessage",
      error: e,
      stackTrace: stackTrace,
    );

    return PaymentFailure(
      message: errorMessage,
      failureType: failureType,
      stackTrace: stackTrace,
      apiMessage: apiMessage,
      statusCode: e.response?.statusCode,
    );
  }

  /// Determines the correct payment failure type based on the response message.
  PaymentFailureType _getPaymentFailureType(String message) {
    final lowerMsg = message.toLowerCase();

    if (lowerMsg.contains("balance is not sufficient")) {
      return PaymentFailureType.insufficientBalance;
    }
    if (lowerMsg.contains("error occurred, please try again later") ||
        lowerMsg.contains("customer rejected to authorize payment")) {
      return PaymentFailureType.customerRejected;
    }
    if (lowerMsg.contains("invalid payment method")) {
      return PaymentFailureType.invalidPaymentMethod;
    }
    if (lowerMsg.contains("invalid pin code")) {
      return PaymentFailureType.invalidPinCode;
    }

    return PaymentFailureType.transactionError;
  }

  /// Determines the correct payment failure message based on the response message.
  String _getPaymentFailureMessage(String message) {
    final lowerMsg = message.toLowerCase();

    if (lowerMsg.contains("balance is not sufficient")) {
      return AppErrorMessages.paymentInsufficientBalance;
    }
    if (lowerMsg.contains("error occurred, please try again later") ||
        lowerMsg.contains("customer rejected to authorize payment")) {
      return AppErrorMessages.paymentCustomerRejected;
    }
    if (lowerMsg.contains("invalid payment method")) {
      return AppErrorMessages.paymentInvalidMethod;
    }
    if (lowerMsg.contains("invalid pin code")) {
      return AppErrorMessages.paymentInvalidPinCode;
    }

    return AppErrorMessages.paymentTransactionError;
  }
}
