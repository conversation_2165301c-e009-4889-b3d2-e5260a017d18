// ignore_for_file: unreachable_switch_default

import 'dart:convert';

import 'package:fpdart/fpdart.dart';
import 'package:hodan_hospital/core/constants/app_error_messages.dart';
import 'package:hodan_hospital/core/enums/cache_failure_type.dart';
import 'package:hodan_hospital/core/enums/database_failure_type.dart';
import 'package:hodan_hospital/core/enums/frappe_failure_type.dart';
import 'package:hodan_hospital/core/enums/http_failure_type.dart';
import 'package:hodan_hospital/core/enums/parse_failure_type.dart';
import 'package:hodan_hospital/core/enums/payment_failure_type.dart';

// Typedefs for convenience
typedef FutureEitherFailOr<T> = Future<Either<AppFailure, T>>;
typedef StreamEitherFailOr<T> = Stream<Either<AppFailure, T>>;
typedef EitherFailOr<T> = Either<AppFailure, T>;

// Abstract Base Class for all failures
abstract class AppFailure implements Exception {
  final String message;
  final StackTrace? stackTrace;

  AppFailure({required this.message, this.stackTrace});

  /// Abstract method to be overridden by each failure type
  String getErrorMessage();

  @override
  String toString() {
    return 'AppFailure(message: $message, stackTrace: $stackTrace)';
  }
}

/// HTTP Errors
class HttpFailure extends AppFailure {
  final String? apiMessage;
  final int? statusCode;
  final HttpFailureType failureType;

  HttpFailure({
    required super.message,
    super.stackTrace,
    required this.failureType,
    this.apiMessage,
    this.statusCode,
  });

  @override
  String getErrorMessage() {
    switch (failureType) {
      case HttpFailureType.badRequest:
        return apiMessage ?? AppErrorMessages.httpBadRequest;
      case HttpFailureType.unauthorized:
        return apiMessage ?? AppErrorMessages.httpUnauthorized;
      case HttpFailureType.forbidden:
        return apiMessage ?? AppErrorMessages.httpForbidden;
      case HttpFailureType.notFound:
        return apiMessage ?? AppErrorMessages.httpNotFound;
      case HttpFailureType.timeout:
        return apiMessage ?? AppErrorMessages.httpTimeout;
      case HttpFailureType.network:
        return apiMessage ?? AppErrorMessages.httpNetworkError;
      case HttpFailureType.cancelled:
        return apiMessage ?? AppErrorMessages.httpCancelled;
      case HttpFailureType.clientError:
        return apiMessage ?? AppErrorMessages.httpClientError(apiMessage);
      case HttpFailureType.serverError:
        return apiMessage ?? AppErrorMessages.httpServerError + message;
      case HttpFailureType.unknown:
        return apiMessage ?? '${AppErrorMessages.httpUnknownError}$message';
      default:
        return apiMessage ?? '${AppErrorMessages.httpUnExpectedError}$message';
    }
  }

  @override
  String toString() {
    return 'HttpFailure(message: $message, stackTrace: $stackTrace, failureType: $failureType, apiMessage: $apiMessage, statusCode: $statusCode)';
  }
}

class PaymentFailure extends AppFailure {
  final PaymentFailureType failureType;
  final String? apiMessage;
  final int? statusCode;

  PaymentFailure({
    required super.message,
    super.stackTrace,
    required this.failureType,
    this.apiMessage,
    this.statusCode,
  });

  @override
  String getErrorMessage() {
    switch (failureType) {
      case PaymentFailureType.insufficientBalance:
        return apiMessage ?? AppErrorMessages.paymentInsufficientBalance;
      case PaymentFailureType.customerRejected:
        return apiMessage ?? AppErrorMessages.paymentCustomerRejected;
      case PaymentFailureType.invalidPinCode:
        return apiMessage ?? AppErrorMessages.paymentInvalidPinCode;
      case PaymentFailureType.invalidPaymentMethod:
        return apiMessage ?? AppErrorMessages.paymentInvalidMethod;
      case PaymentFailureType.transactionError:
        return apiMessage ?? AppErrorMessages.paymentTransactionError;
      case PaymentFailureType.cancelled:
        return apiMessage ?? AppErrorMessages.paymentCancelled;
      case PaymentFailureType.timeout:
        return apiMessage ?? AppErrorMessages.paymentTimeout;
      case PaymentFailureType.network:
        return apiMessage ?? AppErrorMessages.paymentNetworkError;
      case PaymentFailureType.unknown:
        return apiMessage ?? AppErrorMessages.paymentUnknownError(message);
      default:
        return apiMessage ?? AppErrorMessages.paymentUnexpectedError(message);
    }
  }

  @override
  String toString() {
    return 'PaymentFailure(message: $message, failureType: $failureType, statusCode: $statusCode, apiMessage: $apiMessage, stackTrace: $stackTrace)';
  }
}

/// Database Errors
class DatabaseFailure extends AppFailure {
  final DatabaseFailureType failureType;

  DatabaseFailure({
    required super.message,
    super.stackTrace,
    required this.failureType,
  });

  @override
  String getErrorMessage() {
    switch (failureType) {
      case DatabaseFailureType.connectionError:
        return '${AppErrorMessages.dbConnectionError}$message';
      case DatabaseFailureType.readError:
        return '${AppErrorMessages.dbReadError}$message';
      case DatabaseFailureType.writeError:
        return '${AppErrorMessages.dbWriteError}$message';
      case DatabaseFailureType.queryError:
        return '${AppErrorMessages.dbQueryError}$message';
      case DatabaseFailureType.noDataFound:
        return '${AppErrorMessages.dbNoDataFound}$message';
      case DatabaseFailureType.transactionError:
        return '${AppErrorMessages.dbTransactionError}$message';
      case DatabaseFailureType.unknown:
        return '${AppErrorMessages.dbUnknownError}$message';
      default:
        return '${AppErrorMessages.dbUnExpectedError}$message';
    }
  }

  @override
  String toString() {
    return 'DatabaseFailure(message: $message, stackTrace: $stackTrace, failureType: $failureType)';
  }
}

/// Cache Errors
class CacheFailure extends AppFailure {
  final CacheFailureType failureType;

  CacheFailure({
    required super.message,
    super.stackTrace,
    required this.failureType,
  });

  @override
  String getErrorMessage() {
    return message;
    // switch (failureType) {
    //   case CacheFailureType.notFound:
    //     return message;
    //   case CacheFailureType.writeError:
    //     return '${AppErrorMessages.cacheWriteError}$message';
    //   case CacheFailureType.readError:
    //     return '${AppErrorMessages.cacheReadError}$message';
    //   case CacheFailureType.deleteError:
    //     return '${AppErrorMessages.cacheDeleteError}$message';
    //   case CacheFailureType.verificationError:
    //     return message;
    //   case CacheFailureType.expired:
    //     return message;
    //   case CacheFailureType.invalidOtp:
    //     return message;
    //   case CacheFailureType.unknown:
    //     return '${AppErrorMessages.cacheUnknownError}$message';
    //   default:
    //     return '${AppErrorMessages.cacheUnExpectedError}$message';
    // }
  }

  @override
  String toString() {
    return 'CacheFailure(message: $message, stackTrace: $stackTrace, failureType: $failureType)';
  }
}

/// Validation Errors
class ValidationFailure extends AppFailure {
  final String fieldName;

  ValidationFailure({
    required super.message,
    super.stackTrace,
    required this.fieldName,
  });

  @override
  String toString() {
    return 'ValidationFailure(message: $message, stackTrace: $stackTrace, fieldName: $fieldName)';
  }

  @override
  String getErrorMessage() {
    return AppErrorMessages.validationError(message, fieldName);
  }
}

/// Parsing Errors
class ParsingFailure<T> extends AppFailure {
  final ParsingFailureType failureType;
  final T expectedType;

  ParsingFailure({
    required super.message,
    super.stackTrace,
    required this.failureType,
    required this.expectedType,
  });

  @override
  String getErrorMessage() {
    switch (failureType) {
      case ParsingFailureType.jsonParsingError:
        return AppErrorMessages.jsonParsingError(message, expectedType);
      case ParsingFailureType.xmlParsingError:
        return AppErrorMessages.xmlParsingError(message, expectedType);
      case ParsingFailureType.typeConversionError:
        return AppErrorMessages.typeConversionError(message, expectedType);
      case ParsingFailureType.invalidFormat:
        return AppErrorMessages.formatParsingError(message, expectedType);
      case ParsingFailureType.typeMismatch:
        return AppErrorMessages.schemaMismatchError(message, expectedType);
      case ParsingFailureType.unknown:
        return AppErrorMessages.unknownParsingError(message, expectedType);
      default:
        return AppErrorMessages.unexpectedParsingError(message, expectedType);
    }
  }

  @override
  String toString() {
    return 'ParsingFailure(message: $message, stackTrace: $stackTrace, failureType: $failureType, expectedType: $expectedType)';
  }
}

/// Generic Failure
class UnexpectedFailure extends AppFailure {
  UnexpectedFailure({
    required super.message,
    super.stackTrace,
  });

  @override
  String getErrorMessage() {
    return AppErrorMessages.unexpectedError(message);
  }

  @override
  String toString() {
    return 'UnexpectedFailure(message: $message, stackTrace: $stackTrace)';
  }
}

class FrappeFailure extends AppFailure {
  final String? serverMessage;
  final String? serverStackTrace;
  final FrappeFailureType failureType;

  FrappeFailure({
    required super.message,
    super.stackTrace,
    this.serverMessage,
    this.serverStackTrace,
    required this.failureType,
  });

  @override
  String getErrorMessage() {
    switch (failureType) {
      case FrappeFailureType.validationError:
        return AppErrorMessages.frappeValidationError(
            message: message, serverMessage: serverMessage);
      case FrappeFailureType.missingArgument:
        return AppErrorMessages.frappeMissingArgument(
            message: message, serverMessage: serverMessage);
      case FrappeFailureType.moduleNotFound:
        return AppErrorMessages.frappeModuleNotFound(
            message: message, serverMessage: serverMessage);
      case FrappeFailureType.permissionError:
        return AppErrorMessages.frappePermissionError(
            message: message, serverMessage: serverMessage);
      case FrappeFailureType.unknown:
        return AppErrorMessages.frappeUnknownError(
            message: message, serverMessage: serverMessage);
      default:
        return AppErrorMessages.frappeUnexpectedError(
            message: message, serverMessage: serverMessage);
    }
  }

  // static FrappeFailure fromFrappeException(Map<String, dynamic> json) {
  //   try {
  //     final excType = json['exc_type']?.toString().trim();
  //     final serverMessage = _parseServerMessages(json['_server_messages']);
  //     final fullStackTrace = json['exc'];

  //     final stackTraceSummary =
  //         (fullStackTrace is List<dynamic> && fullStackTrace.isNotEmpty)
  //             ? fullStackTrace.first.toString()
  //             : fullStackTrace?.toString() ?? "No stack trace available";

  //     // fallback if both excType and _server_messages are missing
  //     final fallbackMessage = json['message'] is String
  //         ? json['message'] as String
  //         : 'Unknown server error occurred';

  //     // 🎯 Decide based on excType
  //     if (excType != null) {
  //       if (excType.contains("ValidationError")) {
  //         return FrappeFailure(
  //           message: 'Validation Error: ${serverMessage ?? fallbackMessage}',
  //           stackTrace: StackTrace.current,
  //           serverMessage: serverMessage,
  //           serverStackTrace: stackTraceSummary,
  //           failureType: FrappeFailureType.validationError,
  //         );
  //       }

  //       if (excType.contains("PermissionError")) {
  //         return FrappeFailure(
  //           message: 'Permission Error: ${serverMessage ?? fallbackMessage}',
  //           stackTrace: StackTrace.current,
  //           serverMessage: serverMessage,
  //           serverStackTrace: stackTraceSummary,
  //           failureType: FrappeFailureType.permissionError,
  //         );
  //       }

  //       if (excType.contains('missing required positional argument')) {
  //         return FrappeFailure(
  //           message:
  //               'Missing required argument: ${serverMessage ?? fallbackMessage}',
  //           stackTrace: StackTrace.current,
  //           serverMessage: serverMessage,
  //           serverStackTrace: stackTraceSummary,
  //           failureType: FrappeFailureType.missingArgument,
  //         );
  //       }

  //       if (excType.contains('No module named')) {
  //         return FrappeFailure(
  //           message: 'Module not found: ${serverMessage ?? fallbackMessage}',
  //           stackTrace: StackTrace.current,
  //           serverMessage: serverMessage,
  //           serverStackTrace: stackTraceSummary,
  //           failureType: FrappeFailureType.moduleNotFound,
  //         );
  //       }
  //     }

  //     // 🔥 Smart fallback: if _server_messages exists, assume it's a user-facing error
  //     if (serverMessage != null) {
  //       return FrappeFailure(
  //         message: 'Server Error: $serverMessage',
  //         stackTrace: StackTrace.current,
  //         serverMessage: serverMessage,
  //         serverStackTrace: stackTraceSummary,
  //         failureType: FrappeFailureType.unknown,
  //       );
  //     }

  //     // 🛡️ Ultimate fallback
  //     return FrappeFailure(
  //       message: 'Unknown Frappe Error: $fallbackMessage',
  //       stackTrace: StackTrace.current,
  //       serverMessage: serverMessage,
  //       serverStackTrace: stackTraceSummary,
  //       failureType: FrappeFailureType.unknown,
  //     );
  //   } catch (e, s) {
  //     return FrappeFailure(
  //       message: 'Exception while parsing Frappe error: $e',
  //       stackTrace: s,
  //       failureType: FrappeFailureType.unknown,
  //     );
  //   }
  // }

  static FrappeFailure fromFrappeException(Map<String, dynamic> json) {
    try {
      final excType = json['exc_type']?.toString().trim();
      final serverMessage = _parseServerMessages(json['_server_messages']);
      final fullStackTrace = json['exc'];
      final messageDetails = json['message'] is Map
          ? json['message'] as Map<String, dynamic>
          : null;

      final stackTraceSummary =
          (fullStackTrace is List<dynamic> && fullStackTrace.isNotEmpty)
              ? fullStackTrace.first.toString()
              : fullStackTrace?.toString() ?? 'No stack trace available';

      // First try to get the detailed message from the message.details field
      final detailedMessage = messageDetails?['details']?.toString().trim();

      // Then try serverMessage
      final fallbackMessage = detailedMessage ??
          serverMessage ??
          (json['message'] is String
              ? json['message'] as String
              : 'Unknown server error occurred');

      // 🎯 Decide based on excType
      if (excType != null) {
        if (excType.contains('ValidationError')) {
          return FrappeFailure(
            message: 'Validation Error: $fallbackMessage',
            stackTrace: StackTrace.current,
            serverMessage: serverMessage,
            serverStackTrace: stackTraceSummary,
            failureType: FrappeFailureType.validationError,
          );
        }

        if (excType.contains('PermissionError')) {
          return FrappeFailure(
            message: 'Permission Error: $fallbackMessage',
            stackTrace: StackTrace.current,
            serverMessage: serverMessage,
            serverStackTrace: stackTraceSummary,
            failureType: FrappeFailureType.permissionError,
          );
        }

        if (excType.contains('missing required positional argument')) {
          return FrappeFailure(
            message: 'Missing required argument: $fallbackMessage',
            stackTrace: StackTrace.current,
            serverMessage: serverMessage,
            serverStackTrace: stackTraceSummary,
            failureType: FrappeFailureType.missingArgument,
          );
        }

        if (excType.contains('No module named')) {
          return FrappeFailure(
            message: 'Module not found: $fallbackMessage',
            stackTrace: StackTrace.current,
            serverMessage: serverMessage,
            serverStackTrace: stackTraceSummary,
            failureType: FrappeFailureType.moduleNotFound,
          );
        }
      }

      // 🔥 Smart fallback: if we have any message, use it
      return FrappeFailure(
        message: 'Server Error: $fallbackMessage',
        stackTrace: StackTrace.current,
        serverMessage: serverMessage,
        serverStackTrace: stackTraceSummary,
        failureType: FrappeFailureType.unknown,
      );
    } catch (e, s) {
      return FrappeFailure(
        message: 'Exception while parsing Frappe error: $e',
        stackTrace: s,
        failureType: FrappeFailureType.unknown,
      );
    }
  }

  static String? _parseServerMessages(String? serverMessages) {
    if (serverMessages == null) return null;
    try {
      final decoded = jsonDecode(serverMessages) as List<dynamic>;
      return decoded.isNotEmpty ? decoded.first['message'] as String? : null;
    } catch (_) {
      return null;
    }
  }

  @override
  String toString() {
    return 'FrappeFailure(message: $message, stackTrace: $stackTrace, serverMessage: $serverMessage, serverStackTrace: $serverStackTrace, failureType: $failureType)';
  }
}
