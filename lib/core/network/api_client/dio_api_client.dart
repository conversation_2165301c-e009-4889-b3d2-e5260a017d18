import 'package:dio/dio.dart';
import 'package:hodan_hospital/core/config/enviroment/enviroment_config.dart';
import 'package:hodan_hospital/core/enums/http_method.dart';
import 'package:hodan_hospital/core/network/interceptors/auth_interceptor.dart';
import 'package:hodan_hospital/core/network/interceptors/logger_interceptor.dart';
import 'package:hodan_hospital/core/utils/helpers/request_data.dart';

class DioApiClient {
  late Dio _dio;

  DioApiClient() {
    final BaseOptions options = BaseOptions(
      baseUrl: EnvironmentConfig.baseUrl,
      connectTimeout: const Duration(seconds: 20),
      receiveTimeout: const Duration(seconds: 30),
      sendTimeout: const Duration(seconds: 30),
      followRedirects: true,
      validateStatus: (status) {
        return status != null && status <= 500;
      },
      receiveDataWhenStatusError: true,
      headers: {
        'Accept-Encoding': 'br, gzip',
        'Authorization': EnvironmentConfig.apiToken,
      },
    );

    _dio = Dio(options);

    _dio.interceptors.add(AuthInterceptor());
    _dio.interceptors.add(LoggerInterceptor());
  }

  // Unified method for all HTTP requests
  Future<Response> request({
    required HttpMethod method,
    required String endPointUrl,
    Map<String, dynamic>? headers,
    RequestData? data,
    Map<String, dynamic>? queryParameters,
    void Function(int, int)? onReceiveProgress,
    void Function(int, int)? onSendProgress,
    CancelToken? cancelToken,
    Duration? sendTimeout,
    Duration? receiveTimeout,
    bool Function(int?)? validateStatus,
    ResponseType? responseType,
  }) async {
    try {
      final options = Options(
        method: method.name,
        sendTimeout: sendTimeout,
        receiveTimeout: receiveTimeout,
        validateStatus: validateStatus,
        responseType: responseType,
        headers: {
          ...?headers,
          if (data != null &&
              headers != null &&
              !headers.containsKey('Content-Type'))
            'Content-Type': data.contentType,
        },
      );

      final response = await _dio.request(
        endPointUrl,
        options: options,
        data: data?.toHttpPayload,
        queryParameters: queryParameters,
        onReceiveProgress: onReceiveProgress,
        onSendProgress: onSendProgress,
        cancelToken: cancelToken,
      );
      return response;
    } on DioException {
      rethrow;
    } catch (e) {
      rethrow;
    }
  }

  /// Download File
  Future<Response> downloadFile({
    required String urlPath,
    dynamic savePath,
    void Function(int, int)? onReceiveProgress,
    Map<String, dynamic>? queryParameters,
    CancelToken? cancelToken,
    bool deleteOnError = true,
    FileAccessMode fileAccessMode = FileAccessMode.write,
    String lengthHeader = Headers.contentLengthHeader,
    Object? data,
    Options? options,
  }) async {
    try {
      final response = await _dio.download(
        urlPath,
        savePath,
        onReceiveProgress: onReceiveProgress,
      );
      return response;
    } on DioException {
      rethrow;
    } catch (e) {
      rethrow;
    }
  }
}
