import 'package:dio/dio.dart';
import 'package:hodan_hospital/core/config/logger/app_logger.dart';

class LoggerInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    AppLogger().info('🔗 Request: ${options.uri}');
    AppLogger().info('🔗 Request Data: ${options.data}');
    // AppLogger().info('🔗 Request Headers: ${options.headers}');
    // AppLogger().info("🔗 Request Method: ${options.method}");
    // AppLogger().info("🔗 Request Path: ${options.path}");

    return handler.next(options);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    AppLogger().info('✅ Response Status Code: ${response.statusCode}');
    AppLogger().info('✅ Response Data: ${response.data}');
    // AppLogger().info('✅ Response Headers: ${response.headers}');
    // AppLogger()
    // .info('✅ Response Request Path: ${response.requestOptions.path}');
    return handler.next(response);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    AppLogger().error(
      '❌ Error: ${err.message}',
      error: {
        'url': err.requestOptions.uri.toString(),
        'data': err.response?.data,
        'headers': err.requestOptions.headers,
        'realError': err,
      },
      stackTrace: err.stackTrace,
    );
    return handler.next(err);
  }
}
