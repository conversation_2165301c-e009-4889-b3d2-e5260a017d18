import 'package:hodan_hospital/core/config/enviroment/enviroment_config.dart';

class ApiEndpoints {
  /// Authentication
  static const String login = 'Patient.patient_login';
  static const String register = 'Patient.register_patient';
  static const String canRegister = 'Patient.can_register_patient';

  // Doctors
  static const String getDoctors = 'Doctors.get_all_doctors';
  static const String getDoctorDepartments =
      'Doctors.get_doctors_by_department';
  static const String getAllDepartments = 'Doctors.get_all_departments';

  /// User
  static const String getUsersByPhone = 'Patient.get_patients_with_same_mobile';
  static const String getDistricts = 'Patient.get_districts';
  static const String getPatientProfile = 'Patient.get_patient_profile';
  static const String sendFeedback = 'Patient.submit_patient_feedback';

  /// Appointment
  static const String makeAppointment = 'appointment.create_appointment';
  static const String canBookAppointment =
      'appointment.validate_appointment_booking';
  static const String getAppointment = 'appointment.get_appointments';

  /// Banners
  static const String getBanners = 'banners.get_all_banners';

  /// Appointment Bar Code
  static const String processAppointmentBarCode =
      'http://192.168.100.196:5000/print_que';

  /// PDF
  // static const String getPDF =
  //     'https://102.68.17.210/api/method/frappe.utils.print_format.download_pdf';
  static const String getPDF =
      'https://erpnext.hodanhospital.com/api/method/frappe.utils.print_format.download_pdf';

  // Orders
  static const String getOrders = 'Order.get_sales_orders_by_mobile';
  static const String saveOrder = 'Order.convert_sales_order_to_invoice';

  // Results
  static const String getLabResults = 'result.get_lab_results_by_mobile';

  // SMS
  static final String sendSMS = '${EnvironmentConfig.smsBaseUrl}/api/SendSMS';
  static final String getAccessToken = '${EnvironmentConfig.smsBaseUrl}/token';
}
