import 'dart:async';
import 'dart:io';

import 'package:hodan_hospital/core/config/logger/app_logger.dart';
import 'package:hodan_hospital/core/enums/database_failure_type.dart';
import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:path/path.dart' as p;
import 'package:path_provider/path_provider.dart';
// import 'package:hodan_hospital/objectbox.g.dart';

class DatabaseManager {
  // Store? _store;
  bool _isInitialized = false;
  Completer<void>? _initCompleter;

  /// Check if database is initialized
  bool get isInitialized => _isInitialized;

  /// Initialize ObjectBox Store (Thread-safe)
  Future<void> init() async {
    if (_isInitialized) return;

    if (_initCompleter != null) {
      return _initCompleter!.future; // Wait for ongoing initialization
    }

    _initCompleter = Completer<void>();

    try {
      final directory = await getApplicationDocumentsDirectory();
      final storePath = p.join(directory.path, 'objectbox');

      await Directory(storePath)
          .create(recursive: true); // Ensure directory exists

      // _store = Store(getObjectBoxModel(), directory: storePath);
      _isInitialized = true;

      AppLogger().info('✅ ObjectBox Database Initialized Successfully!');

      _initCompleter!.complete(); // Mark initialization as complete
    } catch (e, stacktrace) {
      AppLogger().error('❌ Database Initialization Failed',
          error: e, stackTrace: stacktrace);
      _initCompleter!.completeError(e);
      throw DatabaseFailure(
        message: 'Database Initialization Failed: ${e.toString()}',
        failureType: DatabaseFailureType.connectionError,
        stackTrace: stacktrace,
      );
    } finally {
      _initCompleter = null;
    }
  }

  /// Get Store Instance Safely
  // Future<Store> get store async {
  //   if (!_isInitialized || _store == null) {
  //     await init(); // Ensure initialization completes before accessing store
  //   }
  //   return _store!;
  // }

  /// Get Box for a given entity type
  // Future<Box<T>> getBox<T>() async {
  //   try {
  //     return (await store).box<T>();
  //   } catch (e, stacktrace) {
  //     throw DatabaseFailure(
  //       message: "❌ Failed to access database box for type $T: ${e.toString()}",
  //       failureType: DatabaseFailureType.queryError,
  //       stackTrace: stacktrace,
  //     );
  //   }
  // }

  /// 🔴 **Clear all saved data from ObjectBox**
  Future<void> clearAllData() async {
    try {
      // final storeInstance = await store;
      // storeInstance.runInTransaction(TxMode.write, () {
      //   storeInstance.box<UserModel>().removeAll();
      //   storeInstance.box<UsersByPhoneModel>().removeAll();
      //   // storeInstance.box<DoctorModel>().removeAll();
      //   // storeInstance.box<BannerModel>().removeAll();
      //   storeInstance.box<AppointmentModel>().removeAll();
      //   storeInstance.box<RecentSearchModel>().removeAll();
      //   // storeInstance.box<YourOtherModel>().removeAll(); // Add other models here if needed
      // });
      AppLogger().info('🗑️ All ObjectBox data cleared successfully!');
    } catch (e, stacktrace) {
      AppLogger().error('❌ Failed to clear ObjectBox data',
          error: e, stackTrace: stacktrace);
      throw DatabaseFailure(
        message: 'Failed to clear ObjectBox data: ${e.toString()}',
        failureType: DatabaseFailureType.unknown,
        stackTrace: stacktrace,
      );
    }
  }

  /// Close ObjectBox Store Safely
  void close() {
    // if (_isInitialized && _store != null) {
    //   try {
    //     _store!.close();
    //     AppLogger().info("✅ ObjectBox Database Closed Successfully!");
    //   } catch (e, stacktrace) {
    //     AppLogger().error("❌ Error Closing Database",
    //         error: e, stackTrace: stacktrace);
    //     throw DatabaseFailure(
    //       message: "❌ Error Closing Database: ${e.toString()}",
    //       failureType: DatabaseFailureType.unknown,
    //       stackTrace: stacktrace,
    //     );
    //   } finally {
    //     _isInitialized = false;
    //     _store = null;
    //   }
    // }
  }
}
