// import 'package:dio/dio.dart';
// import 'package:hodan_demo/core/enums/http_method.dart';
// import 'package:hodan_demo/core/models/user_model.dart';
// import 'package:hodan_demo/core/network/dio_api_client.dart';
// import 'package:hodan_demo/core/constants/url_contsants.dart';
// import 'package:hodan_demo/core/utils/helpers/request_data.dart';

// class AuthenticationRepository {
//   final DioApiClient dioApiClient;

//   AuthenticationRepository({required this.dioApiClient});

//   // register user
//   Future<Response> registerUser({required UserModel user}) async {
//     final response = await dioApiClient.request(
//       method: HttpMethod.post,
//       endPointUrl: UrlConstants.registerUrl,
//       data: RequestData.json(user.toMap()),
//     );
//     return response;
//   }

//   // login user
//   Future<Response> loginUser({required String phoneNumber}) async {
//     final response = await dioApiClient.request(
//       method: HttpMethod.post,
//       endPointUrl: UrlConstants.loginUrl,
//       data: RequestData.json(
//         {
//           'phoneNumber': phoneNumber,
//         },
//       ),
//     );
//     return response;
//   }

//   // update user
//   Future<Response> updateUser({required UserModel user}) async {
//     final response = await dioApiClient.request(
//       method: HttpMethod.put,
//       endPointUrl: UrlConstants.authenticatedUserUrl,
//       data: RequestData.json(
//         user.toMap(),
//       ),
//     );
//     return response;
//   }

//   // get user data
//   Future<Response> getUserData() async {
//     final response = await dioApiClient.request(
//       method: HttpMethod.get,
//       endPointUrl: UrlConstants.authenticatedUserUrl,
//     );
//     return response;
//   }

//   // get users by phone number
//   Future<Response> getUsersByPhoneNumber({required String phoneNumber}) async {
//     if (phoneNumber.isEmpty) {
//       throw ArgumentError("Phone number cannot be empty");
//     }

//     final url = UrlConstants.byPhoneUrl + phoneNumber;
//     final response = await dioApiClient.request(
//       method: HttpMethod.get,
//       // endPointUrl: "${UrlConstants.byPhoneUrl}$phoneNumber",
//       endPointUrl: url,
//     );
//     return response;
//   }

// //!!!!1
// }
