enum SubscriptionPlanType {
  free,
  pro,
  premium,
}

extension SubscriptionPlan on SubscriptionPlanType {
  String get name {
    switch (this) {
      case SubscriptionPlanType.free:
        return 'Free Plan';
      case SubscriptionPlanType.pro:
        return 'Pro Plan';
      case SubscriptionPlanType.premium:
        return 'Premium Plan';
    }
  }

  String get type {
    switch (this) {
      case SubscriptionPlanType.free:
        return 'Free';
      case SubscriptionPlanType.pro:
        return 'Pro';
      case SubscriptionPlanType.premium:
        return 'Premium';
    }
  }

  String get description {
    switch (this) {
      case SubscriptionPlanType.free:
        return 'Basic features for users just starting out.';
      case SubscriptionPlanType.pro:
        return 'Enhanced features for users who want more control.';
      case SubscriptionPlanType.premium:
        return 'All features plus premium support and advanced tools.';
    }
  }

  String get formattedPrice {
    switch (this) {
      case SubscriptionPlanType.free:
        return 'Free';
      case SubscriptionPlanType.pro:
        return '\$1.99/month';
      case SubscriptionPlanType.premium:
        return '\$3.99/month';
    }
  }

  List<String> get features {
    switch (this) {
      case SubscriptionPlanType.free:
        return [
          "Basic expense tracking",
          "Limited categories",
          "Limited income",
          "Ads included",
        ];
      case SubscriptionPlanType.pro:
        return [
          "Unlimited expense tracking",
          "Custom categories",
          "No ads",
        ];
      case SubscriptionPlanType.premium:
        return [
          "All Pro features",
          "24/7 Priority Support",
          "Advanced reporting tools",
          "Advanced budget",
        ];
    }
  }
}
