// ignore_for_file: unreachable_switch_default

enum DatabaseFailureType {
  connectionError,
  readError,
  writeError,
  queryError,
  noDataFound,
  transactionError,
  unknown,
}

/// Extension for DatabaseFailureType to provide user-friendly messages
extension DatabaseFailureTypeEx on DatabaseFailureType {
  String getErrorMessage() {
    switch (this) {
      case DatabaseFailureType.connectionError:
        return 'Unable to connect to the database. Please check your connection.';
      case DatabaseFailureType.readError:
        return 'Failed to read data from the database. Please try again.';
      case DatabaseFailureType.writeError:
        return 'Failed to write data to the database. Please try again.';
      case DatabaseFailureType.queryError:
        return 'An error occurred while executing a database query.';
      case DatabaseFailureType.noDataFound:
        return 'No data found in the database.';
      case DatabaseFailureType.transactionError:
        return 'A database transaction failed. Please try again.';
      case DatabaseFailureType.unknown:
      default:
        return 'An unknown database error occurred. Please try again.';
    }
  }
}
