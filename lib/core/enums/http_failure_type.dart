// ignore_for_file: unreachable_switch_default

enum HttpFailureType {
  badRequest,
  unauthorized,
  forbidden,
  notFound,
  timeout,
  network,
  cancelled,
  clientError,
  serverError,
  unknown,
}

/// Extension for HttpFailureType to provide user-friendly messages
extension HttpFailureTypeEx on HttpFailureType {
  String getErrorMessage() {
    switch (this) {
      case HttpFailureType.badRequest:
        return 'The request was invalid. Please check your input and try again.';
      case HttpFailureType.unauthorized:
        return 'You are not authorized to access this resource. Please log in.';
      case HttpFailureType.forbidden:
        return 'Access to this resource is forbidden.';
      case HttpFailureType.notFound:
        return 'The requested resource was not found.';
      case HttpFailureType.timeout:
        return 'The request timed out. Please check your internet connection and try again.';
      case HttpFailureType.network:
        return 'A network error occurred. Please check your internet connection.';
      case HttpFailureType.cancelled:
        return 'The request was cancelled.';
      case HttpFailureType.clientError:
        return 'An error occurred on the client side. Please try again.';
      case HttpFailureType.serverError:
        return 'A server error occurred. Please try again later.';
      case HttpFailureType.unknown:
      default:
        return 'An unknown error occurred. Please try again.';
    }
  }
}
