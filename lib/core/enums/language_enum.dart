enum Language {
  somali(
    name: 'Somali',
    flag: '🇸🇴',
    languageCode: 'en',
    countryCode: 'GB',
  ),
  english(
    name: 'English',
    flag: '🇬🇧',
    languageCode: 'en',
    countryCode: 'US',
  ),
  arabic(
    name: 'Arabic',
    flag: '🇸🇦',
    languageCode: 'ar',
    countryCode: 'SA',
  );

  final String name;
  final String flag;
  final String languageCode;
  final String countryCode;

  const Language({
    required this.name,
    required this.flag,
    required this.languageCode,
    required this.countryCode,
  });
}
