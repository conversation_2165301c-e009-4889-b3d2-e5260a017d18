import 'dart:io';
import 'package:flutter/material.dart';
import 'package:hodan_hospital/core/utils/helpers/snack_bar_helper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';

/// Service to handle image picker permissions smartly for both Android and iOS
class ImagePickerPermissionService {
  static final ImagePickerPermissionService _instance =
      ImagePickerPermissionService._internal();
  factory ImagePickerPermissionService() => _instance;
  ImagePickerPermissionService._internal();

  final ImagePicker _picker = ImagePicker();

  /// Pick image from camera with smart permission handling
  Future<XFile?> pickFromCamera({
    required BuildContext context,
    double? maxWidth,
    double? maxHeight,
    int? imageQuality,
  }) async {
    try {
      // Check and request camera permission
      final hasPermission = await _requestCameraPermission(context);
      if (!hasPermission) {
        return null;
      }

      // Pick image from camera
      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: maxWidth,
        maxHeight: maxHeight,
        imageQuality: imageQuality ?? 85,
      );

      return image;
    } catch (e) {
      if (context.mounted) {
        _showErrorSnackBar(context, 'Failed to capture image: ${e.toString()}');
      }
      return null;
    }
  }

  /// Pick image from gallery with smart permission handling
  Future<XFile?> pickFromGallery({
    required BuildContext context,
    double? maxWidth,
    double? maxHeight,
    int? imageQuality,
  }) async {
    try {
      // Check and request gallery permission
      final hasPermission = await _requestGalleryPermission(context);
      if (!hasPermission) {
        return null;
      }

      // Pick image from gallery
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: maxWidth,
        maxHeight: maxHeight,
        imageQuality: imageQuality ?? 85,
      );

      return image;
    } catch (e) {
      if (context.mounted) {
        _showErrorSnackBar(context, 'Failed to select image: ${e.toString()}');
      }
      return null;
    }
  }

  /// Request camera permission with smart handling for both platforms
  Future<bool> _requestCameraPermission(BuildContext context) async {
    try {
      final Permission permission = Permission.camera;

      // Check current permission status
      PermissionStatus status = await permission.status;

      if (status.isGranted) {
        return true;
      }

      if (status.isDenied) {
        // Request permission
        status = await permission.request();

        if (status.isGranted) {
          return true;
        }

        if (status.isDenied && context.mounted) {
          _showPermissionDeniedDialog(
            context,
            'Camera Permission Required',
            'This app needs camera access to take photos. Please grant camera permission.',
            permission,
          );
          return false;
        }
      }

      if (status.isPermanentlyDenied && context.mounted) {
        _showPermissionPermanentlyDeniedDialog(
          context,
          'Camera Permission Required',
          'Camera access has been permanently denied. Please enable it in app settings.',
        );
        return false;
      }

      return false;
    } catch (e) {
      if (context.mounted) {
        _showErrorSnackBar(context, 'Permission error: ${e.toString()}');
      }
      return false;
    }
  }

  /// Request gallery permission with smart handling for both platforms
  Future<bool> _requestGalleryPermission(BuildContext context) async {
    try {
      Permission permission;

      // Different permissions for different Android versions and iOS
      if (Platform.isAndroid) {
        // For Android 13+ (API 33+), use more specific permissions
        if (await _isAndroid13OrHigher()) {
          permission = Permission.photos;
        } else {
          // For older Android versions
          permission = Permission.storage;
        }
      } else {
        // iOS uses photos permission
        permission = Permission.photos;
      }

      // Check current permission status
      PermissionStatus status = await permission.status;

      if (status.isGranted) {
        return true;
      }

      if (status.isDenied) {
        // Request permission
        status = await permission.request();

        if (status.isGranted) {
          return true;
        }

        if (status.isDenied && context.mounted) {
          _showPermissionDeniedDialog(
            context,
            'Photo Library Permission Required',
            'This app needs access to your photo library to select images. Please grant photo library permission.',
            permission,
          );
          return false;
        }
      }

      if (status.isPermanentlyDenied && context.mounted) {
        _showPermissionPermanentlyDeniedDialog(
          context,
          'Photo Library Permission Required',
          'Photo library access has been permanently denied. Please enable it in app settings.',
        );
        return false;
      }

      return false;
    } catch (e) {
      if (context.mounted) {
        _showErrorSnackBar(context, 'Permission error: ${e.toString()}');
      }
      return false;
    }
  }

  /// Check if Android version is 13 or higher (API 33+)
  Future<bool> _isAndroid13OrHigher() async {
    if (!Platform.isAndroid) return false;

    try {
      // This is a simplified check - in a real app you might want to use
      // device_info_plus package for more accurate version detection
      return true; // Assume modern Android for now
    } catch (e) {
      return false;
    }
  }

  /// Show permission denied dialog with option to retry
  void _showPermissionDeniedDialog(
    BuildContext context,
    String title,
    String message,
    Permission permission,
  ) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await permission.request();
              },
              child: const Text('Retry'),
            ),
          ],
        );
      },
    );
  }

  /// Show permanently denied dialog with option to open settings
  void _showPermissionPermanentlyDeniedDialog(
    BuildContext context,
    String title,
    String message,
  ) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await openAppSettings();
              },
              child: const Text('Open Settings'),
            ),
          ],
        );
      },
    );
  }

  /// Show error snackbar
  void _showErrorSnackBar(BuildContext context, String message) {
    SnackBarHelper.showErrorSnackBar(context, message: message);
  }

  /// Check if camera permission is granted
  Future<bool> isCameraPermissionGranted() async {
    return await Permission.camera.isGranted;
  }

  /// Check if gallery permission is granted
  Future<bool> isGalleryPermissionGranted() async {
    if (Platform.isAndroid) {
      if (await _isAndroid13OrHigher()) {
        return await Permission.photos.isGranted;
      } else {
        return await Permission.storage.isGranted;
      }
    } else {
      return await Permission.photos.isGranted;
    }
  }
}
