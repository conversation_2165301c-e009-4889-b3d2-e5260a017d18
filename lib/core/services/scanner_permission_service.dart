import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';

/// Service to handle scanner/camera permissions smartly for QR code scanning
class ScannerPermissionService {
  static final ScannerPermissionService _instance =
      ScannerPermissionService._internal();
  factory ScannerPermissionService() => _instance;
  ScannerPermissionService._internal();

  /// Check and request camera permission for scanning
  /// Returns true if permission is granted, false otherwise
  Future<bool> checkAndRequestCameraPermission({
    required BuildContext context,
    bool showDialogs = true,
  }) async {
    try {
      final Permission permission = Permission.camera;

      // Check current permission status
      PermissionStatus status = await permission.status;

      // If already granted, return true
      if (status.isGranted) {
        return true;
      }

      // If permission is denied (first time or user said no), try to request
      if (status.isDenied) {
        status = await permission.request();

        if (status.isGranted) {
          return true;
        }

        // If still denied after request, show explanation dialog
        if (status.isDenied && showDialogs && context.mounted) {
          _showPermissionDeniedDialog(context);
          return false;
        }
      }

      // If permanently denied, show settings dialog
      if (status.isPermanentlyDenied && showDialogs && context.mounted) {
        _showPermissionPermanentlyDeniedDialog(context);
        return false;
      }

      // Handle restricted status (iOS parental controls)
      if (status.isRestricted && showDialogs && context.mounted) {
        _showPermissionRestrictedDialog(context);
        return false;
      }

      return false;
    } catch (e) {
      if (showDialogs && context.mounted) {
        _showErrorDialog(context, 'Permission error: ${e.toString()}');
      }
      return false;
    }
  }

  /// Check if camera permission is currently granted
  Future<bool> isCameraPermissionGranted() async {
    try {
      final status = await Permission.camera.status;
      return status.isGranted;
    } catch (e) {
      return false;
    }
  }

  /// Check permission status without requesting
  Future<PermissionStatus> getCameraPermissionStatus() async {
    try {
      return await Permission.camera.status;
    } catch (e) {
      return PermissionStatus.denied;
    }
  }

  /// Show dialog when permission is denied but can be requested again
  void _showPermissionDeniedDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Camera Permission Required'),
          content: const Text(
            'This app needs camera access to scan QR codes. Please grant camera permission to continue.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () async {
                Navigator.of(context).pop();
                // Try requesting permission again
                await Permission.camera.request();
              },
              child: const Text('Grant Permission'),
            ),
          ],
        );
      },
    );
  }

  /// Show dialog when permission is permanently denied
  void _showPermissionPermanentlyDeniedDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Camera Permission Required'),
          content: const Text(
            'Camera access has been permanently denied. Please enable it in app settings to scan QR codes.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await openAppSettings();
              },
              child: const Text('Open Settings'),
            ),
          ],
        );
      },
    );
  }

  /// Show dialog when permission is restricted (iOS parental controls)
  void _showPermissionRestrictedDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Camera Access Restricted'),
          content: const Text(
            'Camera access is restricted on this device. Please check your device restrictions or parental controls.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  /// Show error dialog
  void _showErrorDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Error'),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  /// Get user-friendly permission status message
  String getPermissionStatusMessage(PermissionStatus status) {
    switch (status) {
      case PermissionStatus.granted:
        return 'Camera permission is granted';
      case PermissionStatus.denied:
        return 'Camera permission is denied';
      case PermissionStatus.permanentlyDenied:
        return 'Camera permission is permanently denied. Please enable it in settings.';
      case PermissionStatus.restricted:
        return 'Camera access is restricted on this device';
      case PermissionStatus.limited:
        return 'Camera permission is limited';
      case PermissionStatus.provisional:
        return 'Camera permission is provisional';
    }
  }
}
