import 'dart:convert';

import 'package:fpdart/fpdart.dart';
import 'package:hodan_hospital/core/services/flutter_secure_storage_services.dart';

import '../config/enviroment/enviroment_config.dart';
import '../config/logger/app_logger.dart';
import '../constants/api_end_points.dart';
import '../enums/http_failure_type.dart';
import '../enums/http_method.dart';
import '../errors/app_failure.dart';
import '../network/api_client/dio_api_client.dart';
import '../utils/helpers/request_data.dart';

class SmsServices {
  final FlutterSecureStorageServices flutterSecureStorageServices;
  final DioApiClient dioApiClient;

  SmsServices({
    required this.flutterSecureStorageServices,
    required this.dioApiClient,
  });

  // get access token
  FutureEitherFailOr<String> _getAccessToken() async {
    try {
      final tokenJson = await flutterSecureStorageServices.readData(
        key: EnvironmentConfig.smsCacheKey,
      );

      if (tokenJson != null) {
        final tokenMap = jsonDecode(tokenJson);
        final token = tokenMap['access_token'];
        final expiresAt = DateTime.tryParse(tokenMap['expires_at'] ?? '');
        if (token != null &&
            expiresAt != null &&
            DateTime.now().isBefore(expiresAt)) {
          return right(token);
        }
      }

      final response = await dioApiClient.request(
          method: HttpMethod.post,
          endPointUrl: ApiEndpoints.getAccessToken,
          data: RequestData.json({
            'grant_type': 'password',
            'username': EnvironmentConfig.smsUsername,
            'password': EnvironmentConfig.smsPassword,
          }),
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          });

      if (response.data is Map<String, dynamic>) {
        final responseData = response.data as Map<String, dynamic>;
        final accessToken = responseData['access_token'];
        final responseCode = response.statusCode;
        if (responseCode != 200) {
          return left(HttpFailure(
            message: 'Failed to get access token',
            failureType: HttpFailureType.serverError,
            apiMessage:
                'Failed to get access token, status code: $responseCode',
            statusCode: responseCode,
          ));
        }

        if (accessToken != null) {
          await flutterSecureStorageServices.storeData(
            key: EnvironmentConfig.smsCacheKey,
            value: jsonEncode({
              'access_token': accessToken,
              'expires_at': DateTime.now()
                  .add(const Duration(minutes: 55))
                  .toIso8601String(), // Safe margin
            }),
          );
          return right(accessToken);
        }
      }

      return left(HttpFailure(
        message: 'Failed to get access token',
        failureType: HttpFailureType.serverError,
        apiMessage: response.data['error_description']?.toString(),
      ));
    } catch (e, s) {
      AppLogger().error(
        'Error getting access token : $e',
        error: e,
        stackTrace: s,
      );
      return left(HttpFailure(
        message: 'Failed to get access token : $e',
        failureType: HttpFailureType.serverError,
      ));
    }
  }

  FutureEitherFailOr<String> sendSMS({
    required String mobileNumber,
    required String message,
  }) async {
    try {
      // Step 1: Get access token (cached or fresh)
      final tokenResult = await _getAccessToken();

      return await tokenResult.fold(
        (failure) {
          AppLogger().error(
            '❌ Failed to retrieve access token: ${failure.getErrorMessage()}',
          );
          return left(failure);
        },
        (accessToken) async {
          // Step 2: Prepare SMS request
          final response = await dioApiClient.request(
            method: HttpMethod.post,
            endPointUrl: ApiEndpoints.sendSMS,
            headers: {
              'Authorization': 'Bearer $accessToken',
              'Content-Type': 'application/json',
            },
            data: RequestData.json({
              'refid': 'flutter-${DateTime.now().millisecondsSinceEpoch}',
              'mobile': mobileNumber,
              'message': message,
              'senderid': EnvironmentConfig.smsSenderId,
              'validity': 5,
            }),
          );

          // Step 3: Validate response
          if (response.data is Map<String, dynamic>) {
            final responseData = response.data as Map<String, dynamic>;
            final rawResponseCode = responseData['ResponseCode'];
            final responseCode =
                int.tryParse(rawResponseCode.toString()) ?? 500;
            final description =
                responseData['ResponseMessage'] ?? 'Message sent';

            if (responseCode == 200) {
              AppLogger().info(
                '📨 SMS sent to $mobileNumber: $description',
              );
              return right(description.toString());
            } else {
              AppLogger().error(
                '❌ SMS failed [API Error]: $description',
              );
              return left(HttpFailure(
                message: 'Failed to send SMS',
                failureType: HttpFailureType.serverError,
                apiMessage: description.toString(),
                statusCode: responseCode,
              ));
            }
          }

          // Step 4: Handle unexpected response
          AppLogger().error(
            '❌ SMS failed: Unexpected response format: ${response.data}',
          );
          return left(HttpFailure(
            message: 'Failed to send SMS: Unexpected response format',
            failureType: HttpFailureType.unknown,
          ));
        },
      );
    } catch (e, s) {
      AppLogger().error(
        '❌ SMS sending exception: $e',
        error: e,
        stackTrace: s,
      );
      return left(HttpFailure(
        message: 'Failed to send SMS: $e',
        failureType: HttpFailureType.serverError,
        stackTrace: s,
      ));
    }
  }
}
