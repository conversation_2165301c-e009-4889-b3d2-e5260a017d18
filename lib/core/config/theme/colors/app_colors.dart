import 'package:flutter/material.dart';

class CustomColors extends ThemeExtension<CustomColors> {
  final Color primary;
  final Color secondary;
  final Color background;
  final Color surface;
  final Color button;
  final Color text;
  final Color subtext;
  final Color card;
  final Color sideMenu;
  final Color divider;
  final Color error;
  final Color success;
  final Color warning;
  final Color info;
  final Color loading;
  final Color confirmColor;
  final Color link;
  final Color shimmerBase;
  final Color shimmerHighlight;
  final Color white;
  final Color black;
  final Color disabled;
  final Color transparent;

  CustomColors({
    required this.primary,
    required this.secondary,
    required this.background,
    required this.surface,
    required this.button,
    required this.text,
    required this.subtext,
    required this.sideMenu,
    required this.card,
    required this.divider,
    required this.error,
    required this.success,
    required this.warning,
    required this.info,
    required this.loading,
    required this.confirmColor,
    required this.link,
    required this.shimmerBase,
    required this.shimmerHighlight,
    required this.white,
    required this.black,
    required this.disabled,
    required this.transparent,
  });

  static CustomColors light = CustomColors(
    primary: const Color(0xFFF3A01D), // 0xFFF3A01D
    secondary: const Color(0xFF3A4349),
    background: const Color(0xFFF5F5F5),
    surface: const Color(0xFFEBEBEB),
    button: const Color(0xFFE89B21),
    sideMenu: const Color(0xFFE8F0FE),
    text: const Color(0xFF333333),
    subtext: const Color(0xFF8D8D8D),
    card: const Color(0xFFFFFFFF),
    divider: const Color(0xFFE0E0E0),
    error: const Color(0xFFFF4D6E),
    success: const Color(0xFF52D17F),
    warning: const Color(0xFF00A6B3),
    info: const Color(0xFFFFE25D),
    loading: const Color(0xFF4E8BFF),
    confirmColor: const Color(0xFF55D9D0),
    link: const Color(0xFF00B2E7),
    shimmerBase: const Color(0xFFE0E0E0),
    shimmerHighlight: const Color(0xFFF5F5F5),
    white: const Color(0xFFFFFFFF),
    black: const Color(0xFF000000),
    disabled: const Color(0xFFBDBDBD),
    transparent: const Color(0x00000000),
  );

  static CustomColors dark = CustomColors(
    primary: const Color(0xFFF3A01D),
    secondary: const Color(0xFF3A4349),
    // background: const Color(0xFF3A434A),
    background: const Color(0xFF1E1E1E),
    surface: const Color(0xFF2C2C2C),
    button: const Color(0xFFE89B21),
    sideMenu: const Color(0xFF2F343A),
    text: const Color(0xFFE0E0E0),
    subtext: const Color(0xFF8D8D8D),
    card: const Color(0xFF2C2F38),
    divider: const Color(0xFF303030),
    error: const Color(0xFF9C0027),
    success: const Color(0xFF087A3C),
    warning: const Color(0xFF006F74),
    info: const Color(0xFFF5C23A),
    loading: const Color(0xFF0077B5),
    confirmColor: const Color(0xFF1E8B8A),
    link: const Color(0xFF00B2E7),
    shimmerBase: const Color(0xFF3A3A3A),
    shimmerHighlight: const Color(0xFF606060),
    white: const Color(0xFFFFFFFF),
    black: const Color(0xFF000000),
    disabled: const Color(0xFF757575),
    transparent: const Color(0x00000000),
  );

  @override
  ThemeExtension<CustomColors> copyWith({CustomColors? theme}) {
    return CustomColors(
      primary: theme?.primary ?? primary,
      secondary: theme?.secondary ?? secondary,
      background: theme?.background ?? background,
      surface: theme?.surface ?? surface,
      button: theme?.button ?? button,
      text: theme?.text ?? text,
      subtext: theme?.subtext ?? subtext,
      card: theme?.card ?? card,
      divider: theme?.divider ?? divider,
      error: theme?.error ?? error,
      success: theme?.success ?? success,
      warning: theme?.warning ?? warning,
      info: theme?.info ?? info,
      loading: theme?.loading ?? loading,
      confirmColor: theme?.confirmColor ?? confirmColor,
      sideMenu: theme?.sideMenu ?? sideMenu,
      link: theme?.link ?? link,
      shimmerBase: theme?.shimmerBase ?? shimmerBase,
      shimmerHighlight: theme?.shimmerHighlight ?? shimmerHighlight,
      white: theme?.white ?? white,
      black: theme?.black ?? black,
      disabled: theme?.disabled ?? disabled,
      transparent: theme?.transparent ?? transparent,
    );
  }

  @override
  ThemeExtension<CustomColors> lerp(
      covariant ThemeExtension<CustomColors>? other, double t) {
    if (other is! CustomColors) {
      return this;
    }

    return CustomColors(
      primary: Color.lerp(primary, other.primary, t) ?? primary,
      secondary: Color.lerp(secondary, other.secondary, t) ?? secondary,
      background: Color.lerp(background, other.background, t) ?? background,
      surface: Color.lerp(surface, other.surface, t) ?? surface,
      button: Color.lerp(button, other.button, t) ?? button,
      sideMenu: Color.lerp(sideMenu, other.sideMenu, t) ?? sideMenu,
      text: Color.lerp(text, other.text, t) ?? text,
      subtext: Color.lerp(subtext, other.subtext, t) ?? subtext,
      card: Color.lerp(card, other.card, t) ?? card,
      divider: Color.lerp(divider, other.divider, t) ?? divider,
      error: Color.lerp(error, other.error, t) ?? error,
      success: Color.lerp(success, other.success, t) ?? success,
      warning: Color.lerp(warning, other.warning, t) ?? warning,
      info: Color.lerp(info, other.info, t) ?? info,
      loading: Color.lerp(loading, other.loading, t) ?? loading,
      confirmColor:
          Color.lerp(confirmColor, other.confirmColor, t) ?? confirmColor,
      link: Color.lerp(link, other.link, t) ?? link,
      shimmerBase: Color.lerp(shimmerBase, other.shimmerBase, t) ?? shimmerBase,
      shimmerHighlight:
          Color.lerp(shimmerHighlight, other.shimmerHighlight, t) ??
              shimmerHighlight,
      white: Color.lerp(white, other.white, t) ?? white,
      black: Color.lerp(black, other.black, t) ?? black,
      disabled: Color.lerp(disabled, other.disabled, t) ?? disabled,
      transparent: Color.lerp(transparent, other.transparent, t) ?? transparent,
    );
  }
}
