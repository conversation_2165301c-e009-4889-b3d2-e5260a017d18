// import 'package:flutter/material.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:google_fonts/google_fonts.dart';
// import 'package:hodan_hospital/core/config/theme/colors/app_colors.dart';

// class CustomTextTheme {
//   // avoid making instance
//   CustomTextTheme._();

//   // Light text theme
//   static TextTheme light = TextTheme(
//     displayLarge: GoogleFonts.poppins(
//       fontSize: 57.sp, // Adaptable size using ScreenUtil
//       fontWeight: FontWeight.w700, // Bold style
//       // letterSpacing: -1.5,
//       color: CustomColors.light.text,
//     ),
//     displayMedium: GoogleFonts.poppins(
//       fontSize: 45.sp,
//       fontWeight: FontWeight.w700,
//       // letterSpacing: -0.5,
//       color: CustomColors.light.text,
//     ),
//     displaySmall: GoogleFonts.poppins(
//       fontSize: 36.sp,
//       fontWeight: FontWeight.w700,
//       color: CustomColors.light.text,
//     ),
//     headlineLarge: GoogleFonts.poppins(
//       fontSize: 32.sp,
//       fontWeight: FontWeight.w600, // Slightly lighter than bold
//       // letterSpacing: 0.0,
//       color: CustomColors.light.text,
//     ),
//     headlineMedium: GoogleFonts.poppins(
//       fontSize: 28.sp,
//       fontWeight: FontWeight.w500,
//       // letterSpacing: 0.0,
//       color: CustomColors.light.text,
//     ),
//     headlineSmall: GoogleFonts.poppins(
//       fontSize: 24.sp,
//       fontWeight: FontWeight.w500,
//       // letterSpacing: 0.15,
//       color: CustomColors.light.text,
//     ),
//     titleLarge: GoogleFonts.poppins(
//       fontSize: 22.sp,
//       fontWeight: FontWeight.w900,
//       // letterSpacing: 0.0,
//       color: CustomColors.light.text,
//     ),
//     titleMedium: GoogleFonts.poppins(
//       fontSize: 16.sp,
//       fontWeight: FontWeight.w800,
//       // letterSpacing: 0.15,
//       color: CustomColors.light.text,
//     ),
//     titleSmall: GoogleFonts.poppins(
//       fontSize: 14.sp,
//       fontWeight: FontWeight.w800,
//       // letterSpacing: 0.1,
//       color: CustomColors.light.text,
//     ),
//     bodyLarge: GoogleFonts.poppins(
//       fontSize: 16.sp,
//       fontWeight: FontWeight.normal,
//       // letterSpacing: 0.5,
//       color: CustomColors.light.text,
//     ),
//     bodyMedium: GoogleFonts.poppins(
//       fontSize: 14.sp,
//       fontWeight: FontWeight.normal,
//       // letterSpacing: 0.25,
//       color: CustomColors.light.text,
//     ),
//     bodySmall: GoogleFonts.poppins(
//       fontSize: 12.sp,
//       fontWeight: FontWeight.normal,
//       // letterSpacing: 0.4,
//       color: CustomColors.light.text,
//     ),
//     labelLarge: GoogleFonts.poppins(
//       fontSize: 14.sp,
//       fontWeight: FontWeight.w600,
//       // letterSpacing: 0.1,
//       color: CustomColors.light.subtext,
//     ),
//     labelMedium: GoogleFonts.poppins(
//       fontSize: 12.sp,
//       fontWeight: FontWeight.w500,
//       // letterSpacing: 0.5,
//       color: CustomColors.light.subtext,
//     ),
//     labelSmall: GoogleFonts.poppins(
//       fontSize: 10.sp,
//       fontWeight: FontWeight.w500,
//       // letterSpacing: 0.5,
//       color: CustomColors.light.subtext,
//     ),
//   );

//   // Dark text theme
//   static TextTheme dark = TextTheme(
//     displayLarge: GoogleFonts.poppins(
//       fontSize: 57.sp,
//       fontWeight: FontWeight.w700,
//       // letterSpacing: -1.5,
//       color: CustomColors.dark.text,
//     ),
//     displayMedium: GoogleFonts.poppins(
//       fontSize: 45.sp,
//       fontWeight: FontWeight.w700,
//       // letterSpacing: -0.5,
//       color: CustomColors.dark.text,
//     ),
//     displaySmall: GoogleFonts.poppins(
//       fontSize: 36.sp,
//       fontWeight: FontWeight.w700,
//       color: CustomColors.dark.text,
//     ),
//     headlineLarge: GoogleFonts.poppins(
//       fontSize: 32.sp,
//       fontWeight: FontWeight.w600,
//       // letterSpacing: 0.0,
//       color: CustomColors.dark.text,
//     ),
//     headlineMedium: GoogleFonts.poppins(
//       fontSize: 28.sp,
//       fontWeight: FontWeight.w500,
//       // letterSpacing: 0.0,
//       color: CustomColors.dark.text,
//     ),
//     headlineSmall: GoogleFonts.poppins(
//       fontSize: 24.sp,
//       fontWeight: FontWeight.w500,
//       // letterSpacing: 0.15,
//       color: CustomColors.dark.text,
//     ),
//     titleLarge: GoogleFonts.poppins(
//       fontSize: 22.sp,
//       fontWeight: FontWeight.w600,
//       // letterSpacing: 0.0,
//       color: CustomColors.dark.text,
//     ),
//     titleMedium: GoogleFonts.poppins(
//       fontSize: 16.sp,
//       fontWeight: FontWeight.w500,
//       // letterSpacing: 0.15,
//       color: CustomColors.dark.text,
//     ),
//     titleSmall: GoogleFonts.poppins(
//       fontSize: 14.sp,
//       fontWeight: FontWeight.w500,
//       // letterSpacing: 0.1,
//       color: CustomColors.dark.text,
//     ),
//     bodyLarge: GoogleFonts.poppins(
//       fontSize: 16.sp,
//       fontWeight: FontWeight.normal,
//       // letterSpacing: 0.5,
//       color: CustomColors.dark.text,
//     ),
//     bodyMedium: GoogleFonts.poppins(
//       fontSize: 14.sp,
//       fontWeight: FontWeight.normal,
//       // letterSpacing: 0.25,
//       color: CustomColors.dark.text,
//     ),
//     bodySmall: GoogleFonts.poppins(
//       fontSize: 12.sp,
//       fontWeight: FontWeight.normal,
//       // letterSpacing: 0.4,
//       color: CustomColors.dark.text,
//     ),
//     labelLarge: GoogleFonts.poppins(
//       fontSize: 14.sp,
//       fontWeight: FontWeight.w600,
//       // letterSpacing: 0.1,
//       color: CustomColors.dark.subtext,
//     ),
//     labelMedium: GoogleFonts.poppins(
//       fontSize: 12.sp,
//       fontWeight: FontWeight.w500,
//       // letterSpacing: 0.5,
//       color: CustomColors.dark.subtext,
//     ),
//     labelSmall: GoogleFonts.poppins(
//       fontSize: 10.sp,
//       fontWeight: FontWeight.w500,
//       // letterSpacing: 0.5,
//       color: CustomColors.dark.subtext,
//     ),
//   );
// }

//!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hodan_hospital/core/config/theme/colors/app_colors.dart';

class CustomTextTheme {
  // avoid making instance
  CustomTextTheme._();

  // Light text theme
  static TextTheme light = TextTheme(
    displayLarge: TextStyle(
      fontSize: 57.sp, // Adaptable size using ScreenUtil
      fontWeight: FontWeight.w700, // Bold style
      // letterSpacing: -1.5,
      color: CustomColors.light.text,
    ),
    displayMedium: TextStyle(
      fontSize: 45.sp,
      fontWeight: FontWeight.w700,
      // letterSpacing: -0.5,
      color: CustomColors.light.text,
    ),
    displaySmall: TextStyle(
      fontSize: 36.sp,
      fontWeight: FontWeight.w700,
      color: CustomColors.light.text,
    ),
    headlineLarge: TextStyle(
      fontSize: 32.sp,
      fontWeight: FontWeight.w600, // Slightly lighter than bold
      // letterSpacing: 0.0,
      color: CustomColors.light.text,
    ),
    headlineMedium: TextStyle(
      fontSize: 28.sp,
      fontWeight: FontWeight.w500,
      // letterSpacing: 0.0,
      color: CustomColors.light.text,
    ),
    headlineSmall: TextStyle(
      fontSize: 24.sp,
      fontWeight: FontWeight.w500,
      // letterSpacing: 0.15,
      color: CustomColors.light.text,
    ),
    titleLarge: TextStyle(
      fontSize: 22.sp,
      fontWeight: FontWeight.w900,
      // letterSpacing: 0.0,
      color: CustomColors.light.text,
    ),
    titleMedium: TextStyle(
      fontSize: 16.sp,
      fontWeight: FontWeight.w800,
      // letterSpacing: 0.15,
      color: CustomColors.light.text,
    ),
    titleSmall: TextStyle(
      fontSize: 14.sp,
      fontWeight: FontWeight.w800,
      // letterSpacing: 0.1,
      color: CustomColors.light.text,
    ),
    bodyLarge: TextStyle(
      fontSize: 16.sp,
      fontWeight: FontWeight.normal,
      // letterSpacing: 0.5,
      color: CustomColors.light.text,
    ),
    bodyMedium: TextStyle(
      fontSize: 14.sp,
      fontWeight: FontWeight.normal,
      // letterSpacing: 0.25,
      color: CustomColors.light.text,
    ),
    bodySmall: TextStyle(
      fontSize: 12.sp,
      fontWeight: FontWeight.normal,
      // letterSpacing: 0.4,
      color: CustomColors.light.text,
    ),
    labelLarge: TextStyle(
      fontSize: 14.sp,
      fontWeight: FontWeight.w600,
      // letterSpacing: 0.1,
      color: CustomColors.light.subtext,
    ),
    labelMedium: TextStyle(
      fontSize: 12.sp,
      fontWeight: FontWeight.w500,
      // letterSpacing: 0.5,
      color: CustomColors.light.subtext,
    ),
    labelSmall: TextStyle(
      fontSize: 10.sp,
      fontWeight: FontWeight.w500,
      // letterSpacing: 0.5,
      color: CustomColors.light.subtext,
    ),
  );

  // Dark text theme
  static TextTheme dark = TextTheme(
    displayLarge: TextStyle(
      fontSize: 57.sp,
      fontWeight: FontWeight.w700,
      // letterSpacing: -1.5,
      color: CustomColors.dark.text,
    ),
    displayMedium: TextStyle(
      fontSize: 45.sp,
      fontWeight: FontWeight.w700,
      // letterSpacing: -0.5,
      color: CustomColors.dark.text,
    ),
    displaySmall: TextStyle(
      fontSize: 36.sp,
      fontWeight: FontWeight.w700,
      color: CustomColors.dark.text,
    ),
    headlineLarge: TextStyle(
      fontSize: 32.sp,
      fontWeight: FontWeight.w600,
      // letterSpacing: 0.0,
      color: CustomColors.dark.text,
    ),
    headlineMedium: TextStyle(
      fontSize: 28.sp,
      fontWeight: FontWeight.w500,
      // letterSpacing: 0.0,
      color: CustomColors.dark.text,
    ),
    headlineSmall: TextStyle(
      fontSize: 24.sp,
      fontWeight: FontWeight.w500,
      // letterSpacing: 0.15,
      color: CustomColors.dark.text,
    ),
    titleLarge: TextStyle(
      fontSize: 22.sp,
      fontWeight: FontWeight.w600,
      // letterSpacing: 0.0,
      color: CustomColors.dark.text,
    ),
    titleMedium: TextStyle(
      fontSize: 16.sp,
      fontWeight: FontWeight.w500,
      // letterSpacing: 0.15,
      color: CustomColors.dark.text,
    ),
    titleSmall: TextStyle(
      fontSize: 14.sp,
      fontWeight: FontWeight.w500,
      // letterSpacing: 0.1,
      color: CustomColors.dark.text,
    ),
    bodyLarge: TextStyle(
      fontSize: 16.sp,
      fontWeight: FontWeight.normal,
      // letterSpacing: 0.5,
      color: CustomColors.dark.text,
    ),
    bodyMedium: TextStyle(
      fontSize: 14.sp,
      fontWeight: FontWeight.normal,
      // letterSpacing: 0.25,
      color: CustomColors.dark.text,
    ),
    bodySmall: TextStyle(
      fontSize: 12.sp,
      fontWeight: FontWeight.normal,
      // letterSpacing: 0.4,
      color: CustomColors.dark.text,
    ),
    labelLarge: TextStyle(
      fontSize: 14.sp,
      fontWeight: FontWeight.w600,
      // letterSpacing: 0.1,
      color: CustomColors.dark.subtext,
    ),
    labelMedium: TextStyle(
      fontSize: 12.sp,
      fontWeight: FontWeight.w500,
      // letterSpacing: 0.5,
      color: CustomColors.dark.subtext,
    ),
    labelSmall: TextStyle(
      fontSize: 10.sp,
      fontWeight: FontWeight.w500,
      // letterSpacing: 0.5,
      color: CustomColors.dark.subtext,
    ),
  );
}
