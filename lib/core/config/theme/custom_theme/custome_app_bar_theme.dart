import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:hodan_hospital/core/config/theme/colors/app_colors.dart';

class CustomAppBarTheme {
  CustomAppBarTheme._(); // to avoid creating instance

  // light mode app bar theme
  static AppBarTheme light = AppBarTheme(
    backgroundColor: CustomColors.light.primary,
    foregroundColor: CustomColors.light.primary,
    systemOverlayStyle: SystemUiOverlayStyle(
      statusBarColor: CustomColors.light.primary,
      statusBarIconBrightness: Brightness.light, // Light icons
      statusBarBrightness: Brightness.dark, // Dark status bar background
    ),
    elevation: 0,
    scrolledUnderElevation: 0,
    // surfaceTintColor: AppColors.transparent,
    surfaceTintColor: CustomColors.light.transparent,
    shadowColor: CustomColors.light.transparent,
  );

  // dark mode app bar theme
  static AppBarTheme dark = AppBarTheme(
    backgroundColor: CustomColors.dark.primary,
    foregroundColor: CustomColors.dark.primary,
    systemOverlayStyle: SystemUiOverlayStyle(
      statusBarColor: CustomColors.light.primary,
      statusBarIconBrightness: Brightness.light, // Light icons
      statusBarBrightness: Brightness.dark, // Dark status bar background
    ),
    elevation: 0,
    scrolledUnderElevation: 0,
    surfaceTintColor: CustomColors.dark.transparent,
    shadowColor: CustomColors.dark.transparent,
  );
}
