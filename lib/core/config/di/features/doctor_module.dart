import 'package:hodan_hospital/core/config/di/dependency_injection.dart';
// import 'package:hodan_hospital/features/doctor/data/datasources/local/doctor_database_manager.dart';
// import 'package:hodan_hospital/features/doctor/data/datasources/local/doctor_local_data_source.dart';
import 'package:hodan_hospital/features/doctor/data/datasources/remote/doctor_remote_data_source.dart';
import 'package:hodan_hospital/features/doctor/data/repositories/doctor_repository_impl.dart';
import 'package:hodan_hospital/features/doctor/domain/repositories/doctor_repository.dart';
import 'package:hodan_hospital/features/doctor/domain/usecases/get_banners_use_case.dart';
import 'package:hodan_hospital/features/doctor/domain/usecases/get_doctor_departments_use_case.dart';
import 'package:hodan_hospital/features/doctor/domain/usecases/get_doctors_by_department_use_case.dart';
import 'package:hodan_hospital/features/doctor/domain/usecases/get_doctors_use_case.dart';
import 'package:hodan_hospital/features/doctor/domain/usecases/get_recent_search_use_case.dart';
import 'package:hodan_hospital/features/doctor/domain/usecases/save_recent_search_use_case.dart';
import 'package:hodan_hospital/features/doctor/domain/usecases/search_doctor_use_case.dart';
import 'package:hodan_hospital/features/doctor/presentation/bloc/doctor_bloc.dart';
import 'package:injectable/injectable.dart';

@module
abstract class DoctorModule {
  /// Register Data Sources (Transient)
  @injectable
  DoctorRemoteDataSource get doctorRemoteDataSource =>
      DoctorRemoteDataSourceImpl(
        dioApiClient: sl(),
        httpErrorHandler: sl(),
      );

  // @injectable
  // DoctorLocalDataSource get doctorLocalDataSource => DoctorLocalDataSourceImpl(
  //       doctorDatabaseManager: sl(),
  //     );

  // /// Register Database Manager (Singleton)
  // @lazySingleton
  // DoctorDatabaseManager get doctorDatabaseManager => DoctorDatabaseManagerImpl(
  //       databaseErrorHandler: sl(),
  //     );

  /// Register Repository (Singleton)
  @lazySingleton
  DoctorRepository get doctorRepository => DoctorRepositoryImpl(
        doctorRemoteDataSource: sl(),
        // doctorLocalDataSource: sl(),
      );

  /// Register Use Cases (Singleton)
  @lazySingleton
  GetDoctorsUseCase get getDoctorsUseCase =>
      GetDoctorsUseCase(repository: sl());

  @lazySingleton
  SearchDoctorUseCase get searchDoctorsUseCase =>
      SearchDoctorUseCase(repository: sl());

  @lazySingleton
  GetBannersUseCase get getBannersUseCase =>
      GetBannersUseCase(repository: sl());

  @lazySingleton
  GetRecentSearchUseCase get getRecentSearchUseCase =>
      GetRecentSearchUseCase(repository: sl());

  @lazySingleton
  SaveRecentSearchUseCase get saveRecentSearchUseCase =>
      SaveRecentSearchUseCase(repository: sl());

  @lazySingleton
  GetDoctorDepartmentsUseCase get getDoctorDepartmentsUseCase =>
      GetDoctorDepartmentsUseCase(repository: sl());

  @lazySingleton
  GetDoctorsByDepartmentUseCase get getDoctorsByDepartmentUseCase =>
      GetDoctorsByDepartmentUseCase(repository: sl());

  /// Register Bloc (Singleton)
  @lazySingleton
  DoctorBloc get doctorBloc => DoctorBloc(
        getDoctorsUseCase: sl(),
        searchDoctorUseCase: sl(),
        getBannersUseCase: sl(),
        getRecentSearchUseCase: sl(),
        saveRecentSearchUseCase: sl(),
        getDoctorDepartmentsUseCase: sl(),
        getDoctorsByDepartmentUseCase: sl(),
      );
}
