import 'package:hodan_hospital/features/authentication/domain/use_cases/can_register_usecase.dart';
import 'package:hodan_hospital/features/authentication/domain/use_cases/complete_onboarding_use_case.dart';
import 'package:injectable/injectable.dart';
import 'package:hodan_hospital/core/errors/http_error_handler.dart';
import 'package:hodan_hospital/core/network/api_client/dio_api_client.dart';
import 'package:hodan_hospital/core/services/flutter_secure_storage_services.dart';
import 'package:hodan_hospital/features/authentication/data/datasources/remote/auth_remote_data_source.dart';
import 'package:hodan_hospital/features/authentication/data/datasources/local/auth_local_data_source.dart';
import 'package:hodan_hospital/features/authentication/data/repositories/auth_repository_impl.dart';
import 'package:hodan_hospital/features/authentication/domain/repositories/auth_repository.dart';
import 'package:hodan_hospital/features/authentication/domain/use_cases/login_use_case.dart';
import 'package:hodan_hospital/features/authentication/domain/use_cases/logout_use_case.dart';
import 'package:hodan_hospital/features/authentication/domain/use_cases/register_use_case.dart';
import 'package:hodan_hospital/features/authentication/domain/use_cases/check_user_authentication_use_case.dart';
import 'package:hodan_hospital/features/authentication/presentation/bloc/authentication_bloc.dart';

import '../../../../features/authentication/domain/use_cases/resend_otp_usecase.dart';
import '../../../../features/authentication/domain/use_cases/verify_otp_use_case.dart';
import '../../../services/sms_services.dart';

@module
abstract class AuthenticationModule {
  /// ✅ Remote Data Source
  @injectable
  AuthRemoteDataSource provideAuthRemoteDataSource(
      DioApiClient dioApiClient, HttpErrorHandler httpErrorHandler) {
    return AuthRemoteDataSourceImpl(
      dioApiClient: dioApiClient,
      httpErrorHandler: httpErrorHandler,
    );
  }

  /// ✅ Local Data Source
  @injectable
  AuthLocalDataSource provideAuthLocalDataSource(
    FlutterSecureStorageServices storageServices,
    // AuthDatabaseManager authDatabaseManager
  ) {
    return AuthLocalDataSourceImpl(
      flutterSecureStorageServices: storageServices,
      // authDatabaseManager: authDatabaseManager,
    );
  }

  // /// ✅ Database Manager (Singleton)
  // @lazySingleton
  // AuthDatabaseManager provideAuthDatabaseManager(
  //     DatabaseErrorHandler databaseErrorHandler) {
  //   return AuthDatabaseManagerImpl(
  //     databaseErrorHandler: databaseErrorHandler,
  //   );
  // }

  /// ✅ Repository (Singleton)
  @lazySingleton
  AuthRepository provideAuthRepository(AuthRemoteDataSource remoteDataSource,
      AuthLocalDataSource localDataSource, SmsServices smsServices) {
    return AuthRepositoryImpl(
      authRemoteDataSource: remoteDataSource,
      authLocalDataSource: localDataSource,
      smsServices: smsServices,
    );
  }

  /// ✅ Use Cases (Singleton)
  @lazySingleton
  LoginUseCase provideLoginUseCase(AuthRepository repository) {
    return LoginUseCase(repository: repository);
  }

  @lazySingleton
  RegisterUseCase provideRegisterUseCase(AuthRepository repository) {
    return RegisterUseCase(repository: repository);
  }

  @lazySingleton
  LogoutUseCase provideLogoutUseCase(AuthRepository repository) {
    return LogoutUseCase(repository: repository);
  }

  @lazySingleton
  CheckUserAuthenticationUseCase provideCheckUserAuthenticationUseCase(
      AuthRepository repository) {
    return CheckUserAuthenticationUseCase(repository: repository);
  }

  @lazySingleton
  CompleteOnboardingUseCase provideCompleteOnboardingUseCase(
      AuthRepository repository) {
    return CompleteOnboardingUseCase(repository: repository);
  }

  @lazySingleton
  ResendOtpUseCase provideSendOtpUseCase(AuthRepository repository) {
    return ResendOtpUseCase(repository: repository);
  }

  @lazySingleton
  VerifyOtpUseCase provideVerifyOtpUseCase(AuthRepository repository) {
    return VerifyOtpUseCase(repository: repository);
  }

  @lazySingleton
  CanRegisterUsecase provideCanRegisterUsecase(AuthRepository repository) {
    return CanRegisterUsecase(repository: repository);
  }

  /// ✅ Authentication Bloc (Singleton)
  @lazySingleton
  AuthenticationBloc provideAuthenticationBloc(
    LoginUseCase loginUseCase,
    RegisterUseCase registerUseCase,
    LogoutUseCase logoutUseCase,
    CheckUserAuthenticationUseCase checkUserAuthenticationUseCase,
    CompleteOnboardingUseCase completeOnboardingUseCase,
    ResendOtpUseCase resendOtpUseCase,
    VerifyOtpUseCase verifyOtpUseCase,
    CanRegisterUsecase canRegisterUseCase,
  ) {
    return AuthenticationBloc(
      loginUseCase: loginUseCase,
      registerUseCase: registerUseCase,
      logoutUseCase: logoutUseCase,
      checkUserAuthenticationUseCase: checkUserAuthenticationUseCase,
      completeOnboardingUseCase: completeOnboardingUseCase,
      resendOtpUseCase: resendOtpUseCase,
      verifyOtpUseCase: verifyOtpUseCase,
      canRegisterUseCase: canRegisterUseCase,
    );
  }
}
