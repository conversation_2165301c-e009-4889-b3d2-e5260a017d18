import 'package:hodan_hospital/core/config/di/dependency_injection.dart';
// import 'package:hodan_hospital/features/Result/data/datasources/local/result_database_manager.dart';
// import 'package:hodan_hospital/features/Result/data/datasources/local/result_local_data_source.dart';
import 'package:hodan_hospital/features/Result/data/datasources/remote/result_remote_data_source.dart';
import 'package:hodan_hospital/features/Result/data/repositories/result_repository_impl.dart';
import 'package:hodan_hospital/features/Result/domain/repositories/result_repository.dart';
import 'package:hodan_hospital/features/Result/domain/usecases/get_lab_results_use_case.dart';
import 'package:hodan_hospital/features/Result/presentation/bloc/result_bloc.dart';
import 'package:injectable/injectable.dart';

import '../../../../features/Result/domain/usecases/generate_lab_result_pdf_usecase.dart';

@module
abstract class ResultModule {
  /// Register Data Sources (Transient)
  @injectable
  ResultRemoteDataSource get resultRemoteDataSource =>
      ResultRemoteDataSourceImpl(
        dioApiClient: sl(),
        httpErrorHandler: sl(),
      );

  // @injectable
  // ResultLocalDataSource get resultLocalDataSource => ResultLocalDataSourceImpl(
  //       resultDatabaseManager: sl(),
  //     );

  // /// Register Database Manager (Singleton)
  // @lazySingleton
  // ResultDatabaseManager get resultDatabaseManager => ResultDatabaseManagerImpl(
  //       databaseErrorHandler: sl(),
  //     );

  /// Register Repository (Singleton)
  @lazySingleton
  ResultRepository get resultRepository => ResultRepositoryImpl(
        // localDataSource: sl(),
        remoteDataSource: sl(),
      );

  /// Register Use Cases (Singleton)
  @lazySingleton
  GetLabResultsUseCase get getLabResultsUseCase =>
      GetLabResultsUseCase(resultRepository: sl());

  @lazySingleton
  GenerateLabResultPdfUsecase get generateLabResultPdfUsecase =>
      GenerateLabResultPdfUsecase(resultRepository: sl());

  /// Register Bloc (Singleton)
  @lazySingleton
  ResultBloc get resultBloc => ResultBloc(
        getLabResultsUseCase: sl(),
        generateLabResultPdfUsecase: sl(),
      );
}
