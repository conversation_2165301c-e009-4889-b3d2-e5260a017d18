import 'package:hodan_hospital/core/config/di/dependency_injection.dart';
// import 'package:hodan_hospital/features/Order/data/datasources/local/order_database_manager.dart';
// import 'package:hodan_hospital/features/Order/data/datasources/local/order_local_data_source.dart';
import 'package:hodan_hospital/features/Order/data/datasources/remote/order_remote_data_source.dart';
import 'package:hodan_hospital/features/Order/data/repositories/order_repository_impl.dart';
import 'package:hodan_hospital/features/Order/domain/repositories/order_repository.dart';
import 'package:hodan_hospital/features/Order/domain/usecases/get_orders_use_case.dart';
import 'package:hodan_hospital/features/Order/domain/usecases/process_order_use_case.dart';
import 'package:hodan_hospital/features/Order/presentation/bloc/order_bloc.dart';
import 'package:injectable/injectable.dart';

@module
abstract class OrderModule {
  /// Register Data Sources (Transient)
  @injectable
  OrderRemoteDataSource get orderRemoteDataSource => OrderRemoteDataSourceImpl(
        dioApiClient: sl(),
        httpErrorHandler: sl(),
      );

  // @injectable
  // OrderLocalDataSource get orderLocalDataSource => OrderLocalDataSourceImpl(
  //       orderDatabaseManager: sl(),
  //     );

  // /// Register Database Manager (Singleton)
  // @lazySingleton
  // OrderDatabaseManager get orderDatabaseManager => OrderDatabaseManagerImpl(
  //       databaseErrorHandler: sl(),
  //     );

  /// Register Repository (Singleton)
  @lazySingleton
  OrderRepository get orderRepository => OrderRepositoryImpl(
        // localDataSource: sl(),
        remoteDataSource: sl(),
      );

  /// Register Use Cases (Singleton)
  @lazySingleton
  GetOrdersUseCase get getOrdersUseCase =>
      GetOrdersUseCase(orderRepository: sl());
  @lazySingleton
  ProcessOrderUseCase get processOrderUseCase =>
      ProcessOrderUseCase(orderRepository: sl());

  /// Register Bloc (Singleton)
  @lazySingleton
  OrderBloc get orderBloc => OrderBloc(
        getOrdersUseCase: sl(),
        processOrderUseCase: sl(),
      );
}
