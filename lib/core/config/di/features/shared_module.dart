import 'package:hodan_hospital/core/config/di/dependency_injection.dart';
import 'package:hodan_hospital/features/shared/data/repositories/shared_repository_impl.dart';
import 'package:hodan_hospital/features/shared/domain/repositories/shared_repository.dart';
import 'package:hodan_hospital/features/shared/domain/usecases/generate_pdf_use_case.dart';
import 'package:injectable/injectable.dart';

import '../../../../features/shared/data/datasources/remote/shared_remote_datasource.dart';
import '../../../../features/shared/domain/usecases/download_pdf_use_case.dart';
import '../../../../features/shared/domain/usecases/send_sms_usecase.dart';
import '../../../../features/shared/presentation/blocs or cubits/shared bloc/shared_bloc.dart';

@module
abstract class SharedModule {
  /// Register Data Sources (Transient)
  @injectable
  SharedRemoteDatasource get sharedRemoteDataSource =>
      SharedRemoteDatasourceImpl(
        dioApiClient: sl(),
        httpErrorHandler: sl(),
        flutterSecureStorageServices: sl(),
      );

  // @injectable
  // SharedLocalDataSource get SharedLocalDataSource => SharedLocalDataSourceImpl(
  //       SharedDatabaseManager: sl(),
  //     );

  /// Register Database Manager (Singleton)
  // @lazySingleton
  // SharedDatabaseManager get SharedDatabaseManager => SharedDatabaseManagerImpl(
  //       databaseErrorHandler: sl(),
  //     );

  /// Register Repository (Singleton)
  @lazySingleton
  SharedRepository get sharedRepository => SharedRepositoryImpl(
        sharedRemoteDatasource: sl(),
      );

  /// Register Use Cases (Singleton)
  @lazySingleton
  GeneratePdfUseCase get generatePdfUseCase =>
      GeneratePdfUseCase(repository: sl());

  @lazySingleton
  DownloadPdfUseCase get downloadPdfUseCase =>
      DownloadPdfUseCase(repository: sl());

  @lazySingleton
  SendSMSUseCase get sendSMSUseCase => SendSMSUseCase(repository: sl());
    

  /// Register Bloc (Singleton)
  @lazySingleton
  SharedBloc get sharedBloc => SharedBloc(
        generatePdfUseCase: sl(),
        downloadPdfUseCase: sl(),
        sendSMSUseCase: sl(),
      );
}
