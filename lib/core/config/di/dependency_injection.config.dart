// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:flutter_secure_storage/flutter_secure_storage.dart' as _i558;
import 'package:get_it/get_it.dart' as _i174;
import 'package:hodan_hospital/core/config/di/core/core_module.dart' as _i604;
import 'package:hodan_hospital/core/config/di/core/external_module.dart'
    as _i993;
import 'package:hodan_hospital/core/config/di/features/appointment_module.dart'
    as _i535;
import 'package:hodan_hospital/core/config/di/features/authentication_module.dart'
    as _i420;
import 'package:hodan_hospital/core/config/di/features/doctor_module.dart'
    as _i544;
import 'package:hodan_hospital/core/config/di/features/order_module.dart'
    as _i897;
import 'package:hodan_hospital/core/config/di/features/result_module.dart'
    as _i273;
import 'package:hodan_hospital/core/config/di/features/shared_module.dart'
    as _i1000;
import 'package:hodan_hospital/core/config/di/features/user_module.dart'
    as _i561;
import 'package:hodan_hospital/core/database/database_manager.dart' as _i42;
import 'package:hodan_hospital/core/errors/database_error_handler.dart'
    as _i564;
import 'package:hodan_hospital/core/errors/http_error_handler.dart' as _i894;
import 'package:hodan_hospital/core/network/api_client/dio_api_client.dart'
    as _i332;
import 'package:hodan_hospital/core/network/connection_checker.dart' as _i651;
import 'package:hodan_hospital/core/services/flutter_secure_storage_services.dart'
    as _i917;
import 'package:hodan_hospital/core/services/sms_services.dart' as _i1008;
import 'package:hodan_hospital/features/appointment/data/datasources/remote/appointment_remote_data_source.dart'
    as _i565;
import 'package:hodan_hospital/features/appointment/domain/repositories/appointment_repository.dart'
    as _i705;
import 'package:hodan_hospital/features/appointment/domain/usecases/dowload_appointment_pdf_use_case.dart'
    as _i45;
import 'package:hodan_hospital/features/appointment/domain/usecases/get_appointment_pdf_use_case.dart'
    as _i123;
import 'package:hodan_hospital/features/appointment/domain/usecases/get_appointments_use_case.dart'
    as _i6;
import 'package:hodan_hospital/features/appointment/domain/usecases/make_appointment_use_case.dart'
    as _i1073;
import 'package:hodan_hospital/features/appointment/domain/usecases/proccess_appointment_use_case.dart'
    as _i937;
import 'package:hodan_hospital/features/appointment/presentation/bloc/appointment_bloc.dart'
    as _i1069;
import 'package:hodan_hospital/features/authentication/data/datasources/local/auth_local_data_source.dart'
    as _i365;
import 'package:hodan_hospital/features/authentication/data/datasources/remote/auth_remote_data_source.dart'
    as _i347;
import 'package:hodan_hospital/features/authentication/domain/repositories/auth_repository.dart'
    as _i78;
import 'package:hodan_hospital/features/authentication/domain/use_cases/can_register_usecase.dart'
    as _i920;
import 'package:hodan_hospital/features/authentication/domain/use_cases/check_user_authentication_use_case.dart'
    as _i254;
import 'package:hodan_hospital/features/authentication/domain/use_cases/complete_onboarding_use_case.dart'
    as _i609;
import 'package:hodan_hospital/features/authentication/domain/use_cases/login_use_case.dart'
    as _i342;
import 'package:hodan_hospital/features/authentication/domain/use_cases/logout_use_case.dart'
    as _i954;
import 'package:hodan_hospital/features/authentication/domain/use_cases/register_use_case.dart'
    as _i190;
import 'package:hodan_hospital/features/authentication/domain/use_cases/resend_otp_usecase.dart'
    as _i296;
import 'package:hodan_hospital/features/authentication/domain/use_cases/verify_otp_use_case.dart'
    as _i62;
import 'package:hodan_hospital/features/authentication/presentation/bloc/authentication_bloc.dart'
    as _i900;
import 'package:hodan_hospital/features/doctor/data/datasources/remote/doctor_remote_data_source.dart'
    as _i128;
import 'package:hodan_hospital/features/doctor/domain/repositories/doctor_repository.dart'
    as _i924;
import 'package:hodan_hospital/features/doctor/domain/usecases/get_banners_use_case.dart'
    as _i760;
import 'package:hodan_hospital/features/doctor/domain/usecases/get_doctor_departments_use_case.dart'
    as _i256;
import 'package:hodan_hospital/features/doctor/domain/usecases/get_doctors_by_department_use_case.dart'
    as _i980;
import 'package:hodan_hospital/features/doctor/domain/usecases/get_doctors_use_case.dart'
    as _i174;
import 'package:hodan_hospital/features/doctor/domain/usecases/get_recent_search_use_case.dart'
    as _i876;
import 'package:hodan_hospital/features/doctor/domain/usecases/save_recent_search_use_case.dart'
    as _i287;
import 'package:hodan_hospital/features/doctor/domain/usecases/search_doctor_use_case.dart'
    as _i791;
import 'package:hodan_hospital/features/doctor/presentation/bloc/doctor_bloc.dart'
    as _i238;
import 'package:hodan_hospital/features/Order/data/datasources/remote/order_remote_data_source.dart'
    as _i691;
import 'package:hodan_hospital/features/Order/domain/repositories/order_repository.dart'
    as _i490;
import 'package:hodan_hospital/features/Order/domain/usecases/get_orders_use_case.dart'
    as _i463;
import 'package:hodan_hospital/features/Order/domain/usecases/process_order_use_case.dart'
    as _i883;
import 'package:hodan_hospital/features/Order/presentation/bloc/order_bloc.dart'
    as _i693;
import 'package:hodan_hospital/features/Result/data/datasources/remote/result_remote_data_source.dart'
    as _i99;
import 'package:hodan_hospital/features/Result/domain/repositories/result_repository.dart'
    as _i432;
import 'package:hodan_hospital/features/Result/domain/usecases/generate_lab_result_pdf_usecase.dart'
    as _i836;
import 'package:hodan_hospital/features/Result/domain/usecases/get_lab_results_use_case.dart'
    as _i982;
import 'package:hodan_hospital/features/Result/presentation/bloc/result_bloc.dart'
    as _i243;
import 'package:hodan_hospital/features/shared/data/datasources/remote/shared_remote_datasource.dart'
    as _i926;
import 'package:hodan_hospital/features/shared/domain/repositories/shared_repository.dart'
    as _i603;
import 'package:hodan_hospital/features/shared/domain/usecases/download_pdf_use_case.dart'
    as _i644;
import 'package:hodan_hospital/features/shared/domain/usecases/generate_pdf_use_case.dart'
    as _i599;
import 'package:hodan_hospital/features/shared/domain/usecases/send_sms_usecase.dart'
    as _i833;
import 'package:hodan_hospital/features/shared/presentation/blocs%20or%20cubits/bottom%20nav%20cubit/bottom_nav_cubit.dart'
    as _i85;
import 'package:hodan_hospital/features/shared/presentation/blocs%20or%20cubits/dialog%20cubit/dialog_cubit.dart'
    as _i709;
import 'package:hodan_hospital/features/shared/presentation/blocs%20or%20cubits/shared%20bloc/shared_bloc.dart'
    as _i559;
import 'package:hodan_hospital/features/shared/presentation/blocs%20or%20cubits/theme-cubit/theme_cubit.dart'
    as _i396;
import 'package:hodan_hospital/features/shared/presentation/blocs%20or%20cubits/theme-cubit/theme_storage.dart'
    as _i449;
import 'package:hodan_hospital/features/user/data/datasources/local/user_database_manager.dart'
    as _i927;
import 'package:hodan_hospital/features/user/data/datasources/local/user_local_data_source.dart'
    as _i29;
import 'package:hodan_hospital/features/user/data/datasources/remote/user_remote_data_source.dart'
    as _i692;
import 'package:hodan_hospital/features/user/domain/repositories/user_reposiory.dart'
    as _i922;
import 'package:hodan_hospital/features/user/domain/usecases/get_current_user_use_case.dart'
    as _i492;
import 'package:hodan_hospital/features/user/domain/usecases/get_districts_use_case.dart'
    as _i858;
import 'package:hodan_hospital/features/user/domain/usecases/get_users_by_phone_number_use_case.dart'
    as _i644;
import 'package:hodan_hospital/features/user/domain/usecases/register_new_patient_use_case.dart'
    as _i809;
import 'package:hodan_hospital/features/user/domain/usecases/send_feedback_usecase.dart'
    as _i1002;
import 'package:hodan_hospital/features/user/presentation/bloc/user_bloc.dart'
    as _i598;
import 'package:injectable/injectable.dart' as _i526;
import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart'
    as _i161;

extension GetItInjectableX on _i174.GetIt {
// initializes the registration of main-scope dependencies inside of GetIt
  Future<_i174.GetIt> init({
    String? environment,
    _i526.EnvironmentFilter? environmentFilter,
  }) async {
    final gh = _i526.GetItHelper(
      this,
      environment,
      environmentFilter,
    );
    final resultModule = _$ResultModule();
    final orderModule = _$OrderModule();
    final appointmentModule = _$AppointmentModule();
    final sharedModule = _$SharedModule();
    final doctorModule = _$DoctorModule();
    final coreModule = _$CoreModule();
    final externalModule = _$ExternalModule();
    final userModule = _$UserModule();
    final authenticationModule = _$AuthenticationModule();
    gh.factory<_i99.ResultRemoteDataSource>(
        () => resultModule.resultRemoteDataSource);
    gh.factory<_i691.OrderRemoteDataSource>(
        () => orderModule.orderRemoteDataSource);
    gh.factory<_i565.AppointmentRemoteDataSource>(
        () => appointmentModule.appointmentRemoteDataSource);
    gh.factory<_i926.SharedRemoteDatasource>(
        () => sharedModule.sharedRemoteDataSource);
    gh.factory<_i128.DoctorRemoteDataSource>(
        () => doctorModule.doctorRemoteDataSource);
    gh.singleton<_i651.ConnectionChecker>(
        () => coreModule.provideConnectionChecker());
    gh.lazySingleton<_i558.FlutterSecureStorage>(
        () => externalModule.secureStorage);
    gh.lazySingleton<_i161.InternetConnection>(
        () => externalModule.internetConnection);
    gh.lazySingleton<_i332.DioApiClient>(() => coreModule.dioApiClient);
    gh.lazySingleton<_i1008.SmsServices>(() => coreModule.smsServices);
    gh.lazySingleton<_i449.ThemeStorage>(() => coreModule.themeStorage);
    gh.lazySingleton<_i709.DialogCubit>(() => coreModule.dialogCubit);
    gh.lazySingleton<_i85.BottomNavCubit>(() => coreModule.bottomNavCubit);
    gh.lazySingleton<_i396.ThemeCubit>(() => coreModule.themeCubit);
    await gh.lazySingletonAsync<_i42.DatabaseManager>(
      () => coreModule.provideDatabaseManager(),
      preResolve: true,
    );
    gh.lazySingleton<_i917.FlutterSecureStorageServices>(
        () => coreModule.provideSecureStorageServices());
    gh.lazySingleton<_i432.ResultRepository>(
        () => resultModule.resultRepository);
    gh.lazySingleton<_i982.GetLabResultsUseCase>(
        () => resultModule.getLabResultsUseCase);
    gh.lazySingleton<_i836.GenerateLabResultPdfUsecase>(
        () => resultModule.generateLabResultPdfUsecase);
    gh.lazySingleton<_i243.ResultBloc>(() => resultModule.resultBloc);
    gh.lazySingleton<_i490.OrderRepository>(() => orderModule.orderRepository);
    gh.lazySingleton<_i463.GetOrdersUseCase>(
        () => orderModule.getOrdersUseCase);
    gh.lazySingleton<_i883.ProcessOrderUseCase>(
        () => orderModule.processOrderUseCase);
    gh.lazySingleton<_i693.OrderBloc>(() => orderModule.orderBloc);
    gh.lazySingleton<_i705.AppointmentRepository>(
        () => appointmentModule.appointmentRepository);
    gh.lazySingleton<_i6.GetAppointmentsUseCase>(
        () => appointmentModule.getAppointmentsUseCase);
    gh.lazySingleton<_i1073.MakeAppointmentUseCase>(
        () => appointmentModule.makeAppointmentsUseCase);
    gh.lazySingleton<_i937.ProcessAppointmentUseCase>(
        () => appointmentModule.processAppointmentUseCase);
    gh.lazySingleton<_i45.DowloadAppointmentPDFUseCase>(
        () => appointmentModule.dowloadAppointmentPDFUseCase);
    gh.lazySingleton<_i123.GetAppointmentPDFUseCase>(
        () => appointmentModule.getAppointmentPDFUseCase);
    gh.lazySingleton<_i1069.AppointmentBloc>(
        () => appointmentModule.appointmentBloc);
    gh.lazySingleton<_i603.SharedRepository>(
        () => sharedModule.sharedRepository);
    gh.lazySingleton<_i599.GeneratePdfUseCase>(
        () => sharedModule.generatePdfUseCase);
    gh.lazySingleton<_i644.DownloadPdfUseCase>(
        () => sharedModule.downloadPdfUseCase);
    gh.lazySingleton<_i833.SendSMSUseCase>(() => sharedModule.sendSMSUseCase);
    gh.lazySingleton<_i559.SharedBloc>(() => sharedModule.sharedBloc);
    gh.lazySingleton<_i924.DoctorRepository>(
        () => doctorModule.doctorRepository);
    gh.lazySingleton<_i174.GetDoctorsUseCase>(
        () => doctorModule.getDoctorsUseCase);
    gh.lazySingleton<_i791.SearchDoctorUseCase>(
        () => doctorModule.searchDoctorsUseCase);
    gh.lazySingleton<_i760.GetBannersUseCase>(
        () => doctorModule.getBannersUseCase);
    gh.lazySingleton<_i876.GetRecentSearchUseCase>(
        () => doctorModule.getRecentSearchUseCase);
    gh.lazySingleton<_i287.SaveRecentSearchUseCase>(
        () => doctorModule.saveRecentSearchUseCase);
    gh.lazySingleton<_i256.GetDoctorDepartmentsUseCase>(
        () => doctorModule.getDoctorDepartmentsUseCase);
    gh.lazySingleton<_i980.GetDoctorsByDepartmentUseCase>(
        () => doctorModule.getDoctorsByDepartmentUseCase);
    gh.lazySingleton<_i238.DoctorBloc>(() => doctorModule.doctorBloc);
    gh.lazySingleton<_i894.HttpErrorHandler>(() =>
        coreModule.provideHttpErrorHandler(gh<_i651.ConnectionChecker>()));
    gh.lazySingleton<_i564.DatabaseErrorHandler>(() =>
        coreModule.provideDatabaseErrorHandler(gh<_i42.DatabaseManager>()));
    gh.lazySingleton<_i927.UserDatabaseManager>(() => userModule
        .provideUserDatabaseManager(gh<_i564.DatabaseErrorHandler>()));
    gh.factory<_i365.AuthLocalDataSource>(() => authenticationModule
        .provideAuthLocalDataSource(gh<_i917.FlutterSecureStorageServices>()));
    gh.factory<_i692.UserRemoteDataSource>(
        () => userModule.provideUserRemoteDataSource(
              gh<_i332.DioApiClient>(),
              gh<_i894.HttpErrorHandler>(),
              gh<_i917.FlutterSecureStorageServices>(),
            ));
    gh.factory<_i347.AuthRemoteDataSource>(
        () => authenticationModule.provideAuthRemoteDataSource(
              gh<_i332.DioApiClient>(),
              gh<_i894.HttpErrorHandler>(),
            ));
    gh.factory<_i29.UserLocalDataSource>(
        () => userModule.provideUserLocalDataSource(
              gh<_i917.FlutterSecureStorageServices>(),
              gh<_i927.UserDatabaseManager>(),
            ));
    gh.lazySingleton<_i922.UserRepository>(
        () => userModule.provideUserRepository(
              gh<_i692.UserRemoteDataSource>(),
              gh<_i29.UserLocalDataSource>(),
            ));
    gh.lazySingleton<_i78.AuthRepository>(
        () => authenticationModule.provideAuthRepository(
              gh<_i347.AuthRemoteDataSource>(),
              gh<_i365.AuthLocalDataSource>(),
              gh<_i1008.SmsServices>(),
            ));
    gh.lazySingleton<_i644.GetUsersByPhoneNumberUseCase>(() => userModule
        .provideGetUsersByPhoneNumberUseCase(gh<_i922.UserRepository>()));
    gh.lazySingleton<_i492.GetCurrentUserUseCase>(() =>
        userModule.provideGetCurrentUserUseCase(gh<_i922.UserRepository>()));
    gh.lazySingleton<_i809.RegisterNewPatientUseCase>(() => userModule
        .provideRegisterNewPatientUseCase(gh<_i922.UserRepository>()));
    gh.lazySingleton<_i858.GetDistrictsUseCase>(() =>
        userModule.provideGetDistrictsUseCase(gh<_i922.UserRepository>()));
    gh.lazySingleton<_i1002.SendFeedbackUseCase>(() =>
        userModule.provideSendFeedbackUseCase(gh<_i922.UserRepository>()));
    gh.lazySingleton<_i342.LoginUseCase>(() =>
        authenticationModule.provideLoginUseCase(gh<_i78.AuthRepository>()));
    gh.lazySingleton<_i190.RegisterUseCase>(() =>
        authenticationModule.provideRegisterUseCase(gh<_i78.AuthRepository>()));
    gh.lazySingleton<_i954.LogoutUseCase>(() =>
        authenticationModule.provideLogoutUseCase(gh<_i78.AuthRepository>()));
    gh.lazySingleton<_i254.CheckUserAuthenticationUseCase>(() =>
        authenticationModule
            .provideCheckUserAuthenticationUseCase(gh<_i78.AuthRepository>()));
    gh.lazySingleton<_i609.CompleteOnboardingUseCase>(() => authenticationModule
        .provideCompleteOnboardingUseCase(gh<_i78.AuthRepository>()));
    gh.lazySingleton<_i296.ResendOtpUseCase>(() =>
        authenticationModule.provideSendOtpUseCase(gh<_i78.AuthRepository>()));
    gh.lazySingleton<_i62.VerifyOtpUseCase>(() => authenticationModule
        .provideVerifyOtpUseCase(gh<_i78.AuthRepository>()));
    gh.lazySingleton<_i920.CanRegisterUsecase>(() => authenticationModule
        .provideCanRegisterUsecase(gh<_i78.AuthRepository>()));
    gh.lazySingleton<_i598.UserBloc>(() => userModule.provideUserBloc(
          gh<_i644.GetUsersByPhoneNumberUseCase>(),
          gh<_i492.GetCurrentUserUseCase>(),
          gh<_i809.RegisterNewPatientUseCase>(),
          gh<_i858.GetDistrictsUseCase>(),
          gh<_i1002.SendFeedbackUseCase>(),
        ));
    gh.lazySingleton<_i900.AuthenticationBloc>(
        () => authenticationModule.provideAuthenticationBloc(
              gh<_i342.LoginUseCase>(),
              gh<_i190.RegisterUseCase>(),
              gh<_i954.LogoutUseCase>(),
              gh<_i254.CheckUserAuthenticationUseCase>(),
              gh<_i609.CompleteOnboardingUseCase>(),
              gh<_i296.ResendOtpUseCase>(),
              gh<_i62.VerifyOtpUseCase>(),
              gh<_i920.CanRegisterUsecase>(),
            ));
    return this;
  }
}

class _$ResultModule extends _i273.ResultModule {}

class _$OrderModule extends _i897.OrderModule {}

class _$AppointmentModule extends _i535.AppointmentModule {}

class _$SharedModule extends _i1000.SharedModule {}

class _$DoctorModule extends _i544.DoctorModule {}

class _$CoreModule extends _i604.CoreModule {}

class _$ExternalModule extends _i993.ExternalModule {}

class _$UserModule extends _i561.UserModule {}

class _$AuthenticationModule extends _i420.AuthenticationModule {}
