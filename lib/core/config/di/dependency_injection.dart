import 'package:get_it/get_it.dart';
import 'package:hodan_hospital/core/config/di/dependency_injection.config.dart';
import 'package:hodan_hospital/core/config/logger/app_logger.dart';
import 'package:injectable/injectable.dart';

/// dart run build_runner build --delete-conflicting-outputs

final GetIt sl = GetIt.instance;

@InjectableInit()
Future<void> setupServiceLocator() async {
  try {
    AppLogger().info('Initializing dependencies...');

    await sl.init();

    AppLogger().info('Dependencies initialized successfully!');
  } catch (e, stackTrace) {
    AppLogger().error(
      'Error initializing service locator',
      error: e,
      stackTrace: stackTrace,
    );
  }
}
