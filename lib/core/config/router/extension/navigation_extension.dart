// import 'package:flutter/material.dart';
// import 'package:go_router/go_router.dart';
// import 'package:hodan_hospital/core/config/router/paths/route_paths.dart';

// extension NavigationExtension on BuildContext {
//   /// 🔹 Push a new page (standard push)
//   void pushRoute(RoutePaths route,
//       {Map<String, String>? params, Object? extra}) {
//     String path = params != null ? route.withParams(params) : route.path;
//     push(path, extra: extra);
//   }

//   /// 🔹 Push a new named route
//   void pushNamedRoute(RoutePaths route,
//       {Map<String, String>? params, Object? extra}) {
//     pushNamed(route.name, pathParameters: params ?? {}, extra: extra);
//   }

//   /// 🔹 Push a new page and replace the current one
//   void pushReplacementRoute(RoutePaths route,
//       {Map<String, String>? params, Object? extra}) {
//     String path = params != null ? route.withParams(params) : route.path;
//     pushReplacement(path, extra: extra);
//   }

//   /// 🔹 Push a new named route and replace the current one
//   void pushReplacementNamedRoute(RoutePaths route,
//       {Map<String, String>? params, Object? extra}) {
//     pushReplacementNamed(route.name,
//         pathParameters: params ?? {}, extra: extra);
//   }

//   /// 🔹 Go to a new page (replaces the entire stack)
//   void goRoute(RoutePaths route, {Map<String, String>? params, Object? extra}) {
//     String path = params != null ? route.withParams(params) : route.path;
//     go(path, extra: extra);
//   }

//   /// 🔹 Go to a new named route (replaces the entire stack)
//   void goNamedRoute(RoutePaths route,
//       {Map<String, String>? params, Object? extra}) {
//     goNamed(route.name, pathParameters: params ?? {}, extra: extra);
//   }

//   /// 🔹 Replace the current route
//   void replaceRoute(RoutePaths route,
//       {Map<String, String>? params, Object? extra}) {
//     String path = params != null ? route.withParams(params) : route.path;
//     replace(path, extra: extra);
//   }

//   /// 🔹 Replace the current route with a named route
//   void replaceNamedRoute(RoutePaths route,
//       {Map<String, String>? params, Object? extra}) {
//     replaceNamed(route.name, pathParameters: params ?? {}, extra: extra);
//   }

//   /// 🔹 Pop the current page
//   void popRoute<T extends Object?>([T? result]) {
//     if (canPop()) {
//       pop(result);
//     }
//   }
// }

import 'package:flutter/material.dart';

extension NavigationExtension on BuildContext {
  /// 🔹 Push a new page (standard push)
  void pushRoute(Widget page) {
    Navigator.push(
      this,
      MaterialPageRoute(builder: (context) => page),
    );
  }

  /// 🔹 Push a new page and replace the current one
  void pushReplacementRoute(Widget page) {
    Navigator.pushReplacement(
      this,
      MaterialPageRoute(builder: (context) => page),
    );
  }

  /// 🔹 Push a new page and clear all previous routes
  void pushAndRemoveUntilRoute(Widget page) {
    Navigator.pushAndRemoveUntil(
      this,
      MaterialPageRoute(builder: (context) => page),
      (route) => false,
    );
  }

  /// 🔹 Pop the current page
  void popRoute<T extends Object?>([T? result]) {
    if (Navigator.canPop(this)) {
      Navigator.pop(this, result);
    }
  }
}
